import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import PartsMatrixModel from './PartMatrixModel';
import LaborGrid from './LaborGrid';
import { withRouter } from 'react-router-dom';
import loadash from 'lodash';
import {
  Typography,
  LinearProgress,
  Box,
  Paper,
  Tooltip,
  Link,
  FormControl,
  FormHelperText,
  TextField,
  IconButton,
  InputLabel,
  Select,
  MenuItem,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress
} from '@material-ui/core';
import ClearIcon from '@material-ui/icons/Clear';
import { Alert } from '@material-ui/lab';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
import AddIcon from '@material-ui/icons/Add';
import Radio from '@material-ui/core/Radio';
import RadioGroup from '@material-ui/core/RadioGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import Checkbox from '@material-ui/core/Checkbox';
import Autocomplete from '@material-ui/lab/Autocomplete';
import InputAdornment from '@material-ui/core/InputAdornment';
import SwapHorizIcon from '@material-ui/icons/SwapHoriz';
import SearchIcon from '@material-ui/icons/Search';
import FixedRateRenderer from './FixedRateRenderer';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';

import { AgGridReact } from '@ag-grid-community/react';

import 'src/grid.css';
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import {
  getFleetCustomerNames,
  getFleetListItems,
  insertFleetDetails,
  insertFleetNameToList,
  getFleetMasterData,
  getFleetPayTypeNames
} from 'src/utils/hasuraServices';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import { withStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { Button } from '@material-ui/core';
import RestoreIcon from '@material-ui/icons/Restore';
import ExportIcon from '@material-ui/icons/GetApp';
import { processCells } from 'src/components/ViewGraphDetailsAction';
import Datepicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import 'react-datepicker/dist/react-datepicker-cssmodules.css';
import moment from 'moment';

import GridCheckboxRenderer from './GridCheckboxRenderer.js';

import { withKeycloak } from '@react-keycloak/web';

import { ReactSession } from 'react-client-session';
var lodash = require('lodash');

var Dealer = process.env.REACT_APP_DEALER;

const top100Films = [
  { title: 'The Shawshank Redemption', year: 1994 },
  { title: 'The Godfather', year: 1972 },
  { title: 'The Godfather: Part II', year: 1974 },
  { title: 'The Dark Knight', year: 2008 },
  { title: '12 Angry Men', year: 1957 },
  { title: "Schindler's List", year: 1993 },
  { title: 'Pulp Fiction', year: 1994 }
];

class FleetAccounts extends React.Component {
  componentWillMount() {
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate(prevProps, prevState) {
    if (
      prevState.inputValue !== this.state.inputValue ||
      prevState.searchStatus !== this.state.searchStatus ||
      prevState.selectedCustomer !== this.state.selectedCustomer ||
      prevState.selectedPayTypeOption !== this.state.selectedPayTypeOption ||
      prevState.selectedOpcodeOption !== this.state.selectedOpcodeOption
    ) {
      this.updateFilteredOptions();
    }

    if (
      prevState.inputValueSelected !== this.state.inputValueSelected ||
      prevState.searchStatus !== this.state.searchStatus ||
      prevState.selectedFleet !== this.state.selectedFleet ||
      prevState.selectedPayTypeOptionType !==
        this.state.selectedPayTypeOptionType ||
      prevState.selectedOpcodeOptionType !== this.state.selectedOpcodeOptionType
    ) {
      this.updateFilteredOptionsSelected();
    }

    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ isLoading: true });
        this.setState({
          store: localStorage.getItem('selectedStoreId'),
          storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        });
        this.setState({
          selectedFleet: 'All',
          selectedPayTypeOptionType: 'All',
          selectedOpcodeOptionType: 'All'
        });
        this.resetFleet();

        this.getAgGridData(
          'All',
          JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        );
        this.fetchFleetCustomerNames(
          JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        );
        this.fetchFleetPayTypeNames(
          'paytype_fleet',
          JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        );
        this.fetchFleetPayTypeNames(
          'paytype_fleet_exists',
          JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        );
        this.fetchFleetPayTypeNames(
          'cust_fleet_exists',
          JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        );
        this.fetchFleetOpcodeNames(
          'opcode_fleet',
          JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        );
        this.fetchFleetOpcodeNames(
          'opcode_fleet_exists',
          JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        );
      }
    }
  }
  componentDidMount() {
    this.updateFilteredOptions();
    this.updateFilteredOptionsSelected();

    // this.updateFilteredOptions();

    // this.updateFilteredOptionsSelected();

    // to get list of opcode names
    this.fetchFleetOpcodeNames('opcode_fleet');

    // to get list of already exist opcode names
    this.fetchFleetOpcodeNames('opcode_fleet_exists');

    // to get list of customer

    this.fetchFleetCustomerNames();

    // to get list of paytype names
    this.fetchFleetPayTypeNames('paytype_fleet');

    // to get list of already exist paytype names
    this.fetchFleetPayTypeNames('paytype_fleet_exists');

    // to get list of already exist customer names
    this.fetchFleetPayTypeNames('cust_fleet_exists');

    getFleetMasterData('fleet_name', this.state.storeId, result => {
      let data = result;

      if (
        result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
          .statelessCcPhysicalRwKpiFleetMasters.length > 0
      ) {
        this.setState({
          isFleetLoaded: true
        });

        let fleetNames = [{ customerName: 'All' }];
        result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster.statelessCcPhysicalRwKpiFleetMasters.map(
          (val, i) => {
            fleetNames.push({
              customerName: val.fleetName
            });
          }
        );
        this.setState({
          selectedFleetNames: fleetNames
        });
        //console.log('ffff', fleetNames);
        // this.setState({
        //   customerNames: result.data.statelessCcAggregateGetCustomers.nodes
        // });
      }
    });
    getFleetMasterData('part_source', this.state.storeId, result => {
      let data = result;

      if (
        result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
          .statelessCcPhysicalRwKpiFleetMasters.length > 0
      ) {
        this.setState({
          isFleetLoaded: true
        });

        let partsSource = [];
        result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster.statelessCcPhysicalRwKpiFleetMasters.map(
          (val, i) => {
            partsSource.push(val.partSource);
          }
        );

        this.setState({
          partsSourceArray: partsSource
        });
        this.setState({
          partsSource: partsSource[0]
        });
        //console.log('ffff', partsSource[0]);
        // this.setState({
        //   customerNames: result.data.statelessCcAggregateGetCustomers.nodes
        // });
      }
    });
  }

  // Define a function to fetch fleet opcode names
  fetchFleetOpcodeNames = (variable, selectedStore) => {
    getFleetPayTypeNames(
      variable,
      selectedStore ? selectedStore : this.state.storeId,
      result => {
        let data = result;
        let fleetNames = [];
        if (
          variable == 'opcode_fleet_exists' ||
          variable == 'opcode_fleet_exists'
        ) {
          fleetNames = [{ opcodeName: 'All' }];
        } else {
          fleetNames = [{ customerName: 'All' }];
        }

        if (
          result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
            .statelessCcPhysicalRwKpiFleetMasters.length > 0
        ) {
          this.setState({
            isFleetLoaded: true
          });

          result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster.statelessCcPhysicalRwKpiFleetMasters.map(
            (val, i) => {
              if (
                variable == 'opcode_fleet' ||
                variable == 'opcode_fleet_exists'
              ) {
                fleetNames.push({
                  opcodeName: val.fleetName
                });
              } else {
                fleetNames.push({
                  customerName: val.fleetName
                });
              }
            }
          );
          if (variable == 'opcode_fleet') {
            this.setState({
              selectedOpcodeNames: fleetNames
            });
          } else if (variable == 'opcode_fleet_exists') {
            this.setState({
              selectedOpcodeNamesExist: fleetNames
            });
          } else {
            this.setState({
              selectedCustomerNamesExist: fleetNames
            });
          }

          const selectedOpcodes = loadash.map(
            this.state.selectedOpcodeNamesExist,
            'opcodeName'
          );

          const notSelectedOpcode = loadash.map(
            this.state.selectedOpcodeNames,
            'opcodeName'
          );

          const filteredNotSelectedOpcode = loadash.difference(
            notSelectedOpcode,
            selectedOpcodes
          );

          const formattedFilteredNames = loadash.map(
            filteredNotSelectedOpcode,
            name => ({ opcodeName: name })
          );

          this.setState({
            selectedOpcodeNames: formattedFilteredNames
          });
          this.updateFilteredOptions();
          this.updateFilteredOptionsSelected();
        } else {
          if (variable == 'opcode_fleet') {
            this.setState({
              selectedOpcodeNames: fleetNames
            });
          } else if (variable == 'opcode_fleet_exists') {
            this.setState({
              selectedOpcodeNamesExist: fleetNames
            });
          } else {
            this.setState({
              selectedCustomerNamesExist: fleetNames
            });
          }
        }
        this.updateFilteredOptions();
        this.updateFilteredOptionsSelected();
      }
    );
  };
  // Define a function to fetch fleet pay type names
  fetchFleetPayTypeNames = (variable, selectedStore) => {
    getFleetPayTypeNames(
      variable,
      selectedStore ? selectedStore : this.state.storeId,
      result => {
        let data = result;
        let fleetNames = [];
        if (
          variable == 'paytype_fleet_exists' ||
          variable == 'paytype_fleet_exists'
        ) {
          fleetNames = [{ payTypeName: 'All' }];
        } else {
          fleetNames = [{ customerName: 'All' }];
        }

        if (
          result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
            .statelessCcPhysicalRwKpiFleetMasters.length > 0
        ) {
          this.setState({
            isFleetLoaded: true
          });

          result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster.statelessCcPhysicalRwKpiFleetMasters.map(
            (val, i) => {
              if (
                variable == 'paytype_fleet' ||
                variable == 'paytype_fleet_exists'
              ) {
                fleetNames.push({
                  payTypeName: val.fleetName
                });
              } else {
                fleetNames.push({
                  customerName: val.fleetName
                });
              }
            }
          );
          if (variable == 'paytype_fleet') {
            this.setState({
              selectedPayTypeNames: fleetNames
            });
          } else if (variable == 'paytype_fleet_exists') {
            this.setState({
              selectedPayTypeNamesExist: fleetNames
            });
          } else {
            this.setState({
              selectedCustomerNamesExist: fleetNames
            });
          }

          const selectedPaytypes = loadash.map(
            this.state.selectedPayTypeNamesExist,
            'payTypeName'
          );

          const notSelectedPaytype = loadash.map(
            this.state.selectedPayTypeNames,
            'payTypeName'
          );

          const filteredNotSelectedPaytype = loadash.difference(
            notSelectedPaytype,
            selectedPaytypes
          );

          const formattedFilteredNames = loadash.map(
            filteredNotSelectedPaytype,
            name => ({ payTypeName: name })
          );

          this.setState({
            selectedPayTypeNames: formattedFilteredNames
          });
        } else {
          if (variable == 'paytype_fleet') {
            this.setState({
              selectedPayTypeNames: fleetNames
            });
          } else if (variable == 'paytype_fleet_exists') {
            this.setState({
              selectedPayTypeNamesExist: fleetNames
            });
          } else {
            this.setState({
              selectedCustomerNamesExist: fleetNames
            });
          }
        }
        this.updateFilteredOptions();
        this.updateFilteredOptionsSelected();
      }
    );
  };

  fetchFleetCustomerNames = selectedStore => {
    getFleetMasterData(
      'cust_fleet',
      selectedStore ? selectedStore : this.state.storeId,
      result => {
        let data = result;

        if (
          result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
            .statelessCcPhysicalRwKpiFleetMasters.length > 0
        ) {
          let fleetNames =
            result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
              .statelessCcPhysicalRwKpiFleetMasters;

          // Sort fleetNames alphabetically by customerName using Lodash
          fleetNames = loadash.sortBy(fleetNames, 'fleetName');

          this.setState({
            isFleetLoaded: true,
            customerNames: fleetNames,
            isFleetNamesLoaded: true
          });

          const selectedNames = loadash.map(
            this.state.selectedCustomerNamesExist,
            'customerName'
          );

          const notSelectedNames = loadash.map(
            this.state.customerNames,
            'fleetName'
          );

          const filteredNotSelectedNames = loadash.difference(
            notSelectedNames,
            selectedNames
          );

          const sortedFilteredNotSelectedNames = filteredNotSelectedNames.sort(
            this.caseInsensitiveSort
          );

          const formattedFilteredNames = loadash.map(
            sortedFilteredNotSelectedNames,
            name => ({ customerName: name })
          );

          // const filteredCustomerNames = this.state.customerNames.filter(
          //   item =>
          //     !selectedNames.some(
          //       selectedName => selectedName === item.customerName
          //     )
          // );

          // Filtering customerNames to remove those present in selectedNames

          this.setState({
            customerNames: formattedFilteredNames
          });
          this.updateFilteredOptions();
          this.updateFilteredOptionsSelected();
        } else {
          this.setState({
            isFleetLoaded: true,
            isFleetNamesLoaded: true
          });
        }
      }
    );

    // getFleetCustomerNames(this.state.storeId, result => {
    //   const nodes = result.data.statelessCcAggregateGetCustomers.nodes;

    //   if (nodes && nodes.length > 0) {
    //     let fleetNames = nodes;

    //     // Sort fleetNames alphabetically by customerName using Lodash
    //     fleetNames = loadash.sortBy(fleetNames, 'customerName');

    //     this.setState({
    //       isFleetLoaded: true,
    //       customerNames: fleetNames,
    //       isFleetNamesLoaded: true
    //     });

    //     console.log('ccc===', fleetNames);
    //     // console.log('1111111111' + JSON.stringify(this.state.customerNames));
    //     // console.log(
    //     //   '1111111111' + JSON.stringify(this.state.selectedCustomerNamesExist)
    //     // );

    //     // Extracting customer names from selectedCustomerNamesExist for quick lookup
    //     const selectedNames = loadash.map(
    //       this.state.selectedCustomerNamesExist,
    //       'customerName'
    //     );

    //     const notSelectedNames = loadash.map(
    //       this.state.customerNames,
    //       'customerName'
    //     );

    //     const filteredNotSelectedNames = loadash.difference(
    //       notSelectedNames,
    //       selectedNames
    //     );

    //     const sortedFilteredNotSelectedNames = filteredNotSelectedNames.sort(
    //       this.caseInsensitiveSort
    //     );

    //     const formattedFilteredNames = loadash.map(
    //       sortedFilteredNotSelectedNames,
    //       name => ({ customerName: name })
    //     );

    //     // const filteredCustomerNames = this.state.customerNames.filter(
    //     //   item =>
    //     //     !selectedNames.some(
    //     //       selectedName => selectedName === item.customerName
    //     //     )
    //     // );

    //     // Filtering customerNames to remove those present in selectedNames

    //     this.setState({
    //       customerNames: formattedFilteredNames
    //     });
    //   }
    // });
  };

  constructor(props) {
    super(props);
    this.handleCallback = this.handleCallback.bind(this);
    let opcodeSelected =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.gridType
        ? this.props.history.location.state.gridType
        : this.props.selectedOpcode;
    let payTypeSelected =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.gridType
        ? this.props.history.location.state.gridType
        : this.props.selectedPayType;
    let gridDoorRate =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.gridDoorRate
        ? this.props.history.location.state.gridDoorRate
        : '';

    let selectedGrid =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.selectedGrid == ''
        ? this.props.history.location.state.selectedGrid
        : 1;
    if (payTypeSelected == 'C') {
      payTypeSelected = 'Customer';
    } else if (payTypeSelected == 'I') {
      payTypeSelected = 'Internal';
    }
    let showAllJobs =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.showAllJobs
        ? this.props.history.location.state.showAllJobs
        : false;
    let gridRateValue =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.gridType &&
      (this.props.history.location.state.gridType ==
        'Fixed Rate - Fleet Pay Type' ||
        this.props.history.location.state.gridType == 'paytype')
        ? 'paytype'
        : this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.gridType &&
          (this.props.history.location.state.gridType ==
            'Fixed Rate - Fleet Opcode' ||
            this.props.history.location.state.gridType ==
              'Matrix - Fleet Opcode' ||
            this.props.history.location.state.gridType == 'opcode')
        ? 'opcode'
        : 'customer';

    let fleetAccountSelectedCustomer =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.customerName
        ? this.props.history.location.state.customerName
        : '';
    let fleetAccountSelectedPayType =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.selectedPayType
        ? this.props.history.location.state.selectedPayType
        : '';
    let fleetAccountSelectedOpcode =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.selectedOpcode
        ? this.props.history.location.state.selectedOpcode
        : '';
    let fleetSelected = '';

    if (gridRateValue == 'customer') {
      this.setState({ inputValueSelected: fleetAccountSelectedCustomer });
      fleetSelected = fleetAccountSelectedCustomer;
    } else if (gridRateValue == 'paytype') {
      this.setState({ inputValueSelected: fleetAccountSelectedPayType });
      fleetSelected = fleetAccountSelectedPayType;
    } else {
      this.setState({ inputValueSelected: fleetAccountSelectedOpcode });
      fleetSelected = fleetAccountSelectedOpcode;
    }
    if (fleetSelected == 'All') {
      fleetSelected = '';
    }
    let storeId =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.storeId
        ? this.props.history.location.state.storeId
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const startEdit = this;
    const { keycloak } = this.props;
    this.textInput = React.createRef();
    class NumericCellEditor {
      init(params) {
        this.params = params;
        this.input = document.createElement('input');
        this.input.type = 'text';
        this.input.value = params.value;
        this.originalValue = params.value; // Store the original value

        // Apply styles
        this.input.style.width = '100px'; // Set the width of the input
        this.input.style.borderRadius = '4px'; // Rounded corners
        this.input.style.borderColor = ''; // Rounded corners

        // Add event listeners for validation and formatting
        this.input.addEventListener('keydown', this.onKeyDown.bind(this));
        this.input.addEventListener('input', this.onInput.bind(this));
      }

      onKeyDown(event) {
        const char = event.key;

        // Allow control keys (backspace, delete, arrows)
        if (
          event.key === 'Backspace' ||
          event.key === 'Delete' ||
          event.key === 'ArrowLeft' ||
          event.key === 'ArrowRight' ||
          event.key === 'Tab'
        ) {
          return;
        }

        // Get the current value including the new character
        const newValue = this.getNewValue(event);

        if (
          !/^\d{0,3}(\.\d{0,2})?$/.test(newValue) ||
          (char === '.' && this.input.value.includes('.'))
        ) {
          event.preventDefault();
        }
      }

      onInput(event) {
        const value = this.input.value;

        startEdit.setState({ laborFixedRateNew: value });

        const decimalIndex = value.indexOf('.');
        if (decimalIndex >= 0 && value.length - decimalIndex - 1 > 2) {
          this.input.value = value.slice(0, decimalIndex + 3); // Limit to 2 decimal places
        }
      }

      getNewValue(event) {
        const selectionStart = this.input.selectionStart;
        const selectionEnd = this.input.selectionEnd;
        const char = event.key;

        // Determine the new value based on cursor position and selection
        const newValue =
          this.input.value.slice(0, selectionStart) +
          char +
          this.input.value.slice(selectionEnd);

        return newValue;
      }

      getGui() {
        return this.input;
      }

      afterGuiAttached() {
        this.input.focus();
        this.input.select();
        this.input.className = 'editedRow';
        this.input.id = 'uniqueId';
      }

      getValue() {
        return this.input.value;
      }

      isCancelBeforeStart() {
        return false;
      }

      isCancelAfterEnd() {
        if (startEdit.state.btnEditClick == true) {
          return false;
        }
        // Check if the input value is the same as the original value
        if (this.input.value !== this.originalValue) {
          // If the input value is different, cancel the edit and revert to the original value
          this.input.value = this.originalValue;
          return true;
        }
        return false;
      }

      focusIn() {
        this.input.focus();
        this.input.select();
      }

      destroy() {
        if (!this.input.value) {
          // Apply red border if the input value is empty
          this.input.style.border = '1px solid red';
        } else {
          // Remove the border if the input value is not empty
          this.input.style.border = '';
        }
      }
    }

    this.state = {
      // changeFleetOption: false,
      removeBtnClick: false,
      inputValue: '',
      filteredOptions: [],
      inputValueSelected: fleetSelected,
      filteredOptionsSelected: [],
      btnEditClick: false,
      partsEdit: false,
      partsEditIndex: '',
      laborEdit: false,
      laborEditIndex: '',
      oldDataArrParts: '',
      updateMsg: '',
      openSnackbarSuccessMsg: '',
      loadElement: `<div class="ag-overlay-panel">
            <div class="ag-overlay-wrapper ag-layout-normal ag-overlay-no-rows-wrapper" ref="eOverlayWrapper">
              <span class="ag-overlay-no-rows-center">No rows to show</span>
            </div>
          </div>`,
      loader: false,
      dataDeleteConfirm: false,
      openSnackbar: false,
      openSnackbarAccount: false,
      openSnackbarAccountDelete: false,
      openSnackbarPaytype: false,
      openSnackbarPaytypeDelete: false,
      openSnackbarOpcode: false,
      openSnackbarOpcodeDelete: false,
      openSnackbarEdit: false,
      openSnackbarError: false,
      openSnackbarDeleteFixedRate: false,
      openSnackbarDeleteLaborGrid: false,
      openSnackbarDeletePartsMatrix: false,
      showPartsMatrix: false,
      showLaborGrid: false,
      typeStatus: true,
      searchStatus: gridRateValue,
      responseStatus: false,
      gridRateValueStart: gridRateValue,
      rawGridApi: {},
      rawGridApiLabor: {},
      rawGridApiParts: {},
      gridApiLabor: {},
      gridApiParts: {},
      isLoading: true,
      oldRowArray: [],
      newRowArray: [],
      oldCodeArray: [],
      prevCodeArray: [],
      newCodeArray: [],
      selectedGrid: selectedGrid,
      selectedPayType: payTypeSelected,
      payTypes: this.props.payTypes,
      opcodes: this.props.opcodes,
      gridPeriods: [],
      gridDoorRate: gridDoorRate,
      addNewRow: false,
      addNewFleet: false,
      isFleetLoaded: false,
      isFleetNamesLoaded: false,
      customerNames: { customerName: 'All' },
      selectedCustomer: '',
      selectedPayTypeOption: '',
      selectedOpcodeOption: '',
      selectedFleet: fleetAccountSelectedCustomer,
      selectedPayTypeOptionType: fleetAccountSelectedPayType,
      selectedOpcodeOptionType: fleetAccountSelectedOpcode,
      fleetSelected:
        this.props.history &&
        this.props.history.location &&
        this.props.history.location.state &&
        this.props.history.location.state.gridType ==
          'Fixed Rate - Fleet Pay Type'
          ? fleetAccountSelectedPayType
          : this.props.history &&
            this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.gridType ==
              'Fixed Rate - Fleet Customer'
          ? fleetAccountSelectedCustomer
          : this.props.history &&
            this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.gridType ==
              'Fixed Rate - Fleet Opcode'
          ? fleetAccountSelectedOpcode
          : '',
      selectedCustomerNamesExist: [],
      selectedPayTypeNamesExist: [],
      selectedOpcodeNamesExist: [],
      showAllJobs: showAllJobs,
      gridRateValue: 'fixedRate',
      matrixRateValue: 'partsFixedRate',
      partsSourceArray: [],
      laborGrid: false,
      fixedRate: true,
      doorRate: '',
      fixedDoorRate: '',
      fixedLaborRate: '',
      partsFixedRate: true,
      partsMatrix: false,
      doorRateChanged: false,
      fixedDoorRateChanged: false,
      fixedRateChanged: false,
      markupChanged: false,
      markupValueChanged: false,
      pSourceChanged: false,
      matrixChanged: false,
      markupValue: '',
      partsMarkup: 'Markup',
      partsSource: '',
      gridInstalledDate: new Date(),
      matrixInstalledDate: new Date(),
      selectedFleetError: false,
      setFleetOpen: false,
      fleetNameError: false,
      doorRateError: false,
      fixedLaborRateError: false,
      gridInstalledDateError: false,
      markupError: false,
      partsourceError: false,
      matrixInstalledDateError: false,
      laborFixedRateNew: null,
      doorRateNew: null,
      partsFixedRateOld: '',
      partsFixedRateNew: null,
      errorItems: [],
      storeId: storeId,
      columnDefs: [
        // {
        //   headerName: 'Door Rate',
        //   field: 'doorRate',
        //   width: 90,njan
        //   },
        // },
        {
          headerName: 'Fleet Name',
          field: 'fleetName',
          width: 130,
          tooltipField: 'fleetName',
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },

        {
          headerName: 'Type',
          field: 'fleetFlag',
          tooltipField: 'fleetFlag',
          width: 120,
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },

        {
          headerName: 'Labor Grid',
          hide: true,
          field: 'laborgridOrNot',
          width: 70,
          unSortIcon: false,
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'center', border: ' 0px white' };
          },
          cellRenderer: params => {
            var index = params.rowIndex;

            if (typeof params.value != 'undefined') {
              return `<input type='checkbox' value=${
                params.value
              } disabled="disabled"
              id="laborgridOrNot${index}"
              ${params.value == 1 ? 'checked' : ''} />`;
            } else {
              return '';
            }
          }
        },
        {
          headerName: 'Door Rate',
          field: 'fleetRate',
          tooltipField: 'fleetRate',
          width: 105,
          suppressMenu: true,
          unSortIcon: true,
          valueSetter: function(params) {
            var newValue = parseFloat(params.newValue); // Parse the new value as a float
            if (isNaN(newValue) || newValue < 0) {
              // Validate the new value
              // Validation failed, return false to reject the new value

              return false;
            }

            // Validation passed, update the data with the new value
            params.data.fleetRate = newValue;
            return true;
          },
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$',
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          cellEditor: NumericCellEditor, // Using the custom cell editor class
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: '1 Hr Door Rt',
          hide: true,
          field: 'doorRate',
          width: 105,
          //cellRenderer: 'fixedRateRenderer',
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$',
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Install Date',
          field: 'installDate',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          width: 110,
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          editable: false,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          }
        },
        {
          headerName: 'Period',
          field: 'gridOrder',
          valueFormatter: this.formatCellValuePeriod,
          width: 110,
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          },
          filterParams: {
            valueFormatter: this.formatCellValuePeriod
          }
        },

        {
          headerName: 'Action',
          width: 120,
          filter: false,
          sortable: false,
          editable: false,

          suppressMenu: true,
          hide:
            typeof keycloak.realmAccess.roles !== 'undefined' &&
            keycloak.realmAccess.roles.includes('client') === true
              ? true
              : false,
          flex: 1,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          cellRenderer(params) {
            let index = params.rowIndex;
            const eDiv = document.createElement('div');
            if (params.data.fleetFlag == 'Fixed Rate') {
              eDiv.innerHTML = `<button   title="Edit" id="btnedit${index}" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button"><i class="fas fa-pencil-alt"></i></button> <button  title="Cancel" id="btncancel${index}" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdate${index}" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-save"></i></button>
            <button title="Delete" id="btndelete${index}" style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-button" ><i class="fas fa-trash-alt"></i></button>`;
            } else {
              eDiv.innerHTML = `<button   title="View" id="btnedit${index}" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="view-button"><i class="fas fa-eye"></i></button> <button  title="Cancel" id="btncancel${index}" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdate${index}" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-save"></i></button>
            <button title="Delete" id="btndelete${index}" style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-button" ><i class="fas fa-trash-alt"></i></button>`;
            }

            // if (
            //   props.keycloak &&
            //   props.keycloak.realmAccess.roles.includes('admin') == false &&
            //   props.keycloak.realmAccess.roles.includes('superadmin') ==
            //     false &&
            //   props.keycloak.realmAccess.roles.includes('user') == false
            // ) {
            // $(document).ready(function() {
            //   $('.edit-button').attr('disabled', 'disabled');
            //   $('.edit-button').css('background', '#38416373');
            //   $('.edit-button').css('cursor', 'default');
            // });
            // }
            if (index !== undefined) {
              const eButton = eDiv.querySelectorAll('.edit-button')[0];
              const vButton = eDiv.querySelectorAll('.view-button')[0];
              const uButton = eDiv.querySelectorAll('.update-button')[0];
              const cButton = eDiv.querySelectorAll('.cancel-button')[0];
              const dButton = eDiv.querySelectorAll('.delete-button')[0];

              // eButton.addEventListener('click', () => {
              //   alert(44);
              //   startEdit.gridApiParts.redrawRows();
              //   $(`.cancel-buttonp`).hide();
              //   $(`.edit-buttonp`).show();
              //   $(`.update-buttonp`).hide();
              //   $(`.delete-buttonp`).hide();
              //   let rowData = startEdit.state.oldDataArr;

              //   // startEdit.setState({
              //   //   editedRowId: null
              //   // });
              //   localStorage.setItem('index', index);
              //   startEdit.setState({ cancel: false });
              //   startEdit.onBtStartEditingParts(index);
              //   localStorage.setItem('fleetIdParts', params.data.id);
              //   let editingCells = params.api.getEditingCells();
              //   startEdit.setState({
              //     editedRowId: index
              //   });
              //   let oldArr = {
              //     index: index,
              //     partsmarkup: params.data.partsmarkup,
              //     id: params.data.id
              //   };
              //   var rowPrevArray = [];
              //   let indexArr = rowPrevArray.findIndex(
              //     ({ id }) => id == params.data.id
              //   );
              //   if (indexArr === -1) {
              //     rowPrevArray.push(oldArr);
              //   }params
              //   startEdit.setState({
              //     oldDataArr: rowPrevArray
              //   });
              //   $('#btneditp' + index).hide();
              //   $('#btndeletep' + index).hide();
              //   $('#btncancelp' + index).show();
              //   $('#btnupdatep' + index).show();
              // });

              eButton &&
                eButton.addEventListener('click', () => {
                  startEdit.setState({ laborEdit: true });
                  startEdit.setState({ laborEditIndex: index });
                  startEdit.setState({ btnEditClick: false });

                  if (startEdit.state.partsEdit == true) {
                    // let rowData = startEdit.state.oldDataArrParts;
                    // var rowNode =
                    //   startEdit.state.gridApiParts &&
                    //   startEdit.state.gridApiParts.api.getDisplayedRowAtIndex(
                    //     startEdit.state.partsEditIndex
                    //   );

                    // console.log('rowNode======', rowNode);
                    // rowNode &&
                    //   rowNode.setDataValue(
                    //     'partsmarkup',
                    //     rowData[0].partsmarkup
                    //   );
                    // startEdit.state.gridApiParts &&
                    //   startEdit.state.gridApiParts.api.refreshCells({
                    //     columns: ['partsmarkup'],
                    //     rowNodes: [rowNode],
                    //     force: true
                    //   });

                    // startEdit.onBtStopEditingParts(
                    //   startEdit.state.partsEditIndex
                    // );

                    let rowData = startEdit.state.oldDataArrParts;

                    if (
                      startEdit.state.rawGridApiParts &&
                      startEdit.state.partsEditIndex !== undefined
                    ) {
                      const rowNode = startEdit.state.rawGridApiParts.getDisplayedRowAtIndex(
                        startEdit.state.partsEditIndex
                      );

                      if (rowNode) {
                        rowNode.setDataValue(
                          'partsmarkup',
                          rowData[0].partsmarkup
                        );

                        startEdit.state.rawGridApiParts.refreshCells({
                          columns: ['partsmarkup'],
                          rowNodes: [rowNode],
                          force: true
                        });
                      } else {
                        // Log an error if rowNode is undefined
                        //  console.error('Row node is undefined or not found.');
                      }
                    } else {
                      // Log an error if the grid API is not initialized or index is invalid
                      // console.error(
                      //   'Grid API is not initialized or partsEditIndex is invalid:',
                      //   startEdit.state.partsEditIndex
                      // );
                    }

                    $('#btneditp' + startEdit.state.partsEditIndex).show();
                    $('#btncancelp' + startEdit.state.partsEditIndex).hide();
                    $('#btnupdatep' + startEdit.state.partsEditIndex).hide();
                    $('#btndeletep' + startEdit.state.partsEditIndex).show();
                    startEdit.setState({
                      partsfixedRateError: null
                    });
                  }

                  $(`.cancel-button`).hide();
                  $(`.update-button`).hide();
                  $(`.edit-button`).show();
                  $(`.view-button`).show();
                  $(`.delete-button`).show();
                  $('.laborFixedRateValue').hide();
                  $('.doorRateValues').hide();
                  $('.doorRateValue').hide();
                  $('.fixedRateValue').hide();
                  localStorage.setItem('oldId', params.data.id);
                  localStorage.setItem('oldDoorRate', params.data.doorRate);
                  localStorage.setItem(
                    'oldlaborFixedRate',
                    params.data.fleetRate
                  );
                  var oldRow = [];
                  oldRow.push(params.data.id);
                  oldRow.push(params.data.doorRate);
                  oldRow.push(params.data.fleetRate);
                  JSON.stringify(oldRow);
                  localStorage.setItem('oldRow', oldRow);
                  startEdit.setState({
                    editedRowId: index
                  });
                  startEdit.setState({
                    isCodeEdited: true
                  });
                  var rowPrev = {
                    id: params.data.id,
                    //doorRate: params.data.doorRate,
                    laborFixedRate: params.data.fleetRate,
                    storeId: JSON.parse(
                      localStorage.getItem('selectedStoreId')
                    )[0]
                  };

                  startEdit.setState({
                    laborFixedRateNew: params.data.fleetRate
                    //doorRateNew: params.data.doorRate
                  });
                  var rowPrevArray = startEdit.state.prevCodeArray;
                  let indexArr = rowPrevArray.findIndex(
                    ({ id, laborFixedRate, doorRate }) =>
                      id === rowPrev.id &&
                      laborFixedRate === rowPrev.laborFixedRate
                    //&& doorRate === rowPrev.doorRate
                  );
                  if (indexArr === -1) {
                    rowPrevArray.push(rowPrev);
                  }
                  startEdit.setState({
                    prevCodeArray: rowPrevArray
                  });
                  var prevRow = [];
                  prevRow.push({
                    id: params.data.id,
                    laborFixedRate: params.data.fleetRate,
                    //doorRate: params.data.doorRate,
                    storeId: JSON.parse(
                      localStorage.getItem('selectedStoreId')
                    )[0]
                  });
                  startEdit.setState({
                    oldCodeArray: prevRow
                  });
                  startEdit.setState({
                    laborfixedRateError: null
                    //doorRateError: null
                  });
                  localStorage.setItem('fleetId', params.data.id);
                  //startEdit.refreshDisable();
                  startEdit.onBtStartEditing(index);
                  $(`#btndelete${index}`).hide();
                  $(`#btnedit${index}`).hide();
                  $(`#btncancel${index}`).show();
                  $(`#btnupdate${index}`).show();
                });

              vButton &&
                vButton.addEventListener('click', () => {
                  if (params.data.fleetFlag == 'Matrix') {
                    //window.location.href = 'PartsMatrix';
                    window.filterStatePrts = startEdit.gridApiParts.getFilterModel();
                    window.sortStatePrts = startEdit.gridApiParts.getSortModel();
                    props.history.push({
                      pathname: '/PartsMatrix',
                      state: {
                        fleetName: params.data.fleetName,
                        // gridOrder: params.data.gridOrder,
                        pageType: 'FleetAccounts',
                        matrixType: params.data.fleetName,
                        matrixDate: params.data.installDate,
                        selectedPartsFor:
                          startEdit.state.gridRateValueStart == 'customer'
                            ? 'cust_fleet'
                            : startEdit.state.gridRateValueStart == 'paytype'
                            ? 'paytype_fleet'
                            : 'opcode_fleet',
                        gridRateValueStart: startEdit.state.gridRateValueStart,
                        selectedPayType:
                          startEdit.state.selectedPayTypeOptionType,
                        selectedOpcode:
                          startEdit.state.selectedOpcodeOptionType,
                        customerName: startEdit.state.selectedFleet
                      }
                    });
                  } else if (params.data.fleetFlag == 'Grid') {
                    //console.log('data value=', params.data);
                    window.filterStateLbr = startEdit.gridApiLabor.getFilterModel();
                    window.sortStateLbr = startEdit.gridApiLabor.getSortModel();
                    props.history.push({
                      pathname: '/LaborGridPricing',
                      state: {
                        fleetName: params.data.fleetName,
                        //  selectedGrid: params.data.gridOrder,
                        selectedGrid: '',
                        selectedGridFor:
                          startEdit.state.gridRateValueStart == 'customer'
                            ? 'cust_fleet'
                            : startEdit.state.gridRateValueStart == 'paytype'
                            ? 'paytype_fleet'
                            : 'opcode_fleet',
                        gridDate: params.data.installDate,
                        pageType: 'FleetAccounts',
                        gridType: params.data.fleetName,
                        gridRateValueStart: startEdit.state.gridRateValueStart,
                        customerName: startEdit.state.selectedFleet,
                        selectedPayType:
                          startEdit.state.selectedPayTypeOptionType,
                        selectedOpcode: startEdit.state.selectedOpcodeOptionType
                      }
                    });
                  }
                });
              uButton.addEventListener('click', () => {
                // console.log(
                //   'enter===1',
                //   startEdit.state.laborFixedRateNew,
                //   '==',
                //   startEdit.state.doorRateNew
                // );

                startEdit.setState({ btnEditClick: true });
                if (
                  startEdit.state.laborFixedRateNew &&
                  startEdit.state.laborFixedRateNew != '' &&
                  startEdit.state.laborFixedRateNew != '.' &&
                  startEdit.state.laborFixedRateNew > 0

                  //&& startEdit.state.doorRateNew &&
                  // startEdit.state.doorRateNew != 0 &&
                  //startEdit.state.doorRateNew != '' &&
                  // startEdit.state.doorRateNew != '0'
                ) {
                  document.getElementById('uniqueId').style.borderColor = '';
                  //console.log('enter===2');
                  startEdit.onBtStopEditing(index);
                  //alert(111);
                  localStorage.setItem('newId', params.data.id);
                  localStorage.setItem(
                    'newLaborFixedRate',
                    params.data.fleetRate
                  );
                  const storeId = JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0];
                  startEdit.setState({
                    isCodeEdited: false
                  });
                  var rowNode = params.api.getDisplayedRowAtIndex(
                    params.rowIndex
                  );
                  if (rowNode != undefined) {
                    rowNode.data['laborFixedRate'] =
                      startEdit.state.laborFixedRateNew;
                    rowNode.data['doorRate'] = startEdit.state.doorRateNew;
                  }
                  startEdit.setState({
                    prevIndex: ''
                  });
                  var newRow = [];
                  newRow.push(
                    params.data.id,
                    params.data.fleetRate,
                    //params.data.doorRate,
                    storeId
                  );
                  let oldDataArr = lodash.filter(
                    startEdit.state.prevCodeArray,
                    item => {
                      return (
                        item.laborFixedRate == params.data.fleetRate &&
                        // item.doorRate == params.data.doorRate &&
                        item.id == params.data.id
                      );
                    }
                  );
                  var oldData = [];
                  oldData = startEdit.state.oldCodeArray;
                  let lbr = params.data.fleetRate;
                  lbr = parseFloat(lbr).toFixed(2);
                  // let dr = params.data.doorRate;
                  // dr = parseFloat(dr).toFixed(2);
                  if (
                    oldData[0].id == params.data.id
                    // && oldData[0].laborFixedRate != lbr
                  ) {
                    localStorage.setItem('newRow', newRow);

                    startEdit.deleteFleet(params.data, 'labor', 'update');
                  }
                  startEdit.setState({
                    laborfixedRateError: null,
                    doorRateError: null
                  });

                  startEdit.setState({
                    editedRowId: null
                  });
                  startEdit.gridApiLabor.redrawRows();
                  $(`#btnedit${index}`).show();
                  $(`#btncancel${index}`).hide();
                  $(`#btnupdate${index}`).hide();
                } else {
                  // var rowNode = params.api.getDisplayedRowAtIndex(
                  //   params.rowIndex
                  // );
                  // //alert(startEdit.state.partsFixedRateNew);

                  // params.data.partsmarkup = startEdit.state.partsFixedRateNew;

                  // if (
                  //   startEdit.state.partsFixedRateNew == null ||
                  //   startEdit.state.partsFixedRateNew == '' ||
                  //   startEdit.state.partsFixedRateNew == '0'
                  // ) {
                  //   var rowNode = params.api.getDisplayedRowAtIndex(index);
                  //   rowNode.data['partsmarkup'] = null;
                  //   startEdit.setState({
                  //     partsfixedRateError: index
                  //   });
                  //   // var rowNode = params.api.getDisplayedRowAtIndex(index);
                  //   // rowNode.data['laborFixedratevalue'] = null;
                  //   params.api.refreshCells({
                  //     columns: ['partsmarkup'],
                  //     rowNodes: [params.node],
                  //     force: true
                  //   });
                  // }

                  // if (
                  //   startEdit.state.partsFixedRateNew == null ||
                  //   startEdit.state.partsFixedRateNew == '' ||
                  //   startEdit.state.partsFixedRateNew == 'N/A' ||
                  //   (startEdit.state.partsFixedRateNew != null &&
                  //     startEdit.state.partsFixedRateNew.split(' ')[1] == '')
                  // ) {
                  //   startEdit.setState({
                  //     partsfixedRateError: index
                  //   });
                  //   var rowNode = params.api.getDisplayedRowAtIndex(index);
                  //   rowNode.data['partsmarkup'] =
                  //     startEdit.state.partsFixedRateNew.split(' ')[0] + ' ';
                  //   params.api.refreshCells({
                  //     columns: ['partsmarkup'],
                  //     rowNodes: [params.node],
                  //     force: true
                  //   });

                  //   return false;
                  // }
                  document.getElementById('uniqueId').style.borderColor = 'red';
                  var rowNode = params.api.getDisplayedRowAtIndex(
                    params.rowIndex
                  );

                  rowNode.data['laborFixedRate'] =
                    startEdit.state.laborFixedRateNew;
                  rowNode.data['doorRate'] = startEdit.state.doorRateNew;
                  startEdit.setState({
                    laborfixedRateError: null,
                    doorRateError: null
                  });
                  //console.log('enter===33');
                  if (
                    startEdit.state.laborFixedRateNew == null ||
                    startEdit.state.laborFixedRateNew == '' ||
                    startEdit.state.laborFixedRateNew == '0' ||
                    startEdit.state.laborFixedRateNew == 0
                  ) {
                    if (
                      startEdit.state.doorRateNew == null ||
                      startEdit.state.doorRateNew == '' ||
                      startEdit.state.doorRateNew == '0' ||
                      startEdit.state.doorRateNew == 0
                    ) {
                      //console.log('enter===63');
                      var rowNode = params.api.getDisplayedRowAtIndex(index);
                      rowNode.data['doorRate'] = null;
                      rowNode.data['laborFixedRate'] = null;
                      startEdit.setState({
                        laborfixedRateError: index,
                        doorRateError: index
                      });

                      if (params.data.laborgridOrNot == 1) {
                        params.api.refreshCells({
                          columns: ['doorRate'],
                          rowNodes: [params.node],
                          force: true
                        });
                      } else {
                        params.api.refreshCells({
                          columns: ['doorRate', 'laborFixedRate'],
                          rowNodes: [params.node],
                          force: true
                        });
                      }
                    } else {
                      //console.log('enter===73', params.data.laborgridOrNot);
                      var rowNode = params.api.getDisplayedRowAtIndex(index);
                      if (params.data.laborgridOrNot == 1) {
                        // console.log('enter===733');
                        // rowNode.data['laborFixedRate'] = startEdit.state.laborFixedRateNew;
                        rowNode.data['doorRate'] = startEdit.state.doorRateNew;
                        startEdit.setState({
                          laborfixedRateError: null,
                          doorRateError: null
                        });

                        params.api.refreshCells({
                          columns: ['doorRate'],
                          rowNodes: [params.node],
                          force: true
                        });
                        startEdit.deleteFleet(params.data, 'labor', 'update');

                        // startEdit.setState({
                        //   laborfixedRateError: null,
                        //   doorRateError: null
                        // });
                        startEdit.setState({
                          editedRowId: null
                        });
                        startEdit.gridApiLabor.redrawRows();
                        $(`#btnedit${index}`).show();
                        $(`#btncancel${index}`).hide();
                        $(`#btnupdate${index}`).hide();
                      } else {
                        //console.log('enter===7333');
                        rowNode.data['laborFixedRate'] = null;
                        rowNode.data['doorRate'] = startEdit.state.doorRateNew;
                        startEdit.setState({
                          laborfixedRateError: index,
                          doorRateError: null
                        });
                        params.api.refreshCells({
                          columns: ['laborFixedRate', 'doorRate'],
                          rowNodes: [params.node],
                          force: true
                        });
                      }
                    }
                  } else {
                    if (
                      startEdit.state.doorRateNew == null ||
                      startEdit.state.doorRateNew == '' ||
                      startEdit.state.doorRateNew == '0' ||
                      startEdit.state.doorRateNew == 0
                    ) {
                      // console.log(
                      //   'enter===65',
                      //   startEdit.state.laborFixedRateNew
                      // );
                      var rowNode = params.api.getDisplayedRowAtIndex(index);
                      rowNode.data['doorRate'] = null;
                      rowNode.data['laborFixedRate'] =
                        startEdit.state.laborFixedRateNew;
                      startEdit.setState({
                        laborfixedRateError: index,
                        doorRateError: index
                      });
                      if (params.data.laborgridOrNot == 1) {
                        params.api.refreshCells({
                          columns: ['doorRate'],
                          rowNodes: [params.node],
                          force: true
                        });
                      } else {
                        params.api.refreshCells({
                          columns: ['laborFixedRate', 'doorRate'],
                          rowNodes: [params.node],
                          force: true
                        });
                      }
                    } else {
                      // console.log('enter===75');
                      var rowNode = params.api.getDisplayedRowAtIndex(index);
                      rowNode.data['laborFixedRate'] =
                        startEdit.state.laborFixedRateNew;
                      rowNode.data['doorRate'] = startEdit.state.doorRateNew;
                      startEdit.setState({
                        laborfixedRateError: null,
                        doorRateError: null
                      });
                      // params.api.refreshCells({
                      //   columns: ['doorRate'],
                      //   rowNodes: [params.node],
                      //   force: true
                      // });
                    }
                  }
                }
              });
              cButton.addEventListener('click', () => {
                //  alert(3);
                startEdit.onBtStopEditing(index);
                //alert(77);
                startEdit.setState({
                  isCodeEdited: true
                });
                var rowNode = params.api.getDisplayedRowAtIndex(index);
                let valArr = startEdit.state.prevCodeArray.filter(function(o) {
                  return o.id == params.data.id;
                });

                //console.log(valArr);
                if (valArr.length > 0) {
                  const lastIndex = valArr.length - 1;
                  rowNode.setDataValue(
                    'fleetRate',
                    valArr[lastIndex].laborFixedRate
                  );
                  //rowNode.setDataValue('doorRate', valArr[0].doorRate);
                  rowNode.data['fleetRate'] = valArr[lastIndex].laborFixedRate;
                  // rowNode.data['doorRate'] = valArr[0].doorRate;
                }
                startEdit.setState({
                  editedRowId: null
                });
                params.api.refreshCells({
                  // columns: ['laborFixedRate', 'doorRate'],
                  columns: ['fleetRate'],
                  rowNodes: [rowNode],
                  force: true
                });
                let filteredArray = startEdit.state.newCodeArray.filter(
                  function(obj) {
                    return obj.id != params.data.id;
                  }
                );
                startEdit.setState({
                  newCodeArray: filteredArray
                });
                if (startEdit.state.newCodeArray.length <= 0) {
                  startEdit.setState({
                    isCodeRowUpdated: false
                  });
                }
                startEdit.setState({
                  laborfixedRateError: null,
                  doorRateError: null
                });
                startEdit.gridApiLabor.redrawRows();
                $(`#btnedit${index}`).show();
                $(`#btncancel${index}`).hide();
                $(`#btnupdate${index}`).hide();
                $(`#btndelete${index}`).show();
              });
              dButton.addEventListener('click', function() {
                var selectedId = params.data.id;
                if (selectedId != '') {
                  startEdit.deleteFleet(params.data, 'labor', 'delete');
                }
                $('#btnedit' + index).show();
                $('#btncancel' + index).hide();
                $('#btnupdate' + index).hide();
                $('#btndelete' + index).show();
              });
            }
            return eDiv;
          }
        }
        // {
        //   headerName: 'Action',
        //   //cellRenderer: 'buttonRenderer',
        //   filter: false,
        //   // tooltip(params) {
        //   //   return 'Edit';
        //   // },
        //   width: 90,
        //   editable: false,
        //   sortable: false,
        //
        //   suppressMenu: true,
        //   // hide:
        //   //   // eslint-disable-next-line react/destructuring-assignment
        //   //   !!(
        //   //     typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
        //   //     this.props.keycloak.realmAccess.roles.includes('client') === true
        //   //   ),
        //   cellStyle: params => {
        //     return startEdit.state.isReloaded
        //       ? {
        //           'pointer-events': 'none',
        //           opacity: '0.4',
        //           textAlign: 'center'
        //         }
        //       : { textAlign: 'center' };
        //   },
        //   // cellStyle() {
        //   //   return { textAlign: 'center', border: ' 0px white' };
        //   // },
        //   // eslint-disable-next-line no-dupe-keys
        //   cellRenderer(params) {
        //     const index = params.rowIndex;
        //     const eDiv = document.createElement('div');
        //     // let context = this;
        //     let bgcolor;
        //     if (!startEdit.state.isOpcodeUpdated) {
        //       bgcolor = '#384163';
        //     } else {
        //       bgcolor = '#384163';
        //     }

        //     if (
        //       typeof params.value == 'undefined' &&
        //       typeof params.data != 'undefined'
        //     ) {
        //       if (!params.data) {
        //         eDiv.innerHTML = `<button title="Edit" id="btnedit${index}" style="background: ${bgcolor}; color: #fff; display:none;disabled; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancel${index}" style="background: #384163;color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Confirm" id="btnupdate${index}" style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-check"></i></button>`;
        //       } else {
        //         eDiv.innerHTML = `<button  title="Edit" id="btnedit${index}" style="background: ${bgcolor}; color: #fff; border-radius: 3px; width: 30px;disabled; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancel${index}" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Confirm" id="btnupdate${index}" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-check"></i></button>`;
        //       }
        //     }

        //     if (
        //       props.keycloak &&
        //       props.keycloak.realmAccess.roles.includes('admin') == false &&
        //       props.keycloak.realmAccess.roles.includes('user') == false
        //     ) {
        //       $(document).ready(function() {
        //         $('.edit-button').attr('disabled', 'disabled');
        //         $('.edit-button').css('background', '#38416373');
        //       });
        //     }

        //     return eDiv;
        //   }
        // }
      ],
      columnDefsParts: [
        // {
        //   headerName: 'Door Rate',
        //   field: 'doorRate',
        //   width: 90,
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   cellStyle: function(params) {
        //     return { textAlign: 'right', border: ' 0px white' };
        //   },
        // },

        {
          headerName: 'Fleet Name',
          field: 'fleetName',
          width: 130,
          tooltipField: 'fleetName',
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },

        {
          headerName: 'Type',
          field: 'fleetFlag',
          width: 130,
          tooltipField: 'fleetFlag',
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'Parts Matrix',
          hide: true,
          field: 'laborgridOrNot',
          width: 80,
          suppressMenu: false,
          unSortIcon: false,
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          },
          cellRenderer: params => {
            var index = params.rowIndex;

            if (typeof params.value != 'undefined') {
              return `<input type='checkbox' value=${
                params.value
              } disabled="disabled"
              id="laborgridOrNot${index}"
              ${params.value == 1 ? 'checked' : ''} />`;
            } else {
              return '';
            }
          }
        },
        {
          headerName: 'Markup',
          width: 240,
          flex: 1,
          field: 'partsmarkup',

          // cellEditorFramework: FixedRateRenderer,
          cellRenderer: 'fixedRateRenderer',

          suppressMenu: true,
          unSortIcon: false,
          sortable: false,
          hide: false,

          // editable: !(
          //   typeof keycloak.realmAccess.roles !== 'undefined' &&
          //   keycloak.realmAccess.roles.includes('client') === true
          // ),
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },

        {
          headerName: 'Source',
          hide: true,
          field: 'partsource',
          width: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'Install Date',
          field: 'installDate',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          width: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          }
        },

        {
          headerName: 'Period',
          field: 'gridOrder',
          valueFormatter: this.formatCellValuePeriod,
          width: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          },
          filterParams: {
            valueFormatter: this.formatCellValuePeriod
          }
        },
        {
          headerName: 'Action',
          cellRenderer: 'buttonRenderer',
          cellStyle: this.cellStyles,
          width: 100,
          flex: 1,
          hide:
            typeof keycloak.realmAccess.roles !== 'undefined' &&
            keycloak.realmAccess.roles.includes('client') === true
              ? true
              : false,
          filter: false,

          suppressMenu: true,
          sortable: false,
          editable: false,
          // tooltip: function(params) {
          //   return 'Edit';
          // },
          cellRenderer(params) {
            //console.log('paramssssss0000', params);
            var index = params.rowIndex;
            var eDiv = document.createElement('div');
            //console.log('params===', params);
            if (params.data.id == '' && params.data.name == '') {
              if (params.data.fleetFlag == 'Fixed Rate') {
                eDiv.innerHTML =
                  '<button  title="Edit" id="btneditp' +
                  index +
                  '" style="background: #384163; color: #fff; display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-buttonp"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancelp' +
                  index +
                  '" style="background: #384163;color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-buttonp" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdatep' +
                  index +
                  '" style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-buttonp" ><i class="fas fa-save"></i></button>&nbsp;<button title="Delete" id="btndeletep' +
                  index +
                  '" style="background: #384163;  color: #fff; border-radius: 3px;  width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-buttonp" ><i class="fas fa-trash-alt"></i></button>';
              } else {
                eDiv.innerHTML =
                  '<button  title="View" id="btneditp' +
                  index +
                  '" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="view-buttonp"><i class="fas fa-eye"></i></button> <button title="Cancel" id="btncancelp' +
                  index +
                  '" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-buttonp" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdatep' +
                  index +
                  '" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-buttonp" ><i class="fas fa-save"></i></button>&nbsp;<button title="Delete" id="btndeletep' +
                  index +
                  '" style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-buttonp" ><i class="fas fa-trash-alt"></i></button>';
              }
            } else {
              if (params.data.fleetFlag == 'Fixed Rate') {
                eDiv.innerHTML =
                  '<button  title="Edit" id="btneditp' +
                  index +
                  '" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-buttonp"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancelp' +
                  index +
                  '" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-buttonp" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdatep' +
                  index +
                  '" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-buttonp" ><i class="fas fa-save"></i></button>&nbsp;<button title="Delete" id="btndeletep' +
                  index +
                  '" style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-buttonp" ><i class="fas fa-trash-alt"></i></button>';
              } else {
                eDiv.innerHTML =
                  '<button  title="View" id="btneditp' +
                  index +
                  '" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="view-buttonp"><i class="fas fa-eye"></i></button> <button title="Cancel" id="btncancelp' +
                  index +
                  '" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-buttonp" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdatep' +
                  index +
                  '" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-buttonp" ><i class="fas fa-save"></i></button>&nbsp;<button title="Delete" id="btndeletep' +
                  index +
                  '" style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-buttonp" ><i class="fas fa-trash-alt"></i></button>';
              }
            }

            if (
              props.keycloak &&
              props.keycloak.realmAccess.roles.includes('admin') == false &&
              props.keycloak.realmAccess.roles.includes('superadmin') ==
                false &&
              props.keycloak.realmAccess.roles.includes('user') == false
            ) {
              $(document).ready(function() {
                $('.edit-buttonp').attr('disabled', 'disabled');
                $('.edit-buttonp').css('background', '#38416373');
                $('.edit-buttonp').css('cursor', 'default');
              });
              // $('.editNickName').attr('disabled', true);
            }
            if (index != undefined) {
              var eButton = eDiv.querySelectorAll('.edit-buttonp')[0];
              var vButton = eDiv.querySelectorAll('.view-buttonp')[0];
              var uButton = eDiv.querySelectorAll('.update-buttonp')[0];
              var cButton = eDiv.querySelectorAll('.cancel-buttonp')[0];
              var dButton = eDiv.querySelectorAll('.delete-buttonp')[0];

              eButton &&
                eButton.addEventListener('click', () => {
                  startEdit.setState({ partsEdit: true });
                  startEdit.setState({ partsEditIndex: index });
                  if (startEdit.state.laborEdit == true) {
                    startEdit.onBtStopEditing(startEdit.state.laborEditIndex);
                    $('#btnedit' + startEdit.state.laborEditIndex).show();
                    $('#btncancel' + startEdit.state.laborEditIndex).hide();
                    $('#btnupdate' + startEdit.state.laborEditIndex).show();
                    $('#btndelete' + startEdit.state.laborEditIndex).hide();
                  }
                  startEdit.gridApiParts.redrawRows();
                  $(`.cancel-buttonp`).hide();
                  $(`.edit-buttonp`).show();
                  $(`.view-buttonp`).show();
                  $(`.update-buttonp`).hide();
                  $(`.delete-buttonp`).hide();
                  let rowData = startEdit.state.oldDataArr;

                  // startEdit.setState({
                  //   editedRowId: null
                  // });
                  localStorage.setItem('index', index);
                  startEdit.setState({ cancel: false });
                  startEdit.onBtStartEditingParts(index);
                  localStorage.setItem('fleetIdParts', params.data.id);
                  let editingCells = params.api.getEditingCells();
                  startEdit.setState({
                    editedRowId: index
                  });
                  let oldArr = {
                    index: index,
                    partsmarkup: params.data.partsmarkup,
                    id: params.data.id
                  };

                  startEdit.setState({
                    partsFixedRateNew: params.data.partsmarkup
                  });

                  var rowPrevArray = [];
                  let indexArr = rowPrevArray.findIndex(
                    ({ id }) => id == params.data.id
                  );
                  if (indexArr === -1) {
                    rowPrevArray.push(oldArr);
                  }
                  startEdit.setState({
                    oldDataArr: rowPrevArray
                  });
                  startEdit.setState({
                    oldDataArrParts: rowPrevArray
                  });
                  $('#btneditp' + index).hide();
                  $('#btndeletep' + index).hide();
                  $('#btncancelp' + index).show();
                  $('#btnupdatep' + index).show();
                });

              vButton &&
                vButton.addEventListener('click', () => {
                  if (params.data.fleetFlag == 'Matrix') {
                    //window.location.href = 'PartsMatrix';
                    window.filterStatePrts = startEdit.gridApiParts.getFilterModel();
                    window.sortStatePrts = startEdit.gridApiParts.getSortModel();
                    props.history.push({
                      pathname: '/PartsMatrix',
                      state: {
                        fleetName: params.data.fleetName,
                        //gridOrder: params.data.gridOrder,
                        pageType: 'FleetAccounts',
                        matrixType: params.data.fleetName,
                        matrixDate: params.data.installDate,
                        selectedPartsFor:
                          startEdit.state.gridRateValueStart == 'customer'
                            ? 'cust_fleet'
                            : startEdit.state.gridRateValueStart == 'paytype'
                            ? 'paytype_fleet'
                            : 'opcode_fleet',
                        gridRateValueStart: startEdit.state.gridRateValueStart,
                        selectedPayType:
                          startEdit.state.selectedPayTypeOptionType,
                        customerName: startEdit.state.selectedFleet,
                        selectedOpcode: startEdit.state.selectedOpcodeOptionType
                      }
                    });
                  } else if (params.data.fleetFlag == 'Grid') {
                    // console.log('data value=', params.data);
                    window.filterStateLbr = startEdit.gridApiLabor.getFilterModel();
                    window.sortStateLbr = startEdit.gridApiLabor.getSortModel();

                    props.history.push({
                      pathname: '/LaborGridPricing',
                      state: {
                        fleetName: params.data.fleetName,
                        //  selectedGrid: params.data.gridOrder,
                        selectedGrid: '',
                        selectedGridFor:
                          startEdit.state.gridRateValueStart == 'customer'
                            ? 'cust_fleet'
                            : startEdit.state.gridRateValueStart == 'paytype'
                            ? 'paytype_fleet'
                            : 'opcode_fleet',

                        gridDate: params.data.installDate,
                        pageType: 'FleetAccounts',
                        gridType: params.data.fleetName,
                        gridRateValueStart: startEdit.state.gridRateValueStart,
                        customerName: startEdit.state.selectedFleet,
                        selectedPayType:
                          startEdit.state.selectedPayTypeOptionType,
                        selectedOpcode: startEdit.state.selectedOpcodeOptionType
                      }
                    });
                  }
                  // startEdit.gridApiParts.redrawRows();
                  // $(`.cancel-buttonp`).hide();
                  // $(`.edit-buttonp`).show();
                  // $(`.view-buttonp`).show();
                  // $(`.update-buttonp`).hide();
                  // $(`.delete-buttonp`).hide();
                  // let rowData = startEdit.state.oldDataArr;

                  // // startEdit.setState({
                  // //   editedRowId: null
                  // // });
                  // localStorage.setItem('index', index);
                  // startEdit.setState({ cancel: false });
                  // startEdit.onBtStartEditingParts(index);
                  // localStorage.setItem('fleetIdParts', params.data.id);
                  // let editingCells = params.api.getEditingCells();
                  // startEdit.setState({
                  //   editedRowId: index
                  // });
                  // let oldArr = {
                  //   index: index,
                  //   partsmarkup: params.data.partsmarkup,
                  //   id: params.data.id
                  // };
                  // var rowPrevArray = [];
                  // let indexArr = rowPrevArray.findIndex(
                  //   ({ id }) => id == params.data.id
                  // );
                  // if (indexArr === -1) {
                  //   rowPrevArray.push(oldArr);
                  // }
                  // startEdit.setState({
                  //   oldDataArr: rowPrevArray
                  // });
                  // $('#btneditp' + index).hide();
                  // $('#btndeletep' + index).hide();
                  // $('#btncancelp' + index).show();
                  // $('#btnupdatep' + index).show();
                });

              uButton.addEventListener('click', () => {
                startEdit.onBtStopEditingParts(index);

                var rowNode = params.api.getDisplayedRowAtIndex(
                  params.rowIndex
                );

                //alert(startEdit.state.partsFixedRateNew);

                params.data.partsmarkup = startEdit.state.partsFixedRateNew;

                if (
                  startEdit.state.partsFixedRateNew == null ||
                  startEdit.state.partsFixedRateNew == ''
                ) {
                  var rowNode = params.api.getDisplayedRowAtIndex(index);
                  rowNode.data['partsmarkup'] = null;
                  startEdit.setState({
                    partsfixedRateError: index
                  });
                  // var rowNode = params.api.getDisplayedRowAtIndex(index);
                  // rowNode.data['laborFixedratevalue'] = null;
                  params.api.refreshCells({
                    columns: ['partsmarkup'],
                    rowNodes: [params.node],
                    force: true
                  });
                }

                if (
                  startEdit.state.partsFixedRateNew == null ||
                  startEdit.state.partsFixedRateNew == '' ||
                  startEdit.state.partsFixedRateNew == 'N/A' ||
                  (startEdit.state.partsFixedRateNew != null &&
                    (startEdit.state.partsFixedRateNew.split(' ')[1] == '' ||
                      startEdit.state.partsFixedRateNew.split(' ')[1] == '.'))
                ) {
                  startEdit.setState({
                    partsfixedRateError: index
                  });
                  var rowNode = params.api.getDisplayedRowAtIndex(index);
                  rowNode.data['partsmarkup'] =
                    startEdit.state.partsFixedRateNew &&
                    startEdit.state.partsFixedRateNew.split(' ')[0] + ' ';
                  params.api.refreshCells({
                    columns: ['partsmarkup'],
                    rowNodes: [params.node],
                    force: true
                  });

                  return false;
                }

                //console.log('ccccc1====', params.data);
                var selectedId = params.data.id;
                //console.log('selectedId=', selectedId);
                var selectedName = params.data.name;
                var selectedAdvisor = params.data.serviceadvisor;
                var nickName = params.data.nickname;
                nickName = nickName ? nickName.trim() : '';
                var advisorStatus = params.data.active;
                startEdit.setState({ cancel: false });
                startEdit.setState({
                  editedRowId: null
                });
                let arr = startEdit.state.oldDataArr;
                let valueSaved =
                  params.data.partsmarkup &&
                  params.data.partsmarkup.split(' ')[1] &&
                  params.data.partsmarkup.split(' ')[1] != 'undefined'
                    ? params.data.partsmarkup.split(' ')[1]
                    : '';
                let labelSaved =
                  params.data.partsmarkup &&
                  params.data.partsmarkup.split(' ')[0];

                if (
                  params.data.partsmarkup.split(' ')[0].trim() != 'Cost' &&
                  params.data.partsmarkup.split(' ')[0].trim() != 'List' &&
                  valueSaved == ''
                ) {
                  startEdit.setState({
                    partsfixedRateError: index
                  });
                }
                if (valueSaved != '') {
                  params.data.partsmarkup =
                    labelSaved + ' ' + parseFloat(valueSaved).toFixed(2);
                } else {
                  params.data.partsmarkup = labelSaved;
                }

                startEdit.deleteFleet(params.data, 'parts', 'update');
                if (
                  arr[0].nickname != params.data.nickname ||
                  arr[0].active != params.data.active
                ) {
                  let arrNickname = params.data.nickname
                    ? params.data.nickname.trim()
                    : '';
                  let arrNickname1 = arr[0].nickname
                    ? arr[0].nickname.trim()
                    : '';
                  //console.log('nick==', arrNickname, '===', arrNickname1);

                  // if (
                  //   arrNickname != arrNickname1 ||
                  //   arr[0].active != params.data.active
                  // ) {
                  //   startEdit.updateServiceAdvisor(
                  //     selectedId,
                  //     selectedName,
                  //     selectedAdvisor,
                  //     nickName ? nickName.trim() : '',
                  //     advisorStatus
                  //   );
                  //   startEdit.setState({
                  //     oldDataArr: []
                  //   });
                  // }
                }
                startEdit.gridApiParts.redrawRows();
                $('#btneditp' + index).show();
                $('#btncancelp' + index).hide();
                $('#btnupdatep' + index).show();
                $('#btndeletep' + index).hide();

                startEdit.setState({
                  partsfixedRateError: null
                });
              });
              cButton.addEventListener('click', function() {
                startEdit.setState({ cancel: true });
                startEdit.onBtStopEditingParts(index);

                startEdit.setState({
                  isCodeEdited: true
                });
                startEdit.setState({
                  editedRowId: null
                });

                let rowData = startEdit.state.oldDataArr;

                var rowNode = params.api.getDisplayedRowAtIndex(index);
                // console.log('rowNode======', rowNode);
                rowNode.setDataValue('partsmarkup', rowData[0].partsmarkup);
                params.api.refreshCells({
                  columns: ['partsmarkup'],
                  rowNodes: [rowNode],
                  force: true
                });

                $('#btneditp' + index).show();
                $('#btncancelp' + index).hide();
                $('#btnupdatep' + index).hide();
                $('#btndeletep' + index).show();
                startEdit.setState({
                  partsfixedRateError: null
                });
              });
              dButton.addEventListener('click', function() {
                var selectedId = params.data.id;
                if (selectedId != '') {
                  startEdit.deleteFleet(params.data, 'parts', 'delete');
                }
                $('#btneditp' + index).show();
                $('#btncancelp' + index).hide();
                $('#btnupdatep' + index).hide();
                $('#btndeletep' + index).show();
              });
            }
            return eDiv;
          }
        }
      ],

      filter: 'agSetColumnFilter',
      editType: 'fullRow',
      frameworkComponents: {
        gridCheckboxRenderer: GridCheckboxRenderer,
        fixedRateRenderer: FixedRateRenderer
      },
      context: { componentParent: this },
      rowData: [],
      fleetLaborArr: [],
      fleetPartsArr: [],
      selectedFleetNames: [{ customerName: 'All' }],
      chartName: null,
      sortingOrder: ['asc', 'desc', null],
      headerHeight: 45,
      overlayNoRowsTemplateLabor:
        '<span style="padding: 10px; font-size: 12.5px;">No Data Found</span>',
      overlayNoRowsTemplate:
        '<span style="padding: 10px; font-size: 12.5px;">No Data Found</span>',
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false,
        suppressMovable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Right',
            vertical: 'Center'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        },
        {
          id: 'redFont',
          font: {
            color: '#ff0000'
          }
        },
        {
          id: 'greenFont',
          font: {
            color: '008000'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'dateFormatter',
          dataType: 'DateTime',
          numberFormat: {
            format: 'mm/dd/yy'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  deleteFleetConfirmed = () => {
    this.deleteFleet(
      this.state.dataForDelete,
      this.state.dataTypeDelete,
      'delete'
    );
  };

  deleteFleetConfirmedCancel = () => {
    this.setState({ dataDeleteConfirm: false });
  };

  deleteFleet = (data, type, process) => {
    this.setState({ dataForDelete: data });
    this.setState({ dataTypeDelete: type });

    // console.log('data value==', data, type, process);
    let laborArr = [];
    let partsArr = [];

    let fleet_rate_grid = [];
    let fleet_rate_parts = [];
    let fleet_exists_labor_grid = [];
    let fleet_exists_parts_matrix = [];

    let markup = '';
    if (this.state.partsMatrix == true) {
      markup = this.state.partsMarkup;
    } else {
      markup = this.state.partsMarkup + '' + this.state.markupValue;
    }

    let fleettype =
      this.state.searchStatus == 'customer'
        ? 'customer'
        : this.state.searchStatus == 'paytype'
        ? 'paytype'
        : 'opcode';
    let fleetname =
      this.state.searchStatus == 'customer'
        ? this.state.selectedFleet
        : this.state.searchStatus == 'paytype'
        ? this.state.selectedPayTypeOptionType
        : this.state.selectedOpcodeOptionType;

    if (type == 'labor') {
      if (data.fleetFlag == 'Grid') {
        fleet_exists_labor_grid.push({
          process: process,
          fleettype: fleettype,
          fleetname: data.fleetName,
          fleetorgridflag: data.fleetFlag,
          laborfleetrate: data.fleetRate,
          gridinstalldate: data.installDate
        });
      } else {
        fleet_rate_grid.push({
          process: process,
          fleettype: fleettype,
          fleetname: data.fleetName,
          fleetorgridflag: data.fleetFlag,
          laborfleetrate: data.fleetRate,
          gridinstalldate: data.installDate
        });
      }
    }

    if (type == 'parts') {
      if (data.fleetFlag == 'Matrix') {
        fleet_exists_parts_matrix.push({
          process: process,
          fleettype: fleettype,
          fleetname: data.fleetName,
          fleetormatrixflag: data.fleetFlag,
          partsmarkup: data.partsmarkup,
          partsource: null,
          matrixinstalldate: data.installDate
        });
      } else {
        fleet_rate_parts.push({
          process: process,
          fleettype: fleettype,
          fleetname: data.fleetName,
          fleetormatrixflag: data.fleetFlag,
          partsmarkup: data.partsmarkup,
          partsource: null,
          matrixinstalldate: data.installDate
        });
      }
    }

    // Open Material-UI Dialog for confirmation if process is delete
    if (process === 'delete' && this.state.dataDeleteConfirm == false) {
      if (data.fleetFlag == 'Fixed Rate') {
        this.setState({ openConfirmationDialog: true });
      } else if (data.fleetFlag == 'Grid') {
        this.setState({ openConfirmationDialogLaborGrid: true });
      } else if (data.fleetFlag == 'Matrix') {
        this.setState({ openConfirmationDialogPartsMatrix: true });
      }

      this.setState({ dataDeleteConfirm: true });
      return; // Wait for user confirmation
    }

    insertFleetDetails(
      JSON.stringify(fleet_rate_grid),
      JSON.stringify(fleet_rate_parts),
      JSON.stringify(fleet_exists_labor_grid),
      JSON.stringify(fleet_exists_parts_matrix),
      result => {
        // console.log('success=====', result);
        if (
          result.data.statelessCcPhysicalRwInsertOrUpdateFleetAccount.results[0]
            .status == 1
        ) {
          this.getAgGridData();

          this.gridApiLabor.setSortModel(null);
          this.gridApiParts.setSortModel(null);
          this.gridApiLabor.setFilterModel(null);
          this.gridApiParts.setFilterModel(null);
          this.gridApiLabor.redrawRows();
          this.gridApiParts.redrawRows();
          this.clearLaborGrid();
          this.clearPartsGrid();
          this.setState({ dataDeleteConfirm: false });
          this.setState({
            updateMsg:
              result.data.statelessCcPhysicalRwInsertOrUpdateFleetAccount
                .results[0].msg
          });
          if (process == 'update') {
            this.setState({ openSnackbarEdit: true });
          } else {
            if (data.fleetFlag == 'Fixed Rate') {
              this.setState({ openSnackbarDeleteFixedRate: true });
            } else if (data.fleetFlag == 'Grid') {
              this.setState({ openSnackbarDeleteLaborGrid: true });
            } else if (data.fleetFlag == 'Matrix') {
              this.setState({ openSnackbarDeletePartsMatrix: true });
            }
          }
        } else {
          this.setState({
            updateMsg:
              result.data.statelessCcPhysicalRwInsertOrUpdateFleetAccount
                .results[0].msg
          });

          this.setState({ openSnackbarError: true });
        }
      }
    );
  };
  geDeptForDrilldown = () => {
    let data = [
      'Cost',
      'Cost-',
      'Cost+',
      'Cost%',
      'List',
      'List-',
      'List+',
      'List%'
    ];
    return data;
  };
  onBtStartEditing = (index, key, char, pinned) => {
    const groupColumn = this.state.rawGridApiLabor.columnController.columnDefs;
    //console.log('groupColumn', groupColumn);
    // if (
    //   typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
    //   this.props.keycloak.realmAccess.roles.length >= 1 &&
    //   this.props.keycloak.realmAccess.roles.includes('client') === false
    // ) {
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = true;
    groupColumn[4]['editable'] = false;
    groupColumn[5]['editable'] = false;
    // }
    this.state.rawGridApiLabor.setColumnDefs(groupColumn);

    this.state.rawGridApiLabor.columnController.columnDefs[1].editable = true;
    const { rawGridApiLabor } = this.state;
    this.state.rawGridApiLabor.setFocusedCell(
      index,
      // 'laborFixedRate',
      'fleetRate',
      pinned
    );
    this.state.rawGridApiLabor.startEditingCell({
      rowIndex: index,
      // colKey: 'laborFixedRate',
      colKey: 'fleetRate',
      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
  };
  // onBtStartEditing = (index, key, char, pinned) => {
  //   // columnController
  //   const groupColumn = this.state.rawGridApiLabor.columnController.columnDefs;
  //   console.log('groupColumn===', groupColumn);
  //   groupColumn[0]['editable'] = false;
  //   groupColumn[1]['editable'] = false;
  //   groupColumn[2]['editable'] = true;
  //   groupColumn[3]['editable'] = false;
  //   this.state.rawGridApiLabor.setColumnDefs(groupColumn);
  //   this.state.rawGridApiLabor.columnController.columnDefs[1].editable = true;
  //   this.state.rawGridApiLabor.setFocusedCell(
  //     index,
  //     'laborFixedRate',
  //     // 'doorRate',
  //     pinned
  //   );
  //   this.state.rawGridApiLabor.startEditingCell({
  //     rowIndex: index,
  //     colKey: 'laborFixedRate',
  //     // colKey: 'doorRate',
  //     rowPinned: pinned,
  //     keyPress: key,
  //     charPress: char
  //   });
  // };
  onBtStopEditing = () => {
    //alert(4);
    this.gridApiLabor.stopEditing();
    const groupColumn = this.state.rawGridApiLabor.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = false;
    groupColumn[4]['editable'] = false;
    groupColumn[5]['editable'] = false;
    this.state.rawGridApiLabor.setColumnDefs(groupColumn);
  };
  onBtStartEditingParts = (index, key, char, pinned) => {
    // columnController
    const groupColumn = this.state.rawGridApiParts.columnController.columnDefs;
    //console.log('groupColumn===', groupColumn);
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = true;
    groupColumn[3]['editable'] = false;
    this.state.rawGridApiParts.setColumnDefs(groupColumn);
    this.state.rawGridApiParts.columnController.columnDefs[1].editable = true;
    this.state.rawGridApiParts.setFocusedCell(index, 'partsmarkup', pinned);
    this.state.rawGridApiParts.startEditingCell({
      rowIndex: index,
      colKey: 'partsmarkup',
      // colKey: 'doorRate',
      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
  };
  onBtStopEditingParts = () => {
    this.gridApiParts.stopEditing();
    const groupColumn = this.state.rawGridApiParts.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = false;
    groupColumn[4]['editable'] = false;
    groupColumn[5]['editable'] = false;
    this.state.rawGridApiParts.setColumnDefs(groupColumn);
  };
  formatCellValueRate = params => {
    if (params.value && params.value != '') {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return '';
    }
  };
  formatCellValueDate = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };
  formatCellValuePeriod = params => {
    if (params.value && params.value != '') {
      return params.value == 1
        ? 'Current'
        : 'Prior' + (Number(params.value) - 1);
    } else {
      return '';
    }
  };
  resetRawData = () => {
    this.gridApiLabor.setColumnDefs([]);
    this.gridApiLabor.setColumnDefs(this.state.columnDefs);
    this.gridApiParts.setColumnDefs([]);
    this.gridApiParts.setColumnDefs(this.state.columnDefsParts);
  };
  onBtExport = () => {
    var params = {
      sheetName: 'Labor Grid(s)',
      fileName: 'Labor Grid(s)',
      processCellCallback: params => processCells(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: 'Labor Grid(s)'
            },
            mergeAcross: 3
          }
        ],
        [
          {
            data: {
              type: 'String',
              value:
                this.state.selectedGrid == 1 ? 'Current Grid' : 'Prior Grid'
            },
            mergeAcross: 1
          },
          {
            data: {
              type: 'String',
              value: 'Grid / Door Install Date  : ' + this.state.gridDate
            },
            mergeAcross: 2
          },
          {
            data: {
              type: 'String',
              value: '1 Hour Door Rate  : ' + this.state.doorRate
            }
          }
        ],
        []
      ]
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };
  onGridReadyLabor = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApiLabor: params.api });
    this.setState({ gridApiLabor: params });
    this.gridApiLabor = params.api;
    this.setState({ gridColumnApiLabor: params.columnApi });
    this.gridApiLabor.sizeColumnsToFit();
    this.getAgGridData();
    this.columnApiLabor = params.columnApi;
    if (
      this.props.history.location.state == undefined ||
      this.props.history.location.state == null
    ) {
      window.sortStateLbr = {};
      window.filterStateLbr = {};
    }

    this.gridApiLabor.setSortModel(window.sortStateLbr);
    this.gridApiLabor.setFilterModel(window.filterStateLbr);

    const overlay = document.querySelector('#fleetLabor .ag-overlay');
    if (overlay) {
      //overlay.classList.remove('ag-hidden');
      overlay.innerHTML = this.state.loadElement;
    }
  };
  onGridReadyParts = params => {
    this.setState({ rawGridApiParts: params.api });
    this.setState({ gridApiParts: params });
    this.gridApiParts = params.api;

    this.setState({ gridColumnApiParts: params.columnApi });
    this.getAgGridData();
    this.columnApiParts = params.columnApi;
    if (
      this.props.history.location.state == undefined ||
      this.props.history.location.state == null
    ) {
      window.sortStatePrts = {};
      window.filterStatePrts = {};
    }

    this.gridApiParts.setSortModel(window.sortStatePrts);
    this.gridApiParts.setFilterModel(window.filterStatePrts);
    // const overlay = document.querySelector('#fleetParts .ag-overlay');
    // if (overlay) {
    //   overlay.classList.add('ag-hidden');
    // }
    const overlay = document.querySelector('#fleetParts .ag-overlay');
    if (overlay) {
      // overlay.classList.remove('ag-hidden');
      overlay.innerHTML = this.state.loadElement;
    }
  };
  getAgGridData(fleet, selectedStore) {
    if (selectedStore) {
      this.setState({ storeId: selectedStore });
    }
    this.setState({ isLoading: true });
    let fleetArr = [];
    let fleettype = '';
    if (this.state.searchStatus == 'customer') {
      fleettype = 'customer';
      if (fleet || this.state.selectedFleet == '') {
        fleetArr.push('All');
      } else {
        fleetArr.push(this.state.selectedFleet);
      }
    } else if (this.state.searchStatus == 'paytype') {
      fleettype = 'paytype';
      if (fleet || this.state.selectedPayTypeOptionType == '') {
        fleetArr.push('All');
      } else {
        fleetArr.push(this.state.selectedPayTypeOptionType);
      }
    } else if (this.state.searchStatus == 'opcode') {
      fleettype = 'opcode';
      if (fleet || this.state.selectedOpcodeOptionType == '') {
        fleetArr.push('All');
      } else {
        fleetArr.push(this.state.selectedOpcodeOptionType);
      }
    }

    this.getFleetListItemsList(fleetArr, fleettype, selectedStore);
  }

  getFleetListItemsList = (fleetArr, fleettype, selectedStore) => {
    getFleetListItems(
      fleetArr,
      fleettype,
      selectedStore ? selectedStore : this.state.storeId,
      result => {
        let data = result;
        // console.log(
        //   'result===',
        //   result,
        //   '==',
        //   fleetArr,
        //   fleettype,
        //   selectedStore
        // );
        let resultArr = [];
        let laborArr = [];
        let partsArr = [];
        if (
          result.data.statelessCcPhysicalRwGetLaborAndPartsFleetAccount.nodes
            .length > 0
        ) {
          this.setState({ isLoading: false });
          result.data.statelessCcPhysicalRwGetLaborAndPartsFleetAccount.nodes.map(
            (obj, i) => {
              if (obj.laborOrParts == 'labor') {
                laborArr.push(obj);
              } else {
                partsArr.push(obj);
              }
            }
          );
          this.setState({
            fleetLaborArr: laborArr
          });
          // se
          this.setState({
            fleetPartsArr: partsArr
          });
          //console.log('partsArr===', partsArr, this.props);
          if (
            this.props.history.location.state &&
            this.props.history.location.state.gridType &&
            this.props.history.location.state.gridType
              .toLowerCase()
              .includes(fleettype)
          ) {
            if (this.props.history.location.state.gridDate) {
              this.setState({
                fleetLaborArr: laborArr.filter(
                  item =>
                    item.installDate ==
                    this.props.history.location.state.gridDate
                )
              });
            }
            if (this.props.history.location.state.gridDoorRate) {
              this.setState({
                fleetPartsArr: partsArr.filter(
                  item =>
                    item.installDate ==
                    this.props.history.location.state.gridDoorRate
                )
              });
            }
            // this.filterByFleetName(
            //   this.props.history.location.state.customerName,
            //   this.props.history.location.state.selectedPayType,
            //   this.props.history.location.state.gridType
            // );
            if (window.filterStateLbr != undefined) {
              this.filterByValue();
            }
            if (window.filterStatePrts != undefined) {
              this.filterByValueParts();
            }
          }
          // setFleetsParts(partsArr);

          // setFleets(
          //   result.data.statelessCcPhysicalRwGetLaborAndPartsFleetAccount.nodes
          // );
        } else {
          this.setState({
            fleetLaborArr: []
          });
          this.setState({
            fleetPartsArr: []
          });
          this.setState({ isLoading: false });
        }
      }
    );
  };
  filterByFleetName = (customerName, payType, type) => {
    if (customerName && type == 'Fixed Rate - Fleet Customer') {
      var fleetFilterComponent = this.state.rawGridApiLabor.getFilterInstance(
        'fleetName'
      );
      fleetFilterComponent.setModel({ values: [customerName] });
      this.gridApiLabor.onFilterChanged();
    } else if (payType && type == 'Fixed Rate - Fleet Pay Type') {
      var paytypeFilterComponent = this.state.rawGridApiLabor.getFilterInstance(
        'fleetName'
      );
      paytypeFilterComponent.setModel({ values: [payType] });
      this.gridApiLabor.onFilterChanged();
    }
  };
  filterByValue = () => {
    var fleetFilterComponentLbr = '';
    var filterArr = Object.entries(window.filterStateLbr);
    filterArr.map(item => {
      fleetFilterComponentLbr = this.state.rawGridApiLabor.getFilterInstance(
        item[0]
      );
      fleetFilterComponentLbr.setModel({ values: item[1].values });
    });
    this.gridApiLabor.onFilterChanged();
  };
  filterByValueParts = () => {
    var fleetFilterComponentPrts = '';
    var filterArrPrt = Object.entries(window.filterStatePrts);

    filterArrPrt.map(item => {
      fleetFilterComponentPrts = this.state.rawGridApiParts.getFilterInstance(
        item[0]
      );
      fleetFilterComponentPrts.setModel({ values: item[1].values });
    });
    this.gridApiParts.onFilterChanged();
  };
  handleclick = params => {
    this.props.history.push({
      pathname:
        this.props.history &&
        this.props.history.location.state &&
        (this.props.history.location.state.pageType == 'LaborMisses' ||
          this.props.history.location.state.parent == 'LaborMisses')
          ? localStorage.getItem('versionFlag') == 'TRUE'
            ? '/LaborMisses'
            : 'LaborGridMisses'
          : localStorage.getItem('versionFlag') == 'TRUE'
          ? '/PartsMisses'
          : 'PartsTargetMisses',
      state: {
        selectedFilter: this.props.history.location.state.selectedFilter,
        selectedToggle: this.props.history.location.state.selectedToggle,
        selectedMonthYear: this.props.history.location.state.selectedMonthYear,
        parent: this.props.history.location.state.parent,
        previousToggle: this.props.history.location.state.previousToggle,
        payType: this.props.history.location.state.payType,
        gridType: this.props.history.location.state.gridType,
        previousPayType: this.props.history.location.state.PrevPayType,
        previousGridType: this.props.history.location.state.PrevGridType,
        showAllJobs: this.state.showAllJobs,
        filterStart: this.props.history.location.state.filterStart,
        filterEnd: this.props.history.location.state.filterEnd,
        selectedGridType: this.props.history.location.state.selectedGridType
      }
    });
  };

  onPartsFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      partsFixedRateOld: oldVal
    });

    this.setState({
      partsFixedRateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  sortedArray = (array, type) => {
    // Define a key based on the type
    const keyMap = {
      customer: 'customerName',
      paytype: 'payTypeName',
      opcode: 'opcodeName'
    };

    const key = keyMap[type];

    if (!key) {
      throw new Error('Invalid type');
    }

    // Separate "All" from other items
    const allItem = array.find(item => item[key] === 'All');
    const otherItems = array.filter(item => item[key] !== 'All');

    // Sort the other items
    otherItems.sort((a, b) => {
      if (a[key] < b[key]) return -1;
      if (a[key] > b[key]) return 1;
      return 0;
    });

    // Concatenate "All" with the sorted other items
    return allItem ? [allItem, ...otherItems] : otherItems;
  };
  handleAddRecord = event => {
    // console.log(
    //   'handleAddRecord===',
    //   this.state.selectedOpcodeOption,
    //   this.state.selectedPayTypeOption,
    //   this.state.searchStatus
    // );
    if (
      this.state.selectedCustomer !== '' &&
      this.state.searchStatus == 'customer'
    ) {
      //console.log('handleAddRecord===1');
      var rowArray = this.state.selectedCustomerNamesExist;
      let found =
        rowArray &&
        rowArray.some(el => el.customerName == this.state.selectedCustomer);
      let rowArr = {
        customerName: this.state.selectedCustomer
      };
      if (!found) {
        rowArray && rowArray.push(rowArr);
        insertFleetNameToList(
          this.state.selectedCustomer,
          'insert',
          'cust_fleet',
          this.state.storeId,
          result => {
            // console.log(
            //   'cccaaa',
            //   result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
            //     .statelessCcPhysicalRwKpiFleetMasters[0].fleetName
            // );
            if (
              result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
                .statelessCcPhysicalRwKpiFleetMasters[0].fleetName == 'Success'
            ) {
              this.setState({ inputValue: '' });
              this.setState({
                setFleetOpen: true
              });
              this.setState({ openSnackbarAccount: true });
              this.setState({ selectedCustomer: '' });

              $('#select-fleet-account').click();
              //this.setState({ changeFleetOption: false });
            } else {
              this.setState({
                setFleetOpen: false
              });
            }
          }
        );
        //this.fetchFleetPayTypeNames('cust_fleet');
        rowArray = this.sortedArray(rowArray, 'customer');
        //console.log('rowarrayyyyy---', rowArray);
        this.setState({
          selectedCustomerNamesExist: rowArray
        });
        this.fetchFleetCustomerNames();
        this.setState({
          selectedFleetError: false
        });
        this.updateFilteredOptions();

        this.updateFilteredOptionsSelected();
      } else {
        this.setState({ openSnackbarAccount: false });
        $('#select-fleet-account').click();
        this.setState({
          selectedFleetError: true
        });
      }
    } else if (
      this.state.selectedPayTypeOption !== '' &&
      this.state.searchStatus == 'paytype'
    ) {
      //console.log('handleAddRecord===2');
      var rowArray = this.state.selectedPayTypeNamesExist;
      let found = rowArray.some(
        el => el.payTypeName == this.state.selectedPayTypeOption
      );
      let rowArr = {
        payTypeName: this.state.selectedPayTypeOption
      };

      if (!found) {
        rowArray.push(rowArr);

        //console.log('rowArray', rowArray);

        insertFleetNameToList(
          this.state.selectedPayTypeOption,
          'insert',
          'paytype_fleet',
          this.state.storeId,
          result => {
            // console.log(
            //   'cccaaa',
            //   result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
            //     .statelessCcPhysicalRwKpiFleetMasters[0].fleetName
            // );
            if (
              result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
                .statelessCcPhysicalRwKpiFleetMasters[0].fleetName == 'Success'
            ) {
              this.setState({ inputValue: '' });
              this.setState({
                setFleetOpen: true
              });
              this.setState({ openSnackbarPaytype: true });
              this.setState({ selectedPayTypeOption: '' });

              this.fetchFleetPayTypeNames('paytype_fleet');
              $('#select-fleet-account').click();
              //this.setState({ changeFleetOption: false });
            } else {
              this.setState({
                setFleetOpen: false
              });
            }
          }
        );
        rowArray = this.sortedArray(rowArray, 'paytype');
        this.setState({
          selectedPayTypeNamesExist: rowArray
        });

        this.setState({
          selectedFleetError: false
        });
      } else {
        this.setState({ openSnackbarPaytype: false });
        $('#select-fleet-account').click();
        this.setState({
          selectedFleetError: true
        });
      }
    } else if (
      this.state.selectedFleet !== 'All' ||
      this.state.selectedPayTypeOptionType !== 'All'
    ) {
      // console.log('handleAddRecord===3');
      var rowArray = [];
      let found;
      let rowArr;
      if (this.state.searchStatus == 'customer') {
        rowArray = this.state.selectedCustomerNamesExist;
        found = rowArray.some(
          el => el.customerName == this.state.selectedFleet
        );

        rowArr = {
          customerName: this.state.selectedFleet
        };
      } else {
        rowArray = this.state.selectedPayTypeNamesExist;
        //console.log('rowArray', rowArray);
        found = rowArray.some(
          el => el.payTypeName == this.state.selectedPayTypeOptionType
        );

        rowArr = {
          payTypeName: this.state.selectedPayTypeOptionType
        };
      }

      if (found) {
        let items = [];
        if (this.state.searchStatus == 'customer') {
          items = rowArray.filter(
            item => item.customerName !== this.state.selectedFleet
          );
          this.setState({ selectedCustomerNamesExist: items });
          this.setState({ selectedFleet: 'All' });
        } else {
          items = rowArray.filter(
            item => item.payTypeName !== this.state.selectedPayTypeOptionType
          );
          this.setState({ selectedPayTypeNamesExist: items });
          this.setState({ selectedPayTypeOptionType: 'All' });
        }

        let pTypeRemove = '';
        if (this.state.searchStatus == 'customer') {
          pTypeRemove = 'cust_fleet';
        } else if (this.state.searchStatus == 'paytype') {
          pTypeRemove = 'paytype_fleet';
        } else if (this.state.searchStatus == 'opcode') {
          pTypeRemove = 'opcode_fleet';
        }

        let fleetRemove = '';
        if (this.state.searchStatus == 'customer') {
          fleetRemove = this.state.selectedFleet;
        } else if (this.state.searchStatus == 'paytype') {
          fleetRemove = this.state.selectedPayTypeOptionType;
        } else if (this.state.searchStatus == 'opcode') {
          fleetRemove = this.state.selectedOpcodeOptionType;
        }
        this.setState({ removeBtnClick: true });
        insertFleetNameToList(
          fleetRemove,
          'delete',
          pTypeRemove,
          this.state.storeId,
          result => {
            // console.log(
            //   'cccaaa===',
            //   result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
            //     .statelessCcPhysicalRwKpiFleetMasters[0].fleetName,
            //   '===',
            //   items
            // );
            if (
              result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
                .statelessCcPhysicalRwKpiFleetMasters[0].fleetName == 'Success'
            ) {
              this.setState({ removeBtnClick: false });
              this.setState({ inputValueSelected: '' });
              if (this.state.searchStatus == 'customer') {
                this.setState({ selectedFleet: 'All' });
                this.fetchFleetCustomerNames();
                this.setState({ openSnackbarAccountDelete: true });
              } else if (this.state.searchStatus == 'paytype') {
                this.setState({ selectedPayTypeOptionType: 'All' });
                //this.setState({ selectedPayTypeNames: 'All' });

                this.fetchFleetPayTypeNames('paytype_fleet');
                //this.fetchFleetPayTypeNames('paytype_fleet_exists');
                //this.setState({ selectedPayTypeNames: 'All' });
                this.fetchFleetPayTypeNames('paytype_fleet');
                this.setState({ openSnackbarPaytypeDelete: true });
              } else if (this.state.searchStatus == 'opcode') {
                this.setState({ selectedOpcodeOptionType: 'All' });
                //this.setState({ selectedPayTypeNames: 'All' });

                this.fetchFleetOpcodeNames('opcode_fleet');
                //this.fetchFleetPayTypeNames('paytype_fleet_exists');
                //this.setState({ selectedPayTypeNames: 'All' });
                this.setState({ openSnackbarOpcodeDelete: true });
              }
              this.setState({
                setFleetOpen: true
              });
              $('#select-fleet-account').click();
              this.setState({ addNewRow: false });
              this.clearLaborGrid();
              this.clearPartsGrid();
              this.getAgGridData();
              // this.setState({ changeFleetOption: false });
            } else {
              this.setState({
                setFleetOpen: false
              });
            }
          }
        );
        // this.setState({
        //   selectedFleetNames: rowArray
        // });
        this.setState({
          selectedFleetError: false
        });
      } else {
        $('#select-fleet-account').click();
        this.setState({
          selectedFleetError: true
        });
      }
    } else if (
      this.state.selectedOpcodeOption !== '' &&
      this.state.searchStatus == 'opcode'
    ) {
      //console.log('handleAddRecord===4', this.state.selectedOpcodeOption);
      var rowArray = this.state.selectedOpcodeNamesExist;
      let found = rowArray.some(
        el => el.opcodeName == this.state.selectedOpcodeOption
      );
      let rowArr = {
        opcodeName: this.state.selectedOpcodeOption
      };

      if (!found) {
        rowArray.push(rowArr);

        //console.log('rowArray', rowArray);

        insertFleetNameToList(
          this.state.selectedOpcodeOption,
          'insert',
          'opcode_fleet',
          this.state.storeId,
          result => {
            // console.log(
            //   'cccaaa',
            //   result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
            //     .statelessCcPhysicalRwKpiFleetMasters[0].fleetName
            // );
            if (
              result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
                .statelessCcPhysicalRwKpiFleetMasters[0].fleetName == 'Success'
            ) {
              this.setState({ inputValue: '' });
              this.setState({
                setFleetOpen: true
              });
              this.setState({ openSnackbarOpcode: true });
              this.setState({ selectedOpcodeOption: '' });

              this.fetchFleetOpcodeNames('opcode_fleet');
              $('#select-fleet-account').click();
            } else {
              this.setState({
                setFleetOpen: false
              });
            }
          }
        );
        rowArray = this.sortedArray(rowArray, 'opcode');
        this.setState({
          selectedOpcodeNamesExist: rowArray
        });

        this.setState({
          selectedFleetError: false
        });
      } else {
        this.setState({ openSnackbarOpcode: false });
        $('#select-fleet-account').click();
        this.setState({
          selectedFleetError: true
        });
      }
    } else if (
      this.state.selectedFleet !== 'All' ||
      this.state.selectedOpcodeOptionType !== 'All'
    ) {
      //console.log('handleAddRecord===5', this.state.selectedOpcodeOption);
      var rowArray = [];
      let found;
      let rowArr;
      if (this.state.searchStatus == 'customer') {
        rowArray = this.state.selectedCustomerNamesExist;
        found = rowArray.some(
          el => el.customerName == this.state.selectedFleet
        );

        rowArr = {
          customerName: this.state.selectedFleet
        };
      } else {
        rowArray = this.state.selectedOpcodeNamesExist;
        //console.log('rowArray', rowArray);
        found = rowArray.some(
          el => el.opcodeName == this.state.selectedOpcodeOptionType
        );

        rowArr = {
          opcodeName: this.state.selectedOpcodeOptionType
        };
      }

      if (found) {
        let items = [];
        if (this.state.searchStatus == 'customer') {
          items = rowArray.filter(
            item => item.customerName !== this.state.selectedFleet
          );
          this.setState({ selectedCustomerNamesExist: items });
          this.setState({ selectedFleet: 'All' });
        } else {
          items = rowArray.filter(
            item => item.opcodeName !== this.state.selectedOpcodeOptionType
          );
          this.setState({ selectedOpcodeNamesExist: items });
          this.setState({ selectedOpcodeOptionType: 'All' });
        }

        let pTypeRemove = '';
        if (this.state.searchStatus == 'customer') {
          pTypeRemove = 'cust_fleet';
        } else if (this.state.searchStatus == 'paytype') {
          pTypeRemove = 'paytype_fleet';
        } else if (this.state.searchStatus == 'opcode') {
          pTypeRemove = 'opcode_fleet';
        }

        let fleetRemove = '';
        if (this.state.searchStatus == 'customer') {
          fleetRemove = this.state.selectedFleet;
        } else if (this.state.searchStatus == 'paytype') {
          fleetRemove = this.state.selectedPayTypeOptionType;
        } else if (this.state.searchStatus == 'opcode') {
          fleetRemove = this.state.selectedOpcodeOptionType;
        }
        if (fleetRemove == 'All') {
          return false;
        }
        this.setState({ removeBtnClick: true });
        insertFleetNameToList(
          fleetRemove,
          'delete',
          pTypeRemove,
          this.state.storeId,
          result => {
            // console.log(
            //   'cccaaa===',
            //   result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
            //     .statelessCcPhysicalRwKpiFleetMasters[0].fleetName,
            //   '===',
            //   items
            // );
            if (
              result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
                .statelessCcPhysicalRwKpiFleetMasters[0].fleetName == 'Success'
            ) {
              this.setState({ removeBtnClick: false });
              this.setState({ inputValueSelected: '' });
              if (this.state.searchStatus == 'customer') {
                this.setState({ selectedFleet: 'All' });
                this.fetchFleetCustomerNames();
                this.setState({ openSnackbarAccountDelete: true });
              } else if (this.state.searchStatus == 'paytype') {
                this.setState({ selectedPayTypeOptionType: 'All' });
                //this.setState({ selectedPayTypeNames: 'All' });

                this.fetchFleetPayTypeNames('paytype_fleet');
                //this.fetchFleetPayTypeNames('paytype_fleet_exists');
                //this.setState({ selectedPayTypeNames: 'All' });
                this.fetchFleetPayTypeNames('paytype_fleet');
                this.setState({ openSnackbarPaytypeDelete: true });
              } else if (this.state.searchStatus == 'opcode') {
                this.setState({ selectedOpcodeOptionType: 'All' });
                //this.setState({ selectedPayTypeNames: 'All' });

                this.fetchFleetOpcodeNames('opcode_fleet');
                //this.fetchFleetPayTypeNames('paytype_fleet_exists');
                //this.setState({ selectedPayTypeNames: 'All' });
                this.setState({ openSnackbarOpcodeDelete: true });
              }
              this.setState({
                setFleetOpen: true
              });
              $('#select-fleet-account').click();
              this.setState({ addNewRow: false });
              this.clearLaborGrid();
              this.clearPartsGrid();
              this.getAgGridData();
            } else {
              this.setState({
                setFleetOpen: false
              });
            }
          }
        );
        // this.setState({
        //   selectedFleetNames: rowArray
        // });
        this.setState({
          selectedFleetError: false
        });
      } else {
        $('#select-fleet-account').click();
        this.setState({
          selectedFleetError: true
        });
      }
    }
  };
  createFleetAccount = event => {
    if (
      this.state.selectedFleet != 'All' ||
      this.state.selectedPayTypeOptionType != 'All' ||
      this.state.selectedOpcodeOptionType != 'All'
    ) {
      this.setState({ addNewFleet: true });
      this.setState({ addNewRow: true });

      if (this.props.history && this.props.history.location) {
        if (
          this.props.history.location.state &&
          this.props.history.location.state.gridType == 'customer'
        ) {
          this.setState({
            fleetSelected: this.props.history.location.state.customerName
          });
        } else if (
          this.props.history.location.state &&
          this.props.history.location.state.gridType == 'paytype'
        ) {
          this.setState({
            fleetSelected: this.props.history.location.state.selectedPayType
          });
        } else if (
          this.props.history.location.state &&
          this.props.history.location.state.gridType == 'opcode'
        ) {
          this.setState({
            fleetSelected: this.props.history.location.state.selectedOpcode
          });
        }
      }
    } else {
      this.setState({ fleetNameError: true });
    }
  };
  handleRadioChange = event => {
    this.setState({ gridRateValue: event.target.value });
    //console.log('sssss===', event.target.value);
    if (event.target.value == 'fixedRate') {
      this.setState({ fixedRate: true });
      this.setState({ laborGrid: false });
    } else {
      this.setState({ laborGrid: true });
      this.setState({ fixedRate: false });
    }
  };
  handleRadioChangeStart = event => {
    this.setState({ inputValue: '' });
    this.setState({ inputValueSelected: '' });
    this.setState({ radioCheckedDefault: false });
    this.setState({ gridRateValueStart: event.target.value });
    this.setState({ searchStatus: event.target.value });
    this.setState({ selectedFleet: 'All' });
    this.setState({ selectedPayTypeOptionType: 'All' });
    this.setState({ selectedCustomer: '' });
    this.setState({ selectedPayTypeOption: '' });
    this.setState({ selectedOpcodeOption: '' });
    this.setState({ selectedOpcodeOptionType: 'All' });
    this.setState({ addNewRow: false });
    this.getFleetListItemsList(['All'], event.target.value);
    window.sortStateLbr = {};
    window.filterStateLbr = {};
    window.sortStatePrts = {};
    window.filterStatePrts = {};
    this.setState({
      editedRowId: null,
      isCodeEdited: false
      // changeFleetOption: false
    });
    this.setState({
      partsfixedRateError: null
    });

    //this.setState({ gridRateValue: event.target.value });
    // if (event.target.value == 'customerType') {
    //   this.setState({ fixedRate: true });
    //   this.setState({ laborGrid: false });
    // } else {
    //   this.setState({ laborGrid: true });
    //   this.setState({ fixedRate: false });
    // }
  };
  handleMatrixChange = event => {
    this.setState({ matrixRateValue: event.target.value, matrixChanged: true });

    if (event.target.value == 'partsFixedRate') {
      this.setState({ partsFixedRate: true });
      this.setState({ partsMatrix: false });
    } else {
      this.setState({ partsMatrix: true });
      this.setState({ partsFixedRate: false });
    }
  };
  handleCancel = event => {
    this.setState({ addNewRow: false });
  };
  handleMouseLeaveLeft = event => {
    let leftTextValue = document.getElementById('select-all-account').value;
    if (this.state.searchStatus == 'customer') {
      this.setState({ selectedCustomer: leftTextValue });
    } else {
      this.setState({ selectedPayTypeOption: leftTextValue });
    }
  };
  handleMouseLeaveRight = event => {
    let leftTextValue = document.getElementById('select-fleet-account').value;
    if (this.state.searchStatus == 'customer') {
      this.setState({ selectedFleet: leftTextValue });
    } else {
      this.setState({ selectedPayTypeOptionType: leftTextValue });
    }
  };
  handleChange = (event, newValue) => {
    //this.setState({ changeFleetOption: true });
    // check if value prsent in the arra
    this.setState({ inputValueSelected: '' });
    this.setState({ inputValue: newValue });
    if (this.state.searchStatus == 'customer') {
      const isPresent = loadash.some(this.state.customerNames, {
        customerName: newValue
      });
      if (isPresent == false) {
        return false;
      }
    } else if (this.state.searchStatus == 'paytype') {
      const isPresent = loadash.some(this.state.selectedPayTypeNames, {
        payTypeName: newValue
      });
      if (isPresent == false) {
        return false;
      }
    } else {
      const isPresent = loadash.some(this.state.selectedOpcodeNames, {
        opcodeName: newValue
      });
      if (isPresent == false) {
        return false;
      }
    }
    if (this.state.searchStatus == 'customer') {
      this.setState({ selectedCustomer: newValue });
      // }
    } else if (this.state.searchStatus == 'paytype') {
      this.setState({ selectedPayTypeOption: newValue });
    } else {
      this.setState({ selectedOpcodeOption: newValue });
    }
    this.setState({ selectedOpcodeOptionType: false });
    this.setState({ selectedPayTypeOptionType: false });
    this.setState({ selectedFleetError: false });
    this.setState({ selectedFleet: 'All' });
    this.setState({ selectedPayTypeOptionType: 'All' });
    this.setState({ selectedPayTypeOptionType: 'All' });
    this.setState({ addNewRow: false });
  };

  handleDateChangeLabor = e => {
    const { value } = e.target;
    const isValid = moment(value, 'MM/DD/YY', true).isValid();
    if (!isValid) {
      this.setState({
        gridInstalledDateErrorLabor: true
      });
    } else {
      this.setState({
        gridInstalledDateErrorLabor: false
      });
    }
  };
  handleDateChangeParts = e => {
    const { value } = e.target;
    const isValid = moment(value, 'MM/DD/YY', true).isValid();
    if (!isValid) {
      this.setState({
        gridInstalledDateErrorParts: true
      });
    } else {
      this.setState({
        gridInstalledDateErrorParts: false
      });
    }
  };

  handleInputChange = event => {
    this.setState({ inputValue: event.target.value });
  };

  handleClearInput = () => {
    this.setState({ inputValue: '' });
  };

  handleInputChangeSelected = event => {
    let selectedFleetValues =
      this.state.searchStatus === 'customer'
        ? this.state.selectedFleet
        : this.state.searchStatus === 'paytype'
        ? this.state.selectedPayTypeOptionType
        : this.state.selectedOpcodeOptionType;

    if (
      event.target.value === '' &&
      selectedFleetValues !== '' &&
      selectedFleetValues !== 'All'
    ) {
      //this.setState({ inputValueSelected: selectedFleetValues });
      this.resetFleet();
    } else {
      if (this.state.filteredOptionsSelected.includes(event.target.value)) {
        // alert('iii');
        this.handleChangeFleet(event, event.target.value);
      }
      this.setState({ inputValueSelected: event.target.value });
    }
  };

  handleClearInputSelected = () => {
    this.resetFleet();
  };

  updateFilteredOptions = () => {
    const {
      searchStatus,
      selectedPayTypeNames,
      selectedOpcodeNames
    } = this.state;

    let filteredOptions = [];

    const trimmedInputValue =
      this.state.inputValue && this.state.inputValue.trim().toLowerCase(); // Trim and convert to lowercase once

    if (
      searchStatus === 'customer' &&
      Array.isArray(this.state.customerNames)
    ) {
      filteredOptions = this.state.customerNames
        .filter(customer => customer && customer.customerName)
        .map(customer => customer.customerName)
        .filter(option => option.toLowerCase().includes(trimmedInputValue));

      // Check if the selected input matches any option
      const isValidOption = filteredOptions.includes(
        this.state.inputValue && this.state.inputValue.trim()
      );
      if (isValidOption) {
        filteredOptions = this.state.customerNames
          .filter(customer => customer && customer.customerName)
          .map(customer => customer.customerName);
      }
    } else if (
      searchStatus === 'paytype' &&
      Array.isArray(selectedPayTypeNames)
    ) {
      filteredOptions = selectedPayTypeNames
        .filter(payType => payType && payType.payTypeName)
        .map(payType => payType.payTypeName)
        .filter(option => option.toLowerCase().includes(trimmedInputValue));

      // Check if the selected input matches any option
      const isValidOption = filteredOptions.includes(
        this.state.inputValue && this.state.inputValue.trim()
      );
      if (isValidOption) {
        filteredOptions = selectedPayTypeNames
          .filter(payType => payType && payType.payTypeName)
          .map(payType => payType.payTypeName);
      }
    } else if (
      searchStatus === 'opcode' &&
      Array.isArray(selectedOpcodeNames)
    ) {
      filteredOptions = selectedOpcodeNames
        .filter(opcode => opcode && opcode.opcodeName)
        .map(opcode => opcode.opcodeName)
        .filter(option => option.toLowerCase().includes(trimmedInputValue));

      // Check if the selected input matches any option
      const isValidOption = filteredOptions.includes(
        this.state.inputValue && this.state.inputValue.trim()
      );
      if (isValidOption) {
        filteredOptions = selectedOpcodeNames
          .filter(opcode => opcode && opcode.opcodeName)
          .map(opcode => opcode.opcodeName);
      }
    }

    this.setState({ filteredOptions });
  };
  updateFilteredOptionsSelectedNormal = () => {
    const {
      searchStatus,
      selectedCustomerNamesExist,
      selectedPayTypeNamesExist,
      selectedOpcodeNamesExist
    } = this.state;

    let filteredOptionsSelected = [];
    const inputValueTrimmed =
      this.state.inputValueSelected &&
      this.state.inputValueSelected.trim().toLowerCase();

    if (
      searchStatus === 'customer' &&
      Array.isArray(selectedCustomerNamesExist)
    ) {
      filteredOptionsSelected = selectedCustomerNamesExist
        .filter(customer => customer && customer.customerName)
        .map(customer => customer.customerName)
        .filter(option => option.toLowerCase());

      const isValidOption = filteredOptionsSelected;
      if (isValidOption) {
        filteredOptionsSelected = selectedCustomerNamesExist
          .filter(customer => customer && customer.customerName)
          .map(customer => customer.customerName);
      }
    } else if (
      searchStatus === 'paytype' &&
      Array.isArray(selectedPayTypeNamesExist)
    ) {
      filteredOptionsSelected = selectedPayTypeNamesExist
        .filter(payType => payType && payType.payTypeName)
        .map(payType => payType.payTypeName)
        .filter(option => option.toLowerCase());

      const isValidOption = filteredOptionsSelected;
      if (isValidOption) {
        filteredOptionsSelected = selectedPayTypeNamesExist
          .filter(payType => payType && payType.payTypeName)
          .map(payType => payType.payTypeName);
      }
    } else if (
      searchStatus === 'opcode' &&
      Array.isArray(selectedOpcodeNamesExist)
    ) {
      filteredOptionsSelected = selectedOpcodeNamesExist
        .filter(opcode => opcode && opcode.opcodeName)
        .map(opcode => opcode.opcodeName)
        .filter(option => option.toLowerCase());

      const isValidOption = filteredOptionsSelected;
      if (isValidOption) {
        filteredOptionsSelected = selectedOpcodeNamesExist
          .filter(opcode => opcode && opcode.opcodeName)
          .map(opcode => opcode.opcodeName);
      }
    }

    this.setState({
      filteredOptionsSelected
    });
  };
  updateFilteredOptionsSelected = () => {
    const {
      searchStatus,
      selectedCustomerNamesExist,
      selectedPayTypeNamesExist,
      selectedOpcodeNamesExist
    } = this.state;

    let filteredOptionsSelected = [];
    const inputValueTrimmed =
      this.state.inputValueSelected &&
      this.state.inputValueSelected.trim().toLowerCase();

    if (
      searchStatus === 'customer' &&
      Array.isArray(selectedCustomerNamesExist)
    ) {
      filteredOptionsSelected = selectedCustomerNamesExist
        .filter(customer => customer && customer.customerName)
        .map(customer => customer.customerName)
        .filter(option => option.toLowerCase().includes(inputValueTrimmed));

      const isValidOption = filteredOptionsSelected.includes(inputValueTrimmed);
      if (isValidOption) {
        filteredOptionsSelected = selectedCustomerNamesExist
          .filter(customer => customer && customer.customerName)
          .map(customer => customer.customerName);
      }
    } else if (
      searchStatus === 'paytype' &&
      Array.isArray(selectedPayTypeNamesExist)
    ) {
      filteredOptionsSelected = selectedPayTypeNamesExist
        .filter(payType => payType && payType.payTypeName)
        .map(payType => payType.payTypeName)
        .filter(option => option.toLowerCase().includes(inputValueTrimmed));

      const isValidOption = filteredOptionsSelected.includes(inputValueTrimmed);
      if (isValidOption) {
        filteredOptionsSelected = selectedPayTypeNamesExist
          .filter(payType => payType && payType.payTypeName)
          .map(payType => payType.payTypeName);
      }
    } else if (
      searchStatus === 'opcode' &&
      Array.isArray(selectedOpcodeNamesExist)
    ) {
      filteredOptionsSelected = selectedOpcodeNamesExist
        .filter(opcode => opcode && opcode.opcodeName)
        .map(opcode => opcode.opcodeName)
        .filter(option => option.toLowerCase().includes(inputValueTrimmed));

      const isValidOption = filteredOptionsSelected.includes(inputValueTrimmed);
      if (isValidOption) {
        filteredOptionsSelected = selectedOpcodeNamesExist
          .filter(opcode => opcode && opcode.opcodeName)
          .map(opcode => opcode.opcodeName);
      }
    }

    this.setState({
      filteredOptionsSelected
    });
  };

  resetFleet = () => {
    this.onBtStopEditing(this.state.laborEditIndex);

    let rowData = this.state.oldDataArrParts;

    if (this.state.rawGridApiParts && this.state.partsEditIndex !== undefined) {
      const rowNode = this.state.rawGridApiParts.getDisplayedRowAtIndex(
        this.state.partsEditIndex
      );

      if (rowNode) {
        rowNode.setDataValue('partsmarkup', rowData[0].partsmarkup);

        this.state.rawGridApiParts.refreshCells({
          columns: ['partsmarkup'],
          rowNodes: [rowNode],
          force: true
        });
      } else {
        // Log an error if rowNode is undefined
        // console.error('Row node is undefined or not found.');
      }
    } else {
      // Log an error if the grid API is not initialized or index is invalid
      // console.error(
      //   'Grid API is not initialized or partsEditIndex is invalid:',
      //   this.state.partsEditIndex
      // );
    }

    // Stop editing at the specified index for parts (if applicable)
    this.onBtStopEditingParts(this.state.partsEditIndex);
    this.getFleetListItemsList(['All'], this.state.searchStatus);

    this.setState({
      searchStatus: this.state.searchStatus,
      gridRateValueStart: this.state.searchStatus,
      selectedCustomer: '',
      selectedPayTypeOption: '',
      selectedOpcodeOption: '',
      selectedFleet: 'All',
      selectedPayTypeOptionType: 'All',
      selectedOpcodeOptionType: 'All',
      editedRowId: null,
      isCodeEdited: false,
      markupChanged: false,
      fixedLaborRate: '',
      changeFleetOption: false,
      laborEditIndex: '',
      laborEdit: false,
      partsEditIndex: '',
      partsEdit: false,
      gridInstalledDateErrorParts: false,
      gridInstalledDateErrorLabor: false
    });
    this.setState({
      partsfixedRateError: null
    });

    this.setState({
      editedRowId: null,
      isCodeEdited: false
      // changeFleetOption: false
    });
    // // Reset column state for both grids
    // if (this.columnApiLabor) {
    //   this.columnApiLabor.resetColumnState();
    // }

    // if (this.columnApiParts) {
    //   this.columnApiParts.resetColumnState();
    // }

    this.resetRawData();

    // Redraw rows for both grids
    this.gridApiLabor.redrawRows();
    this.gridApiParts.redrawRows();

    // Reset sort and filter models for both grids
    this.gridApiLabor.setSortModel(null);
    this.gridApiParts.setSortModel(null);
    this.gridApiLabor.setFilterModel(null);
    this.gridApiParts.setFilterModel(null);
    this.gridApiParts.sizeColumnsToFit();
    this.gridApiLabor.sizeColumnsToFit();

    window.sortStateLbr = {};
    window.filterStateLbr = {};
    window.sortStatePrts = {};
    window.filterStatePrts = {};
    this.setState({ inputValue: '' });
    this.setState({ inputValueSelected: '' });
  };

  clearFirstRow = () => {
    this.setState({ typeStatus: false });
    this.setState({ searchStatus: 'customer' });
    this.setState({ responseStatus: false });
    this.setState({ gridRateValueStart: false });
    this.setState({ selectedFleet: '' });
  };
  clearLaborGrid = () => {
    // Batch all the state updates together for efficiency
    this.setState(
      {
        gridInstalledDate: new Date(),
        fixedRate: true,
        gridRateValue: 'fixedRate',
        laborGrid: false,
        fixedLaborRate: '',
        fixedDoorRate: '',
        doorRate: '',
        doorRateChanged: false,
        fixedDoorRateChanged: false,
        fixedRateChanged: false,
        gridInstalledDateErrorLabor: false
      },
      () => {}
    );
  };
  clearPartsGrid = () => {
    this.setState(
      {
        matrixRateValue: 'partsFixedRate',
        partsFixedRate: true,
        partsMatrix: false,
        markupValue: '',
        partsMarkup: 'Markup',
        partsSource: this.state.partsSourceArray[0],
        matrixInstalledDate: new Date(),
        markupChanged: false,
        matrixChanged: false,
        markupValueChanged: false,
        gridInstalledDateErrorParts: false
      },
      () => {}
    );
  };

  showAllSelected = () => {
    this.updateFilteredOptionsSelectedNormal();
  };
  handleChangeFleet = (event, newValue) => {
    this.setState({ inputValue: '' });
    this.setState({ inputValueSelected: newValue });

    //alert('new--' + newValue);
    // this.setState({ changeFleetOption: true });
    if (this.state.searchStatus == 'customer') {
      const isPresent = loadash.some(this.state.selectedCustomerNamesExist, {
        customerName: newValue
      });
      if (isPresent == false) {
        return false;
      }
    } else if (this.state.searchStatus == 'paytype') {
      const isPresent = loadash.some(this.state.selectedPayTypeNamesExist, {
        payTypeName: newValue
      });
      if (isPresent == false) {
        return false;
      }
    } else {
      const isPresent = loadash.some(this.state.selectedOpcodeNamesExist, {
        opcodeName: newValue
      });
      if (isPresent == false) {
        return false;
      }
    }
    this.setState({ fleetSelected: newValue });
    this.setState({ addNewRow: false });

    this.gridApiLabor.redrawRows();
    this.gridApiParts.redrawRows();
    this.clearLaborGrid();
    this.clearPartsGrid();
    this.setState({
      editedRowId: null,
      isCodeEdited: false,
      markupChanged: false,
      fixedLaborRate: '',
      changeFleetOption: false
    });
    if (newValue != null) {
      this.setState({
        setFleetOpen: false
      });
      this.setState({
        selectedFleetError: false
      });
      this.setState({
        selectedCustomer: ''
      });
      this.setState({
        selectedPayTypeOption: ''
      });
      this.setState({
        selectedOpcodeOption: ''
      });
      // this.setState({ selectedFleet: newValue });
      this.gridApiLabor.showLoadingOverlay();
      this.gridApiParts.showLoadingOverlay();

      let selected = [];
      if (newValue != 'All') {
        selected.push(newValue);
      } else {
        selected.push('All');
      }

      let fleettype = '';
      if (this.state.searchStatus == 'customer') {
        fleettype = 'customer';
        this.setState(
          {
            selectedFleet: false
          },
          () => {
            this.setState({ selectedFleet: newValue });
          }
        );
      } else if (this.state.searchStatus == 'opcode') {
        fleettype = 'opcode';
        this.setState(
          {
            selectedOpcodeOptionType: false
          },
          () => {
            this.setState({ selectedOpcodeOptionType: newValue });
          }
        );
      } else {
        fleettype = 'paytype';
        this.setState(
          {
            selectedPayTypeOptionType: false
          },
          () => {
            this.setState({ selectedPayTypeOptionType: newValue });
          }
        );
      }
      this.setState({ loader: true });
      getFleetListItems(selected, fleettype, this.state.storeId, result => {
        let data = result;

        this.setState({
          isLoading: true
        });

        setTimeout(() => {
          let resultArr = [];
          let laborArr = [];
          let partsArr = [];
          if (
            result.data.statelessCcPhysicalRwGetLaborAndPartsFleetAccount.nodes
              .length > 0
          ) {
            this.setState({ loader: false });
            result.data.statelessCcPhysicalRwGetLaborAndPartsFleetAccount.nodes.map(
              (obj, i) => {
                if (obj.laborOrParts == 'labor') {
                  laborArr.push(obj);
                } else {
                  partsArr.push(obj);
                }
              }
            );
            this.setState({
              fleetLaborArr: laborArr
            });
            // se
            this.setState({
              fleetPartsArr: partsArr
            });
            this.setState({
              isLoading: false
            });

            if (laborArr == '' && partsArr == '') {
              this.gridApiLabor.showLoadingOverlay();
              this.gridApiParts.showLoadingOverlay();
            } else if (laborArr != '' && partsArr == '') {
              this.gridApiLabor.hideOverlay();
              this.gridApiParts.showLoadingOverlay();
            } else if (laborArr == '' && partsArr != '') {
              this.gridApiLabor.showLoadingOverlay();
              this.gridApiParts.hideOverlay();
            } else if (laborArr != '' && partsArr != '') {
              this.gridApiLabor.hideOverlay();
              this.gridApiParts.hideOverlay();
            }

            // if (laborArr != []) {
            //   this.gridApiParts.hideOverlay();
            // } else if (partsArr != []) {
            //   this.gridApiLabor.hideOverlay();
            // } else {
            //   this.gridApiLabor.hideOverlay();
            //   this.gridApiParts.hideOverlay();
            // }

            // this.gridApiLabor.redrawRows();
            // this.gridApiParts.redrawRows();
            // setFleetsParts(partsArr);

            // setFleets(
            //   result.data.statelessCcPhysicalRwGetLaborAndPartsFleetAccount.nodes
            // );
          } else {
            this.setState({ loader: false });
            // this.gridApiLabor.hideOverlay();
            // this.gridApiParts.hideOverlay();
            this.setState({
              fleetLaborArr: []
            });
            this.setState({
              fleetPartsArr: []
            });
            this.setState({
              isLoading: false
            });
          }
        }, 550);
      });
    }
  };
  hidesnackbar = () => {
    this.setState({ openSnackbar: false });
  };

  hidesnackbarAccount = () => {
    this.setState({ openSnackbarAccount: false });
  };
  hidesnackbarAccountDelete = () => {
    this.setState({ openSnackbarAccountDelete: false });
  };

  hidesnackbarPaytype = () => {
    this.setState({ openSnackbarPaytype: false });
  };

  hidesnackbarPaytypeDelete = () => {
    this.setState({ openSnackbarPaytypeDelete: false });
  };
  hidesnackbarOpcode = () => {
    this.setState({ openSnackbarOpcode: false });
  };

  hidesnackbarOpcodeDelete = () => {
    this.setState({ openSnackbarOpcodeDelete: false });
  };
  hidesnackbarEdit = () => {
    this.setState({ openSnackbarEdit: false });
  };
  hidesnackbarDelete = () => {
    this.setState({ openSnackbarDeleteFixedRate: false });
    this.setState({ openSnackbarDeleteLaborGrid: false });
    this.setState({ openSnackbarDeletePartsMatrix: false });
  };
  hidesnackbarError = () => {
    this.setState({ openSnackbarError: false });
  };
  caseInsensitiveSort = (a, b) => {
    return a.toLowerCase().localeCompare(b.toLowerCase());
  };
  handleSave = () => {
    let laborArr = [];
    let errorArr = [];
    let typeChecking = '';
    if (this.state.searchStatus == 'customer') {
      typeChecking = this.state.selectedFleet;
    } else if (this.state.searchStatus == 'paytype') {
      typeChecking = this.state.selectedPayTypeOptionType;
    } else {
      typeChecking = this.state.selectedOpcodeOptionType;
    }
    if (typeChecking == '' || typeChecking == 'All') {
      errorArr.push('fleetNameError');
    }

    this.setState({
      errorItems: errorArr
    });

    let formattedPartsValue =
      this.state.markupValue != null
        ? parseFloat(this.state.markupValue).toFixed(2)
        : '';
    let formattedLaborValue =
      this.state.fixedLaborRate != null
        ? parseFloat(this.state.fixedLaborRate).toFixed(2)
        : '';
    let doorVal = '';
    if (this.state.laborGrid == true) {
      doorVal = this.state.doorRate;
    } else {
      doorVal = this.state.fixedDoorRate;
    }

    // both side fixed rate taken

    let fleet_rate_grid = [];
    let fleet_rate_parts = [];
    let fleet_exists_labor_grid = [];
    let fleet_exists_parts_matrix = [];

    let markup = '';
    if (this.state.partsMatrix == true) {
      markup = this.state.partsMarkup;
    } else {
      markup = this.state.markupValue
        ? this.state.partsMarkup + ' ' + formattedPartsValue
        : this.state.partsMarkup;
    }

    let process = 'insert';
    let fleettype =
      this.state.searchStatus == 'customer'
        ? 'customer'
        : this.state.searchStatus == 'paytype'
        ? 'paytype'
        : 'opcode';
    let fleetname =
      this.state.searchStatus == 'customer'
        ? this.state.selectedFleet
        : this.state.searchStatus == 'paytype'
        ? this.state.selectedPayTypeOptionType
        : this.state.selectedOpcodeOptionType;
    if (this.state.fixedRate == true) {
      if (
        this.state.gridInstalledDate != '' &&
        (this.state.fixedLaborRate != '' || this.state.doorRate != '')
      ) {
        fleet_rate_grid.push({
          process: process,
          fleettype: fleettype,
          fleetname: fleetname,
          fleetorgridflag: 'Fixed Rate',
          laborfleetrate:
            this.state.fixedLaborRate == '' ? 0 : formattedLaborValue,
          gridinstalldate:
            this.state.gridInstalledDate == ''
              ? null
              : moment(this.state.gridInstalledDate).format('YYYY-MM-DD')
        });
      }
    }
    if (this.state.laborGrid == true) {
      // alert('laborgrid');
    }
    const element = document.getElementById('outlined-password-input');
    if (this.state.partsFixedRate == true) {
      if (
        this.state.matrixInstalledDate != '' &&
        (this.state.markupChanged ||
          this.state.matrixChanged ||
          this.state.markupValueChanged)
      ) {
        fleet_rate_parts.push({
          process: process,
          fleettype: fleettype,
          fleetname: fleetname,
          fleetormatrixflag: 'Fixed Rate',
          partsmarkup: markup,
          partsource: null,
          matrixinstalldate:
            this.state.matrixInstalledDate == ''
              ? null
              : moment(this.state.matrixInstalledDate).format('YYYY-MM-DD')
        });
      }
    }
    if (this.state.partsMatrix == true) {
      //alert('matrix');
    }

    // process: insert/update/delete
    // 							fleettype: customer/paytype
    // 							fleetname: <Fleet Name/Pay Type Name>
    // 							fleetorgridflag: fleet/grid
    // 							laborfleetrate: <Fleet Rate>
    // 							gridinstalldate: <Fleet Install Date></Fleet>

    // if (
    //   this.state.gridInstalledDate != '' &&
    //   (this.state.fixedLaborRate != '' || this.state.doorRate != '')
    // ) {
    //   laborArr.push({
    //     process: 'insert',
    //     fleetname: this.state.selectedFleet,
    //     laborgridornot: this.state.laborGrid == true ? 1 : 0,
    //     laborfixedrate:
    //       this.state.fixedLaborRate == '' ? 0 : this.state.fixedLaborRate,
    //     doorrate: doorVal == '' ? 0 : doorVal,
    //     gridinstalldate:
    //       this.state.gridInstalledDate == ''
    //         ? null
    //         : moment(this.state.gridInstalledDate).format('YYYY-MM-DD')
    //   });
    // }

    //let partsArr = [];
    // let markup = '';
    // if (this.state.partsMatrix == true) {
    //   markup = this.state.partsMarkup;
    // } else {
    //   markup = this.state.partsMarkup + '' + this.state.markupValue;
    // }

    // if (
    //   this.state.matrixInstalledDate != '' &&
    //   (this.state.markupChanged ||
    //     this.state.matrixChanged ||
    //     this.state.markupValueChanged)
    // ) {
    //   partsArr.push({
    //     process: 'insert',
    //     fleetname: this.state.selectedFleet,
    //     partsmatrixornot: this.state.partsMatrix == true ? 1 : 0,
    //     partsmarkup: markup,
    //     partsource: this.state.partsSource,
    //     matrixinstalldate:
    //       this.state.matrixInstalledDate == ''
    //         ? null
    //         : moment(this.state.matrixInstalledDate).format('YYYY-MM-DD')
    //   });
    // }

    if (errorArr.length <= 0) {
      insertFleetDetails(
        JSON.stringify(fleet_rate_grid),
        JSON.stringify(fleet_rate_parts),
        JSON.stringify(fleet_exists_labor_grid),
        JSON.stringify(fleet_exists_parts_matrix),
        result => {
          if (
            result.data.statelessCcPhysicalRwInsertOrUpdateFleetAccount
              .results[0].status == 1
          ) {
            this.getAgGridData();
            this.gridApiLabor.redrawRows();
            this.gridApiParts.redrawRows();
            this.clearLaborGrid();
            this.clearPartsGrid();
            this.setState({
              openSnackbarSuccessMsg:
                result.data.statelessCcPhysicalRwInsertOrUpdateFleetAccount
                  .results[0].msg
            });
            this.setState({ openSnackbar: true });
            this.setState({
              editedRowId: null,
              isCodeEdited: false,
              markupChanged: false,
              fixedLaborRate: '',
              changeFleetOption: false
            });
          } else {
            this.setState({ openSnackbarError: true });
            this.setState({
              openSnackbarErrorMsg:
                result.data.statelessCcPhysicalRwInsertOrUpdateFleetAccount
                  .results[0].msg
            });

            this.setState({
              markupValue: '',
              partsMarkup: 'Markup',
              markupChanged: false,
              fixedLaborRate: '',
              doorRate: ''
            });
          }
        }
      );
    }

    // insertFleetDetails(
    //   JSON.stringify(laborArr),
    //   JSON.stringify(partsArr),
    //   result => {
    //     console.log('fff--', result);
    //   }
    // );
  };
  handleChangePayType = event => {
    this.setState({ selectedPayType: event.target.value }, function() {
      this.getAgGridData();
    });
  };
  handleCallback = (start, end, label) => {
    this.setState({
      gridInstalledDate: start.format('MM/DD/YY')
    });
    this.setState({
      gridInstalledDateErrorLabor: false
    });
  };
  handleCallbackParts = (start, end, label) => {
    this.setState({
      matrixInstalledDate: start.format('MM/DD/YY')
    });
    this.setState({
      gridInstalledDateErrorParts: false
    });
  };
  onCellClicked = params => {
    const id = localStorage.getItem('oldId');
    let rowId = this.state.editedRowId;
    // console.log(
    //   'rowId===',
    //   rowId,
    //   '====',
    //   id,
    //   '==',
    //   params.data.id,
    //   '=',
    //   params.data.id != id
    // );
    if (params.data.id != id) {
      //console.log('enter=1');
      $(`.cancel-button`).hide();
      $(`.edit-button`).show();
      $(`.view-button`).show();
      $(`.update-button`).hide();
      $(`#btncancel` + rowId).click();
      $('.laborFixedRateValue').hide();
      $('.doorRateValues').hide();
      $('.doorRateValue').hide();
      $('.fixedRateValue').hide();
      $('.laborFixedRate').attr('disabled', 'disabled');
    } else {
      //console.log('enter=12');
      $(`#btncancel${rowId}`).show();
      $(`#btnupdate${rowId}`).show();
      $(`#btnedit${rowId}`).hide();
    }
  };
  onRowEditingStarted(params) {
    //console.log('pa=pa=', params.data.laborgridOrNot);
    if (params.data.laborgridOrNot == 1) {
      params.api.refreshCells({
        columns: ['doorRate'],
        rowNodes: [params.node],
        force: true
      });
    } else {
      params.api.refreshCells({
        columns: ['laborFixedRate', 'doorRate'],
        rowNodes: [params.node],
        force: true
      });
    }
  }

  onFilterChangedLabor = e => {
    const filterValues = e.api.getFilterModel();
    this.gridApiLabor.redrawRows();
  };
  onSortChangedLabor = e => {
    if (this.state.partsEdit == true) {
      this.resetFleet();
    } else {
      this.gridApiLabor.redrawRows();
    }
  };
  onFilterChangedParts = e => {
    const filterValues = e.api.getFilterModel();
    this.gridApiParts.redrawRows();
  };
  onSortChangedParts = e => {
    if (this.state.laborEdit == true) {
      this.resetFleet();
    } else {
      this.gridApiParts.redrawRows();
    }
  };
  onRowEditingStopped(params) {
    params.api.refreshCells({
      columns: ['laborFixedRate', 'doorRate'],
      rowNodes: [params.node],
      force: true
    });
  }
  onLaborFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      laborFixedRateOld: oldVal
    });
    this.setState({
      laborFixedRateNew: newVal
    });
  };
  onDoorRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      doorRateOld: oldVal
    });
    this.setState({
      doorRateNew: newVal
    });
  };
  partsMatrixPopup = event => {
    // event.preventDefault();
    // console.log('showPartsMatrix===122=', this.state.showPartsMatrix);
    this.setState({
      showPartsMatrix: !this.state.showPartsMatrix
    });
    if (this.state.showPartsMatrix == false) {
      this.setState({ matrixRateValue: 'partsMatrix', matrixChanged: true });
      this.setState({ partsMatrix: true });
      this.setState({ partsFixedRate: false });
    }
    event.preventDefault();
  };
  partsMatrixPopupClose = event => {
    //console.log('showPartsMatrix===122=3==', this.state.showPartsMatrix);
    this.setState({
      showPartsMatrix: !this.state.showPartsMatrix
    });
    this.setState({ matrixRateValue: 'partsFixedRate', matrixChanged: false });
    this.setState({ partsFixedRate: true });
    this.setState({ partsMatrix: false });
  };
  laborGridPopup = event => {
    this.setState({
      showLaborGrid: !this.state.showLaborGrid
    });
    if (this.state.showLaborGrid == false) {
      this.setState({ gridRateValue: 'laborGrid' });
      this.setState({ laborGrid: true });
      this.setState({ fixedRate: false });
    }
    event.preventDefault();
  };
  laborGridPopupClose = event => {
    this.setState({
      showLaborGrid: !this.state.showLaborGrid
    });
    this.setState({ gridRateValue: 'fixedRate' });
    this.setState({ laborGrid: false });
    this.setState({ fixedRate: true });
  };
  onUpdateData = params => {
    // console.log('params=1=', params);
    this.getAgGridData();
    this.gridApiLabor.redrawRows();
    this.gridApiParts.redrawRows();
    this.clearLaborGrid();
    this.clearPartsGrid();
    this.setState({
      editedRowId: null,
      isCodeEdited: false,
      markupChanged: false,
      fixedLaborRate: '',
      changeFleetOption: false
    });
  };
  render() {
    const { inputValue, filteredOptions } = this.state;
    const { classes } = this.props;
    var gridDate = '';
    var doorRate = '';
    var storeDate = '';
    // var regEx = /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/;
    // const regEx = /^[+-]?(\d+(\.\d{0,2})?|\.\d{1,2})$/;
    var regEx = /^\d{0,3}(\.\d{0,2})?$/;

    // if (this.state.rowData.length > 0) {
    //   gridDate = moment(this.state.rowData[0].createdDate).format('MM/DD/YY');
    //   doorRate = '$' + this.state.rowData[0].doorRate;
    //   storeDate = moment(this.state.rowData[0].storeInstallDate).format(
    //     'MM/DD/YY'
    //   );
    // }
    return this.state.isFleetNamesLoaded == false ? (
      <div>
        <Box style={{ padding: 25 }}>
          <LinearProgress color="secondary" />
          <Typography
            variant="h6"
            align="center"
            style={{ padding: 25 }}
            color="primary"
          >
            Processing...
          </Typography>
        </Box>
      </div>
    ) : (
      <>
        <div>
          <Page title={'Fleet Accounts'}></Page>
          <Paper
            square
            style={{
              // margin: 8,
              marginTop: '40px',
              paddingTop: '6px',
              height: '40px',
              margin: '8px 8px 8px',
              backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
              border:
                Dealer === 'Armatus'
                  ? '1px solid #003d6b'
                  : '1px solid #C2185B',
              color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
            }}
          >
            <Grid
              container
              className={clsx(this.props.titleContainer, 'reset-dashboard')}
            >
              <Grid
                item
                xs={5}
                style={{ display: 'flex', alignItems: 'center' }}
              >
                {this.props.history &&
                this.props.history.location.state &&
                (this.props.history.location.state.pageType == 'LaborMisses' ||
                  this.props.history.location.state.parent == 'LaborMisses' ||
                  this.props.history.location.state.pageType == 'PartsMisses' ||
                  this.props.history.location.state.parent == 'PartsMisses') ? (
                  <Button
                    variant="contained"
                    className={'bck-btn'}
                    onClick={this.handleclick}
                  >
                    <Typography variant="body1" align="left">
                      Back
                    </Typography>
                  </Button>
                ) : (
                  ''
                )}
              </Grid>
              <Grid
                item
                xs={5}
                style={{ display: 'flex', alignItems: 'center' }}
              >
                <Typography
                  variant="h4"
                  color="primary"
                  className={clsx(this.props.mainLabel, 'main-title')}
                >
                  Fleet Accounts
                </Typography>
              </Grid>
              <Grid
                item
                xs={2}
                style={{ display: 'flex', justifyContent: 'end' }}
              ></Grid>
            </Grid>
          </Paper>

          <div className="fleetClass">
            <Paper
              square
              style={{
                margin: 8,
                height: 65,
                marginLeft: 8,
                alignItems: 'center',
                paddingLeft: 4,
                display: 'flex',
                gap: 10
              }}
            >
              <Grid container spacing={2}>
                <Grid item xs={12} sm={3}>
                  <Grid container spacing={2} style={{ marginLeft: '0px' }}>
                    {this.state.typeStatus == true && (
                      <Grid item xs={6}>
                        <RadioGroup
                          aria-label="quiz1"
                          name="quiz1"
                          value={this.state.gridRateValueStart}
                          onChange={this.handleRadioChangeStart}
                          style={{ marginTop: '7px' }}
                        >
                          <div
                            style={{
                              display: 'flex',
                              justifyContent: 'space-around'
                            }}
                          >
                            <FormControlLabel
                              value="customer"
                              classes={{
                                root: 'radioLabel'
                              }}
                              style={{ height: 25, width: 125 }}
                              control={<Radio />}
                              label="By Customer"
                            />
                            <FormControlLabel
                              value="paytype"
                              classes={{
                                root: 'radioLabel'
                              }}
                              style={{ height: 25, width: 120 }}
                              control={<Radio />}
                              label="By Pay Type"
                            />
                            <FormControlLabel
                              value="opcode"
                              classes={{
                                root: 'radioLabel'
                              }}
                              style={{ height: 25, width: 120 }}
                              control={<Radio />}
                              label="By Opcode"
                            />
                          </div>
                        </RadioGroup>
                      </Grid>
                    )}
                  </Grid>
                </Grid>
                <Grid item xs={12} sm={1}></Grid>

                {this.state.gridRateValueStart != false && (
                  <Grid item xs={12} sm={8}>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <div
                          style={{
                            display: 'flex',
                            justifyContent: 'flex-end'
                          }}
                        >
                          <CustomAutocomplete
                            id="select-all-account"
                            freeSolo
                            size="small"
                            style={{ width: '38%', fontSize: 12 }}
                            value={this.state.inputValue}
                            onInputChange={this.handleChange}
                            options={filteredOptions}
                            openOnFocus={true}
                            disabled={
                              this.props.keycloak.realmAccess.roles &&
                              this.props.keycloak.realmAccess.roles.includes(
                                'client'
                              )
                                ? true
                                : false
                            }
                            renderInput={params => (
                              <>
                                <TextField
                                  {...params}
                                  label={
                                    this.state.searchStatus === 'customer'
                                      ? 'Search All Accounts'
                                      : this.state.searchStatus === 'paytype'
                                      ? 'Search All Pay Types'
                                      : 'Search All Opcodes'
                                  }
                                  variant="outlined"
                                  onChange={this.handleInputChange}
                                  InputProps={{
                                    ...params.InputProps,
                                    endAdornment: (
                                      <>
                                        {this.state.inputValue && (
                                          <IconButton
                                            onClick={this.handleClearInput}
                                          >
                                            <ClearIcon
                                              style={{ position: 'absolute' }}
                                            />
                                          </IconButton>
                                        )}
                                        <InputAdornment position="end">
                                          <SearchIcon />
                                        </InputAdornment>
                                      </>
                                    ),
                                    disableUnderline: true
                                  }}
                                />
                                {/* Display "No match found" when no options match the input */}
                                {this.state.inputValue &&
                                  filteredOptions.length === 0 && (
                                    <Typography
                                      variant="body2"
                                      color="textSecondary"
                                      style={{
                                        marginTop: '-2px',
                                        color: 'red',
                                        display: 'block',
                                        position: 'absolute'
                                      }}
                                    >
                                      {' '}
                                      No data found
                                    </Typography>
                                  )}
                              </>
                            )}
                          />

                          <Button
                            //className={clsx('reset-btn')}
                            title="Add Fleet Account"
                            variant="contained"
                            color="primary"
                            className={clsx(classes.back, 'reset-btn')}
                            onClick={this.handleAddRecord}
                            style={{ top: '5px', marginLeft: '7px' }}
                            disabled={
                              (typeof this.props.keycloak.realmAccess.roles !==
                                'undefined' &&
                                this.props.keycloak.realmAccess.roles.includes(
                                  'client'
                                ) === true) ||
                              (this.state.inputValue &&
                                filteredOptions.length === 0) ||
                              (this.state.inputValueSelected &&
                                this.state.filteredOptionsSelected.length ===
                                  0) ||
                              (this.state.inputValue == '' &&
                                (this.state.inputValueSelected == '' ||
                                  this.state.inputValueSelected == 'All'))
                                ? true
                                : false
                            }
                          >
                            <SwapHorizIcon style={{ top: '5px' }} />
                            {/* <Typography variant="body1" align="left">
              Add
            </Typography> */}
                          </Button>
                          <CustomAutocomplete
                            key={`${this.state.searchStatus}-${this.state.selectedFleet}-${this.state.selectedPayTypeOptionType}-${this.state.selectedOpcodeOptionType}`}
                            id="select-fleet-account"
                            freeSolo
                            size="small"
                            style={{
                              width: '38%',
                              alignContent: 'center',
                              fontSize: 12
                            }}
                            value={this.state.inputValueSelected}
                            openOnFocus={true}
                            onChange={this.handleChangeFleet}
                            options={this.state.filteredOptionsSelected}
                            disabled={
                              this.props.keycloak.realmAccess.roles &&
                              this.props.keycloak.realmAccess.roles.includes(
                                'client'
                              )
                                ? true
                                : false
                            }
                            renderInput={params => (
                              <>
                                <TextField
                                  {...params}
                                  label="Fleet Accounts"
                                  variant="outlined"
                                  onClick={this.showAllSelected}
                                  error={
                                    this.state.fleetNasssdmeError ? true : false
                                  }
                                  onChange={this.handleInputChangeSelected}
                                  InputProps={{
                                    ...params.InputProps,
                                    endAdornment: (
                                      <>
                                        {this.state.inputValueSelected && (
                                          <IconButton
                                            onClick={
                                              this.handleClearInputSelected
                                            }
                                          >
                                            <ClearIcon
                                              style={{ position: 'absolute' }}
                                            />
                                          </IconButton>
                                        )}
                                        <InputAdornment position="end">
                                          <SearchIcon />
                                        </InputAdornment>
                                      </>
                                    ),
                                    disableUnderline: true
                                  }}
                                />
                                {/* Display "No match found" when no options match the input */}

                                {this.state.inputValueSelected &&
                                  this.state.filteredOptionsSelected.length ===
                                    0 &&
                                  this.state.removeBtnClick == false && (
                                    <Typography
                                      variant="body2"
                                      color="textSecondary"
                                      style={{
                                        marginTop: '-2px',
                                        color: 'red',
                                        display: 'block',
                                        position: 'absolute'
                                      }}
                                    >
                                      {' '}
                                      No data found
                                    </Typography>
                                  )}
                              </>
                            )}
                          />
                          <Button
                            //className={clsx('reset-btn')}
                            title="Add Fleet Rates"
                            variant="contained"
                            color="primary"
                            className={clsx(classes.back, 'reset-btn')}
                            onClick={this.createFleetAccount}
                            style={{
                              top: '5px',
                              marginLeft: '7px'
                            }}
                            disabled={
                              (this.state.searchStatus == 'customer' &&
                                this.state.selectedFleet == 'All') ||
                              (this.state.searchStatus == 'paytype' &&
                                this.state.selectedPayTypeOptionType ==
                                  'All') ||
                              (this.state.searchStatus == 'opcode' &&
                                this.state.selectedOpcodeOptionType == 'All') ||
                              (typeof this.props.keycloak.realmAccess.roles !==
                                'undefined' &&
                                this.props.keycloak.realmAccess.roles.includes(
                                  'client'
                                ) === true) ||
                              (this.state.inputValueSelected &&
                                this.state.filteredOptionsSelected.length === 0)
                                ? true
                                : (this.props.history &&
                                    this.props.history.location &&
                                    this.props.history.location.state &&
                                    this.props.history.location.state
                                      .pageType &&
                                    (this.props.history.location.state
                                      .pageType == 'LaborMisses' ||
                                      this.props.history.location.state
                                        .pageType == 'PartsMisses')) ||
                                  this.state.inputValueSelected == ''
                                ? true
                                : false
                            }
                          >
                            <AddIcon />
                            {/* <Typography variant="body1" align="left">
              Add
            </Typography> */}
                          </Button>
                        </div>
                      </Grid>
                      <Grid item xs={4}>
                        <Grid
                          xs={12}
                          className={classes.flexGrid}
                          style={{ marginTop: '7px' }}
                        >
                          <Typography
                            variant="subtitle2"
                            color="primary"
                            className={clsx(this.props.mainLabel, 'sub-title')}
                          >
                            Selected:{' '}
                          </Typography>

                          <Tooltip
                            title={
                              this.state.searchStatus === 'customer'
                                ? this.state.selectedFleet || 'All'
                                : this.state.searchStatus === 'paytype'
                                ? this.state.selectedPayTypeOptionType || 'All'
                                : this.state.selectedOpcodeOptionType || 'All'
                            }
                          >
                            <Typography
                              variant="subtitle1"
                              color="primary"
                              className={clsx(
                                classes.ellipsis,
                                this.props.mainLabel,
                                'sub-title'
                              )}
                            >
                              {this.state.searchStatus === 'customer'
                                ? this.state.selectedFleet || 'All'
                                : this.state.searchStatus === 'paytype'
                                ? this.state.selectedPayTypeOptionType || 'All'
                                : this.state.selectedOpcodeOptionType || 'All'}
                            </Typography>
                          </Tooltip>
                        </Grid>
                      </Grid>

                      {/* New Grid item with width of 1 */}
                      <Grid item xs={2}>
                        <div style={{ marginTop: '7px' }}>
                          <Button
                            title="Save"
                            className={clsx('reset-btn', 'btnClass')}
                            variant="contained"
                            color="primary"
                            style={{
                              marginLeft: 5,
                              width: '55px',
                              height: '24px',
                              fontSize: '15px',
                              marginTop: 1
                            }}
                            disabled={
                              ((this.state.gridRateValue === 'fixedRate' &&
                                this.state.fixedLaborRate !== '.' &&
                                this.state.fixedLaborRate > 0 &&
                                this.state.markupChanged == false) ||
                                (this.state.matrixRateValue ===
                                  'partsFixedRate' &&
                                  ((this.state.markupChanged &&
                                    this.state.markupValue != '' &&
                                    this.state.markupValue !== '.' &&
                                    this.state.markupValue > 0) ||
                                    (this.state.fixedRateChanged &&
                                      this.state.markupChanged &&
                                      this.state.markupValue != '' &&
                                      this.state.markupValue !== '.' &&
                                      this.state.markupValue > 0))) ||
                                (this.state.markupChanged &&
                                  this.state.partsMarkup != 'Markup' &&
                                  (this.state.partsMarkup == 'Cost' ||
                                    this.state.partsMarkup == 'List'))) &&
                              this.state.gridInstalledDateErrorParts == false &&
                              this.state.gridInstalledDateErrorLabor == false &&
                              this.state.fixedLaborRate !== '.' &&
                              this.state.markupValue !== '.'
                                ? false
                                : true
                            }
                            onClick={this.handleSave}
                          >
                            Save
                          </Button>
                          <Button
                            title="Reset"
                            className={clsx('reset-btn')}
                            style={{
                              marginLeft: 5,
                              width: 55,
                              height: 24,
                              fontSize: 15,
                              marginTop: 1
                            }}
                            variant="contained"
                            onClick={this.resetFleet}
                          >
                            Reset
                          </Button>
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                )}
              </Grid>
            </Paper>
          </div>

          {this.state.addNewRow &&
          this.state.addNewFleet &&
          this.state.fleetSelected != 'All' &&
          this.state.searchStatus != false &&
          (this.state.selectedFleet != 'All' ||
            this.state.selectedPayTypeOptionType != 'All' ||
            this.state.selectedOpcodeOptionType != 'All') ? (
            <Paper
              square
              style={{
                margin: 8,
                height: 65,
                marginLeft: 8,
                alignItems: 'center',
                paddingLeft: 4,
                display: 'flex',
                pointerEvents: this.state.addNewFleet ? 'all' : 'none'
              }}
            >
              <Grid item xs={12} className={classes.textContainer}>
                <Grid
                  xs={6}
                  className={'fleet-grid'}
                  style={{
                    display:
                      this.props.history.location.state &&
                      (this.props.history.location.state.pageType ==
                        'PartsMisses' ||
                        this.props.history.location.state.parent ==
                          'PartsMisses')
                        ? 'none'
                        : 'flex',
                    alignItems: 'center'
                  }}
                >
                  <RadioGroup
                    aria-label="quiz"
                    name="quiz"
                    value={this.state.gridRateValue}
                    onChange={this.handleRadioChange}
                  >
                    <FormControlLabel
                      value="fixedRate"
                      classes={{
                        root: 'radioLabel'
                      }}
                      style={{ height: 25, width: 110 }}
                      control={<Radio />}
                      label="Fixed Rate"
                    />
                    <FormControlLabel
                      value="laborGrid"
                      classes={{
                        root: 'radioLabel'
                      }}
                      style={{ height: 25, width: 120 }}
                      control={<Radio />}
                      label="Labor Grid"
                      onClick={this.laborGridPopup}
                    />
                  </RadioGroup>
                  {/* <Typography
                  variant="subtitle1"
                  color="primary"
                  className={clsx(this.props.mainLabel, 'sub-title')}
                >
                  Labor Grid
                </Typography> */}
                  <FormControl
                    size="small"
                    className={classes.margin}
                    variant="outlined"
                    style={{ display: this.state.laborGrid ? 'block' : 'none' }}
                  >
                    <InputLabel htmlFor="outlined-adornment-amount">
                      Door Rate
                    </InputLabel>
                    <OutlinedInput
                      id="outlined-adornment-amount"
                      // value={values.amount}

                      error={
                        this.state.errorItems.includes('doorRateError')
                          ? true
                          : false
                      }
                      value={this.state.doorRate}
                      onChange={e =>
                        (e.target.value === '' || regEx.test(e.target.value)) &&
                        this.setState({
                          doorRate: e.target.value,
                          doorRateChanged: true
                        })
                      }
                      //onChange={handleChange('amount')}
                      startAdornment={
                        <InputAdornment position="start">$</InputAdornment>
                      }
                      labelWidth={120}
                    />
                    {/* <FormHelperText
                  style={{
                    color: 'red',
                    display: this.state.doorRateError ? 'block' : 'none'
                  }}
                  id="my-helper-text"
                >
                  Enter door rate
                </FormHelperText> */}
                  </FormControl>
                  <FormControl
                    size="small"
                    className={classes.margin}
                    variant="outlined"
                    style={{ display: this.state.fixedRate ? 'block' : 'none' }}
                  >
                    <InputLabel htmlFor="outlined-adornment-amount">
                      Fixed Labor Rate
                    </InputLabel>
                    <OutlinedInput
                      id="outlined-adornment-amount"
                      // value={values.amount}
                      // error={
                      //   this.state.errorItems.includes('fixedLaborRateError')
                      //     ? true
                      //     : false
                      // }
                      autoComplete="off"
                      value={this.state.fixedLaborRate}
                      onChange={e => {
                        const value = e.target.value;
                        if (value === '' || regEx.test(value)) {
                          this.setState({
                            fixedLaborRate: value,
                            fixedRateChanged: value !== ''
                          });
                        }
                      }}
                      disabled={this.state.laborGrid ? true : false}
                      //onChange={handleChange('amount')}
                      startAdornment={
                        <InputAdornment position="start">$</InputAdornment>
                      }
                      labelWidth={120}
                    />
                  </FormControl>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <FormControl
                    variant="outlined"
                    margin="dense"
                    className={clsx(classes.formControl, 'input-container')}
                  >
                    <DateRangePicker
                      key={this.state.gridInstalledDate}
                      initialSettings={{
                        // maxDate: {
                        //   date: new Date(),
                        // },
                        // minDate:{
                        //   date: (this.props.selectedDates[1])
                        // },
                        locale: {
                          format: 'MM/DD/YY',
                          separator: ' - '
                        },
                        autoUpdateInput: true,
                        showDropdowns: true,
                        autoApply: true,
                        singleDatePicker: true,
                        //maxDate: moment().toDate(),
                        // alwaysShowCalendars: true,
                        applyClass: clsx(classes.calButton, 'apply-btn'),
                        cancelClass: clsx(classes.calButton, 'apply-btn'),
                        startDate: new Date(this.state.gridInstalledDate)
                        //showDropdowns: true
                      }}
                      value={moment(this.state.gridInstalledDate).format(
                        'MM/DD/YY'
                      )}
                      onCallback={this.handleCallback}
                    >
                      <input
                        type="text"
                        className="datepicker fleet-picker"
                        style={{
                          border: this.state.errorItems.includes(
                            'gridInstalledDateError'
                          )
                            ? '1px solid red;'
                            : '1px solid #c0c0c0;'
                        }}
                        id="picker"
                        name="picker"
                        aria-labelledby="label-picker"
                        onChange={this.handleDateChangeLabor}
                        onBlur={this.handleDateChangeLabor}
                        // value={moment(this.state.gridInstalledDate).format(
                        //   'MM/DD/YY'
                        // )}
                        autoComplete="off"
                      />
                    </DateRangePicker>
                    <label
                      class="labelpicker fleet-picker"
                      for="picker"
                      id="label-picker"
                    >
                      <div class="textpicker fleet-picker">Install Date</div>
                    </label>
                  </FormControl>
                  {/* <FormControl
                  size="small"
                  className={classes.margin}
                  variant="outlined"
                >
                  <Typography style={{ fontSize: 10 }} sx={{ mt: 2, mb: 1 }}>
                    Install Date
                  </Typography>
                  <Datepicker
                    selected={this.state.gridInstalledDate}
                    onChange={date =>
                      this.setState({ gridInstalledDate: date })
                    }
                    error={
                      this.state.errorItems.includes('gridInstalledDateError')
                        ? true
                        : false
                    }
                    className={classes.datePickerInput}
                    dateFormat="MM/dd/yy"
                    // format={'MM-DD-YYYY'}
                    peekNextMonth
                    showMonthDropdown
                    showYearDropdown
                    dropdownMode="select"
                  />
                </FormControl> */}
                  <Button
                    title="Clear Labor Grid"
                    className={clsx('reset-btn')}
                    style={{
                      marginLeft: 5,
                      width: 55,
                      height: 24,
                      fontSize: 15,
                      marginTop: 1
                    }}
                    variant="contained"
                    onClick={this.clearLaborGrid}
                  >
                    Clear
                  </Button>
                  {this.state.gridInstalledDateErrorLabor == true && (
                    <FormControl>
                      {' '}
                      <div
                        style={{
                          marginTop: '7px',
                          marginLeft: '-154px',
                          color: 'red',
                          whiteSpace: 'normal',
                          width: '95px',
                          fontSize: '13px'
                        }}
                      >
                        Enter Dates in MM/DD/YY Format
                      </div>
                    </FormControl>
                  )}
                </Grid>
                <Grid
                  xs={6}
                  className={'fleet-grid'}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    //justifyContent: 'end',
                    paddingRight: '10px'
                  }}
                >
                  <div
                    style={{
                      display:
                        this.props.history.location.state &&
                        (this.props.history.location.state.pageType ==
                          'LaborMisses' ||
                          this.props.history.location.state.parent ==
                            'LaborMisses')
                          ? 'none'
                          : 'flex',
                      alignItems: 'center'
                    }}
                  >
                    <RadioGroup
                      aria-label="quiz"
                      name="quiz"
                      value={this.state.matrixRateValue}
                      onChange={this.handleMatrixChange}
                    >
                      <FormControlLabel
                        value="partsFixedRate"
                        classes={{
                          root: 'radioLabel'
                        }}
                        style={{ height: 25, width: 110 }}
                        control={<Radio />}
                        label="Fixed Rate"
                      />
                      <FormControlLabel
                        value="partsMatrix"
                        classes={{
                          root: 'radioLabel'
                        }}
                        style={{ height: 25, width: 120 }}
                        control={<Radio />}
                        label="Parts Matrix"
                        onClick={this.partsMatrixPopup}
                      />
                    </RadioGroup>
                    <TextField
                      id="outlined-select-matrix-value"
                      select
                      size="small"
                      style={{
                        marginRight: 5,
                        width: '17%'
                        //display: this.state.partsMatrix ? 'block' : 'none'
                      }}
                      label="Parts Markup"
                      SelectProps={{
                        native: true
                      }}
                      classes={{
                        root: 'matrix-value'
                      }}
                      placeholder={'Select'}
                      value={this.state.partsMarkup}
                      onChange={e => {
                        if (
                          e.target.value == 'Markup' ||
                          e.target.value == 'List' ||
                          e.target.value == 'Cost'
                        ) {
                          this.setState({ markupValue: '' });
                        }
                        this.setState({
                          partsMarkup: e.target.value,
                          markupChanged: true
                        });
                      }}
                      //placeholder=" select your currency"
                      variant="outlined"
                    >
                      <option
                        placeholder="Markup"
                        style={{ display: 'none' }}
                        selected
                        disabled
                        hidden
                        key={''}
                        value={'Markup'}
                      >
                        {'Select'}
                      </option>
                      <option key={1} value={'Cost'}>
                        {'Cost'}
                      </option>
                      <option key={2} value={'Cost-'}>
                        {'Cost-'}
                      </option>
                      <option key={3} value={'Cost+'}>
                        {'Cost+'}
                      </option>
                      <option key={4} value={'Cost%'}>
                        {'Cost%'}
                      </option>

                      <option key={5} value={'List'}>
                        {'List'}
                      </option>
                      <option key={6} value={'List-'}>
                        {'List-'}
                      </option>
                      <option key={7} value={'List+'}>
                        {'List+'}
                      </option>
                      <option key={8} value={'List%'}>
                        {'List%'}
                      </option>
                    </TextField>
                    &nbsp;&nbsp;&nbsp;
                    <TextField
                      style={{
                        display: this.state.partsFixedRate ? 'block' : 'none',
                        marginRight: 5,
                        width: '10%'
                      }}
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      className={classes.margin}
                      id="outlined-password-input"
                      label="%"
                      disabled={
                        this.state.partsMarkup == 'Cost' ||
                        this.state.partsMarkup == 'Markup' ||
                        this.state.partsMarkup == 'List'
                          ? true
                          : false
                      }
                      type="text"
                      autoComplete="off"
                      variant="outlined"
                      // error={
                      //   this.state.errorItems.includes('markupError')
                      //     ? true
                      //     : false
                      // }
                      error={
                        this.state.markupChanged == true &&
                        (this.state.partsMarkup == 'Cost+' ||
                          this.state.partsMarkup == 'Cost%' ||
                          this.state.partsMarkup == 'Cost-' ||
                          this.state.partsMarkup == 'List-' ||
                          this.state.partsMarkup == 'List+' ||
                          this.state.partsMarkup == 'List%') &&
                        this.state.markupValue == ''
                          ? true
                          : false
                      }
                      value={this.state.markupValue}
                      onChange={e => {
                        if (
                          e.target.value === '' ||
                          regEx.test(e.target.value)
                        ) {
                          this.setState({
                            markupValue: e.target.value,
                            markupValueChanged: true
                          });
                        }
                      }}
                    />
                    &nbsp;&nbsp;&nbsp;
                    {/* <TextField
                    id="outlined-select-matrix-value"
                    select
                    size="small"
                    style={{
                      marginRight: 5,
                      width: '17%'
                    }}
                    label="Parts Source"
                    SelectProps={{
                      native: true
                    }}
                    classes={{
                      root: 'matrix-value'
                    }}
                    error={
                      this.state.errorItems.includes('partsourceError')
                        ? true
                        : false
                    }
                    value={this.state.partsSource}
                    InputLabelProps={{ shrink: true }}
                    onChange={e =>
                      (e.target.value === '' || regEx.test(e.target.value)) &&
                      this.setState({ partsSource: e.target.value })
                    }
                    //placeholder=" select your currency"
                    variant="outlined"
                  >
                    {this.state.partsSourceArray.map((source, i) => (
                      <option key={source} value={source}>
                        {source}
                      </option>
                    ))}
                  </TextField> */}
                    <FormControl
                      variant="outlined"
                      margin="dense"
                      className={clsx(classes.formControl, 'input-container')}
                    >
                      <DateRangePicker
                        key={this.state.matrixInstalledDate}
                        initialSettings={{
                          // maxDate: {
                          //   date: new Date(),
                          // },
                          // minDate:{
                          //   date: (this.props.selectedDates[1])
                          // },
                          locale: {
                            format: 'MM/DD/YY',
                            separator: ' - '
                          },
                          autoUpdateInput: true,
                          showDropdowns: true,
                          autoApply: true,
                          singleDatePicker: true,
                          //maxDate: moment().toDate(),
                          // alwaysShowCalendars: true,
                          applyClass: clsx(classes.calButton, 'apply-btn'),
                          cancelClass: clsx(classes.calButton, 'apply-btn'),
                          startDate: new Date(this.state.matrixInstalledDate)
                          //showDropdowns: true
                        }}
                        value={moment(this.state.matrixInstalledDate).format(
                          'MM/DD/YY'
                        )}
                        onCallback={this.handleCallbackParts}
                      >
                        <input
                          type="text"
                          className="datepicker fleet-picker"
                          style={{
                            border: this.state.errorItems.includes(
                              'matrixInstalledDateError'
                            )
                              ? '1px solid red;'
                              : '1px solid #c0c0c0;'
                          }}
                          id="picker"
                          name="picker"
                          aria-labelledby="label-picker"
                          onChange={this.handleDateChangeParts}
                          onBlur={this.handleDateChangeParts}
                          // value={moment(this.state.matrixInstalledDate).format(
                          //   'MM/DD/YY'
                          // )}
                          autoComplete="off"
                        />
                      </DateRangePicker>
                      <label
                        class="labelpicker fleet-picker"
                        for="picker"
                        id="label-picker"
                      >
                        <div class="textpicker fleet-picker">Install Date</div>
                      </label>
                    </FormControl>
                    {/* <FormControl
                    size="small"
                    className={classes.margin}
                    variant="outlined"
                    style={{ marginRight: 10, width: '17%' }}
                  >
                    <Typography style={{ fontSize: 10 }} sx={{ mt: 2, mb: 1 }}>
                      Install Date
                    </Typography>
                    <Datepicker
                      selected={this.state.matrixInstalledDate}
                      error={
                        this.state.errorItems.includes(
                          'matrixInstalledDateError'
                        )
                          ? true
                          : false
                      }
                      onChange={date =>
                        this.setState({ matrixInstalledDate: date })
                      }
                      className={classes.datePickerInput}
                      dateFormat="MM/dd/yy"
                      // format={'MM-DD-YYYY'}
                      peekNextMonth
                      showMonthDropdown
                      showYearDropdown
                      dropdownMode="select"
                    />
                  </FormControl> */}
                    <Button
                      title="Clear Parts Grid"
                      className={clsx('reset-btn', 'btnClass')}
                      style={{
                        marginLeft: 5,
                        width: 55,
                        height: 24,
                        fontSize: 15,
                        marginTop: 1
                      }}
                      variant="contained"
                      onClick={this.clearPartsGrid}
                    >
                      Clear
                    </Button>
                    {this.state.gridInstalledDateErrorParts == true && (
                      <FormControl>
                        {' '}
                        <div
                          style={{
                            marginTop: '7px',
                            marginLeft: '-154px',
                            color: 'red',
                            whiteSpace: 'normal',
                            width: '95px',
                            fontSize: '13px'
                          }}
                        >
                          Enter Dates in MM/DD/YY Format
                        </div>
                      </FormControl>
                    )}
                  </div>
                </Grid>
                {/* <Grid
                xs={1}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'end',
                  paddingRight: '10px'
                }}
              >
                
              </Grid> */}
              </Grid>
            </Paper>
          ) : (
            ''
          )}
          {this.state.isLoading == true ? (
            <div>
              <Box style={{ padding: 25 }}>
                <LinearProgress color="secondary" />
                <Typography
                  variant="h6"
                  align="center"
                  style={{ padding: 25 }}
                  color="primary"
                >
                  Processing...
                </Typography>
              </Box>
            </div>
          ) : null}
          <Paper
            square
            style={{
              margin: 8,
              height: window.innerHeight - 250 + 'px',
              marginLeft: 8,
              display: this.state.isLoading != true ? 'block' : 'none',
              paddingLeft: 4,
              paddingRight: 4,
              display: 'flex'
            }}
          >
            {this.state.loader && (
              <div
                style={{
                  position: 'fixed',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  background: 'rgba(255, 255, 255, 0.91)',
                  zIndex: '1000',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}
              >
                <CircularProgress size={60} />
              </div>
            )}
            <Grid
              xs={12}
              className={
                this.state.isLoading == true ||
                (this.props.history.location.state &&
                  (this.props.history.location.state.pageType ==
                    'PartsMisses' ||
                    this.props.history.location.state.pageType ==
                      'LaborMisses' ||
                    this.props.history.location.state.parent == 'PartsMisses' ||
                    this.props.history.location.state.parent == 'LaborMisses'))
                  ? classes.textContainerGridMisses
                  : classes.textContainerGrid
              }
            >
              <Grid xs={6}>
                <div
                  className="ag-theme-balham fleet-container"
                  id="fleetLabor"
                  style={{
                    //height: '410px',//
                    // width: '700px',
                    height: window.innerHeight - 305 + 'px',
                    // height:(window.innerHeight-215)+'px',
                    alignContent: 'center',
                    marginLeft: '8px',
                    marginTop: 10,
                    display:
                      this.state.isLoading == true ||
                      (this.props.history.location.state &&
                        (this.props.history.location.state.pageType ==
                          'PartsMisses' ||
                          this.props.history.location.state.parent ==
                            'PartsMisses'))
                        ? 'none'
                        : 'block'
                  }}
                >
                  <Typography
                    variant="h5"
                    color="primary"
                    className={clsx(this.props.mainLabel, classes.subTitle)}
                  >
                    Labor
                  </Typography>

                  <AgGridReact
                    className="ag-theme-balham grid-cell-centered "
                    style={{
                      height: '410px'
                    }}
                    onRowEditingStarted={this.onRowEditingStarted}
                    onRowEditingStopped={this.onRowEditingStopped}
                    onFilterChanged={this.onFilterChangedLabor}
                    onSortChanged={this.onSortChangedLabor}
                    getRowStyle={this.getRowStyle}
                    getChartToolbarItems={this.getChartToolbarItems}
                    modules={AllModules}
                    columnDefs={this.state.columnDefs}
                    defaultColDef={this.state.defaultColDef}
                    onGridReady={this.onGridReadyLabor}
                    enableColResize={1}
                    suppressAggFuncInHeader
                    rowData={this.state.fleetLaborArr}
                    excelStyles={this.state.excelStyles}
                    editType={this.state.editType}
                    suppressClickEdit={true}
                    onCellClicked={this.onCellClicked}
                    floatingFilter={true}
                    //context={this.state.context}
                    //frameworkComponents={this.state.frameworkComponents}
                    suppressDragLeaveHidesColumns={true}
                    // context={this.state.context}
                    // frameworkComponents={this.state.frameworkComponents}
                    // animateRows={true}
                    // enableRangeSelection={false}
                    // enableCharts={true}
                    // modules={AllModules}
                    // columnDefs={this.state.columnDefs}
                    // excelStyles={this.state.excelStyles}
                    // defaultColDef={this.state.defaultColDef}
                    // onGridReady={this.onGridReadyLabor}
                    // suppressAggFuncInHeader={true}
                    // rowData={this.state.fleetLaborArr}
                    // sortingOrder={this.sortingOrder}
                    // tooltipShowDelay={0}
                    // floatingFilter={true}
                    // suppressRowClickSelection={true}
                    // headerHeight={this.state.headerHeight}
                    // onFilterChanged={this.onFilterChanged}
                    // suppressContextMenu={true}
                    // overlayNoRowsTemplate={this.state.overlayNoRowsTemplateLabor}
                    // editType={this.state.editType}
                    // onCellClicked={this.onCellClicked}
                    // onRowEditingStarted={this.onRowEditingStarted}
                    // onRowEditingStopped={this.onRowEditingStopped}
                    // suppressClickEdit={true}
                    //suppressHorizontalScroll={true}
                    suppressContextMenu={true}
                  />
                </div>
              </Grid>
              <Grid xs={6}>
                <div
                  id="fleetParts"
                  className="ag-theme-balham fleet-container"
                  style={{
                    //height: '410px',//
                    // width: '100%',
                    // width: '715px',
                    height: window.innerHeight - 305 + 'px',
                    // height:(window.innerHeight-215)+'px',
                    alignContent: 'center',
                    marginLeft: '8px',
                    marginTop: 10,
                    display:
                      this.state.isLoading == true ||
                      (this.props.history.location.state &&
                        (this.props.history.location.state.pageType ==
                          'LaborMisses' ||
                          this.props.history.location.state.parent ==
                            'LaborMisses'))
                        ? 'none'
                        : 'block'
                  }}
                >
                  <Typography
                    variant="h5"
                    color="primary"
                    className={clsx(this.props.mainLabel, classes.subTitle)}
                  >
                    Parts
                  </Typography>
                  <AgGridReact
                    className="ag-theme-balham"
                    style={{
                      height: '410px'
                    }}
                    animateRows={true}
                    enableRangeSelection={false}
                    onFilterChanged={this.onFilterChangedParts}
                    onSortChanged={this.onSortChangedParts}
                    enableCharts={true}
                    modules={AllModules}
                    columnDefs={this.state.columnDefsParts}
                    excelStyles={this.state.excelStyles}
                    defaultColDef={this.state.defaultColDef}
                    onGridReady={this.onGridReadyParts}
                    suppressAggFuncInHeader={true}
                    enableColResize={true}
                    rowData={this.state.fleetPartsArr}
                    sortingOrder={this.sortingOrder}
                    tooltipShowDelay={0}
                    floatingFilter={true}
                    suppressRowClickSelection={true}
                    // headerHeight={this.state.headerHeight}
                    suppressContextMenu={true}
                    // overlayNoRowsTemplate={this.state.overlayNoRowsTemplate}
                    editType={this.state.editType}
                    suppressDragLeaveHidesColumns={true}
                    context={this.state.context}
                    frameworkComponents={this.state.frameworkComponents}
                    //suppressHorizontalScroll={true}
                  />
                </div>
              </Grid>
            </Grid>
          </Paper>
          {this.state.showPartsMatrix && (
            <PartsMatrixModel
              openDialog={this.state.showPartsMatrix}
              handleClose={this.partsMatrixPopupClose}
              gridRateValueStart={
                this.state.gridRateValueStart == 'customer'
                  ? 'cust_fleet'
                  : this.state.gridRateValueStart == 'paytype'
                  ? 'paytype_fleet'
                  : 'opcode_fleet'
              }
              fleetName={
                this.state.searchStatus == 'customer'
                  ? this.state.selectedFleet
                  : this.state.searchStatus == 'paytype'
                  ? this.state.selectedPayTypeOptionType
                  : this.state.selectedOpcodeOptionType
              }
              typeFor={'matrix'}
              onUpdateData={this.onUpdateData}
            />
          )}

          {this.state.showLaborGrid && (
            <PartsMatrixModel
              openDialog={this.state.showLaborGrid}
              handleClose={this.laborGridPopupClose}
              gridRateValueStart={
                this.state.gridRateValueStart == 'customer'
                  ? 'cust_fleet'
                  : this.state.gridRateValueStart == 'paytype'
                  ? 'paytype_fleet'
                  : 'opcode_fleet'
              }
              fleetName={
                this.state.searchStatus == 'customer'
                  ? this.state.selectedFleet
                  : this.state.searchStatus == 'paytype'
                  ? this.state.selectedPayTypeOptionType
                  : this.state.selectedOpcodeOptionType
              }
              typeFor={'grid'}
              onUpdateData={this.onUpdateData}
            />
          )}
          {/* {this.state.showLaborGrid && (
            <LaborGrid
              openDialog={this.state.showLaborGrid}
              handleClose={this.laborGridPopup}
              gridRateValueStart={
                this.state.gridRateValueStart == 'customer'
                  ? 'cust_fleet'
                  : 'paytype_fleet'
              }
              fleetName={
                this.state.searchStatus == 'customer'
                  ? this.state.selectedFleet
                  : this.state.selectedPayTypeOptionType
              }
              typeFor={'grid'}
            />
          )} */}
        </div>
        <Snackbar
          open={this.state.openSnackbar}
          autoHideDuration={6000}
          onClose={this.hidesnackbar}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbar} severity="success">
            {this.state.openSnackbarSuccessMsg}
          </Alert>
        </Snackbar>

        <Snackbar
          open={this.state.openSnackbarAccount}
          autoHideDuration={6000}
          onClose={this.hidesnackbarAccount}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbarAccount} severity="success">
            Successfully updated to Fleet Accounts
          </Alert>
        </Snackbar>

        <Snackbar
          open={this.state.openSnackbarAccountDelete}
          autoHideDuration={6000}
          onClose={this.hidesnackbarAccountDelete}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbarAccountDelete} severity="success">
            Successfully removed from Fleet Accounts
          </Alert>
        </Snackbar>

        <Snackbar
          open={this.state.openSnackbarPaytype}
          autoHideDuration={6000}
          onClose={this.hidesnackbarPaytype}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbarPaytype} severity="success">
            Successfully updated to Fleet Accounts
          </Alert>
        </Snackbar>
        <Snackbar
          open={this.state.openSnackbarPaytypeDelete}
          autoHideDuration={6000}
          onClose={this.hidesnackbarPaytypeDelete}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbarPaytypeDelete} severity="success">
            Successfully removed from Fleet Accounts
          </Alert>
        </Snackbar>

        <Snackbar
          open={this.state.openSnackbarOpcode}
          autoHideDuration={6000}
          onClose={this.hidesnackbarOpcode}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbarOpcode} severity="success">
            Successfully updated to Fleet Accounts
          </Alert>
        </Snackbar>
        <Snackbar
          open={this.state.openSnackbarOpcodeDelete}
          autoHideDuration={6000}
          onClose={this.hidesnackbarOpcodeDelete}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbarOpcodeDelete} severity="success">
            Successfully removed from Fleet Accounts
          </Alert>
        </Snackbar>

        <Snackbar
          open={this.state.openSnackbarEdit}
          autoHideDuration={6000}
          onClose={this.hidesnackbarEdit}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbarEdit} severity="success">
            {/* {this.state.updateMsg} */}
            Edit Successfully completed
          </Alert>
        </Snackbar>
        <Snackbar
          open={this.state.openSnackbarError}
          autoHideDuration={6000}
          onClose={this.hidesnackbarError}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbarError} severity="error">
            {this.state.openSnackbarErrorMsg}
          </Alert>
        </Snackbar>
        <Snackbar
          open={this.state.openSnackbarDeleteFixedRate}
          autoHideDuration={6000}
          onClose={this.hidesnackbarDelete}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbarDelete} severity="success">
            {this.state.updateMsg}
          </Alert>
        </Snackbar>
        <Snackbar
          open={this.state.openSnackbarDeleteLaborGrid}
          autoHideDuration={6000}
          onClose={this.hidesnackbarDelete}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbarDelete} severity="success">
            {this.state.updateMsg}
          </Alert>
        </Snackbar>
        <Snackbar
          open={this.state.openSnackbarDeletePartsMatrix}
          autoHideDuration={6000}
          onClose={this.hidesnackbarDelete}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbarDelete} severity="success">
            {this.state.updateMsg}
          </Alert>
        </Snackbar>
        <Dialog
          open={this.state.openConfirmationDialog}
          onClose={() => {
            this.setState({ openConfirmationDialog: false });
            this.deleteFleetConfirmedCancel();
          }}
        >
          <DialogTitle>Confirm Deletion</DialogTitle>
          <DialogContent>
            <Typography
              style={{
                textTransform: 'none'
              }}
            >
              Are you sure you want to delete this fixed rate?
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => {
                this.setState({ openConfirmationDialog: false });
                this.deleteFleetConfirmedCancel();
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                this.setState({ openConfirmationDialog: false });
                // Call your function to delete the fleet
                this.deleteFleetConfirmed();
              }}
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog
          open={this.state.openConfirmationDialogLaborGrid}
          onClose={() => {
            this.setState({ openConfirmationDialogLaborGrid: false });
            this.deleteFleetConfirmedCancel();
          }}
        >
          <DialogTitle>Confirm Deletion</DialogTitle>
          <DialogContent>
            <Typography
              style={{
                textTransform: 'none'
              }}
            >
              Are you sure you want to delete the labor grid?
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => {
                this.setState({ openConfirmationDialogLaborGrid: false });
                this.deleteFleetConfirmedCancel();
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                this.setState({ openConfirmationDialogLaborGrid: false });
                // Call your function to delete the fleet
                this.deleteFleetConfirmed();
              }}
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog
          open={this.state.openConfirmationDialogPartsMatrix}
          onClose={() => {
            this.setState({ openConfirmationDialogPartsMatrix: false });
            this.deleteFleetConfirmedCancel();
          }}
        >
          <DialogTitle>Confirm Deletion</DialogTitle>
          <DialogContent>
            <Typography
              style={{
                textTransform: 'none'
              }}
            >
              Are you sure you want to delete the parts matrix?
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => {
                this.setState({ openConfirmationDialogPartsMatrix: false });
                this.deleteFleetConfirmedCancel();
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                this.setState({ openConfirmationDialogPartsMatrix: false });
                // Call your function to delete the fleet
                this.deleteFleetConfirmed();
              }}
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </>
    );
  }
}
const CustomAutocomplete = withStyles({
  inputRoot: {
    paddingRight: '5px !important',
    '&[class*="MuiFilledInput-root"]': {}
  }
})(Autocomplete);
const styles = theme => ({
  formControl: {
    margin: theme.spacing(1),
    minWidth: 210,
    paddingLeft: 8,
    paddingRight: 4
  },

  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex',
    marginLeft: 20
  },
  back: {
    marginRight: 10,
    float: 'right'
    // marginTop: 7
  },
  dataAsOf: {
    marginRight: 32,
    float: 'left',
    marginTop: 1,
    marginLeft: '1%'
  },
  dataAsOfText: {
    marginLeft: 45
  },
  dataAsOfValue: {
    marginLeft: 3,
    marginRight: 8
  },
  CurrentGridText: {
    marginLeft: 4
  },
  CurrentGridValue: {
    marginLeft: 3
  },
  TargetRateText: {
    marginLeft: 38
  },
  TargetRateValue: {
    marginLeft: 5
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    }
  },
  summaryBlockText: {
    fontSize: 12,
    color: '#7987a1',
    fontWeight: 'bold'
  },
  inputText: {
    width: 125,
    textAlign: 'left'
  },
  inputTextRate: {
    width: 97,
    textAlign: 'left'
  },
  inputValue: {
    marginRight: 11
  },
  input: {
    margin: '0px 5px',
    width: '200px'
  },
  margin: {
    width: '22%',
    marginLeft: 5
  },
  dataContainer: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  textContainer: {
    alignItems: 'center',
    display: 'flex',
    gap: 10,
    paddingLeft: 10
  },
  textContainerGrid: {
    display: 'flex',
    gap: 10
    //paddingLeft: 10
  },
  textContainerGridMisses: {
    gap: 10
    //paddingLeft: 10
  },
  flexGrid: {
    display: 'flex',
    alignItems: 'center',
    gap: 5
  },
  inputRoot: {
    paddingRight: 2
  },
  subTitle: {
    paddingRight: 5,
    paddingBottom: 5
  },
  datePickerInput: {
    padding: '10px',
    border: 'thin solid #ccc !important',
    borderRadius: '4px',
    height: '36px !important',
    fontFamily: 'sans-serif',
    fontSize: '14px',
    width: '100%',
    marginBottom: 20
  },
  errorBorder: {
    borderColor: 'red !important' // Use !important to ensure it overrides other styles
  },
  ellipsis: {
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: 'block'
    // width: '70%'
  }
});

export default withStyles(styles)(withKeycloak(FleetAccounts));
