import React, { useState, useEffect } from 'react';
import {
  Paper,
  Typography,
  Button,
  Grid,
  FormControl,
  Radio,
  RadioGroup,
  FormControlLabel,
  TextField,
  Select,
  MenuItem,
  InputLabel,
  InputAdornment,
  IconButton,
  OutlinedInput,
  Chip,
  Autocomplete,
  Checkbox,
  ListItemText,
  CircularProgress
} from '@material-ui/core';

import CheckIcon from '@material-ui/icons/Check';
import CancelIcon from '@material-ui/icons/Cancel';
import { FileUploadOutlined } from '@material-ui/icons';
import { DatePicker } from '@material-ui/pickers';
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import moment from 'moment';
import {
  partsMatrixFileUpload,
  // partsMatrixType,
  getPartsSource,
  gridTypeFileUpload
} from 'src/utils/hasuraServices';
import { DropzoneArea } from 'material-ui-dropzone';
import {
  MuiPickersUtilsProvider,
  KeyboardTimePicker,
  KeyboardDatePicker
} from '@material-ui/pickers';
import DateFnsUtils from '@date-io/date-fns';
import { makeStyles } from '@material-ui/core/styles';
import clsx from 'clsx';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'react-datepicker/dist/react-datepicker.css';
import 'react-datepicker/dist/react-datepicker-cssmodules.css';
// import SuccessSnackbar from '../MenuMappingGrid/SuccessSnackbar';
import SuccessSnackbar from 'src/components/SuccessSnackbar';
const useStyles = makeStyles(theme => ({
  customDropzone: {},
  dropzoneImage: {
    // Your custom styles for the image preview
    width: '30px', // Example width
    height: '30px', // Example height
    objectFit: 'cover', // Example object-fit property
    borderRadius: '50%' // Example border-radius
    // Add more styles as needed
  },
  snackbarContainer: {
    '& .MuiSnackbar-root': {
      top: '30px',
      bottom: 'unset',
      left: '50%',
      transform: 'translateX(-50%)'
    }
  },
  loaderGrid: {
    position: 'fixed', // Cover the entire screen
    top: 0,
    left: 0,
    width: '100vw', // Full width of the viewport
    height: '100vh', // Full height of the viewport
    backgroundColor: 'rgba(255, 255, 255, 0.8)', // Semi-transparent white background
    display: 'flex', // Flexbox to center content
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1300 // Ensure it is above other elements, default Material-UI modal zIndex
  },
  root: {
    // Add your custom styles here
    border: '2px dashed #ccc',
    borderRadius: '8px',
    padding: '20px',
    textAlign: 'center'
    // width: '90% !important'
  },
  preview: {
    position: 'relative',
    left: 55,
    top: 15,
    right: '10px', // Adjust the right position as needed
    width: '24px', // Custom width
    height: '24px', // Custom height
    opacity: 1, // Ensure the button is visible
    transition: '0.5s ease',
    '&:hover': {
      opacity: 1
    }
  },
  fileError: {
    // Add your custom styles here
    border: '2px dashed #e53935',
    borderRadius: '8px',
    padding: '20px',
    textAlign: 'center'
    // width: '90% !important'
  },
  icon: {
    fontSize: '48px',
    // color: 'red !important',
    marginBottom: '-8px !important',
    marginTop: '15px !important',
    height: 30
  },

  customFileIcon: {
    width: 32,
    height: 32,
    marginRight: 8
  },
  customFileName: {
    fontSize: 16
  },
  star: {
    color: 'red',
    padding: '2px'
  },
  headerDes: {
    textTransform: 'none',
    fontSize: '13px !important',
    fontFamily: 'Roboto',
    fontWeight: 'bold',
    color: '#003d6b'
    // marginTop: -25
  }
}));
const AddMatrixForm = props => {
  const [matrixType, setMatrixType] = useState([]);
  const [selectedMatrixType, setSelectedMatrixType] = useState('');
  const [partsSource, setPartsSource] = useState([]);
  const [selectedPartsSource, setSelectedPartsSource] = useState([]);
  const [error, setError] = useState('');
  const [selectedDate, setSelectedDate] = React.useState(new Date());
  const [rowData, setRowData] = useState([
    { from: 0.01, to: '', base: '', breakField: '', percentage: '' }
  ]);

  const handleAddRow = () => {
    const lastRow = rowData[rowData.length - 1];
    const newRow = {
      from: parseFloat(lastRow.to) + 0.01,
      to: '',
      base: '',
      breakField: '',
      percentage: ''
    };
    setRowData([...rowData, newRow]);
  };

  const handleRemoveLastRow = () => {
    if (rowData.length > 1) {
      setRowData(rowData.slice(0, -1));
    }
  };
  const handleDateChange = date => {
    setSelectedDate(date.format('MM/DD/YY'));
  };
  useEffect(() => {
    // getPartsSource('prtsource_list', result => {
    //   if (
    //     result.data.statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix.nodes
    //   ) {
    //     setPartsSource(
    //       result.data.statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix
    //         .nodes
    //     );
    //   }
    // });
    // partsMatrixType(result => {
    //   if (result.data.statelessCcPhysicalRwPartsMatrixTypeMasterDetails.nodes) {
    //     setMatrixType(
    //       result.data.statelessCcPhysicalRwPartsMatrixTypeMasterDetails.nodes
    //     );
    //   }
    // });
  }, []);
  const handleMatrixTypeChange = event => {
    setSelectedMatrixType(event.target.value);
    // setMatrixType(event.target.value);
  };

  const handleSourceChange = event => {
    // setSelectedPartsSource(event.target.value);
  };
  const handleCancel = () => {
    props.handleCancel();
  };
  const columnDefs = [
    { headerName: 'From', field: 'from', editable: false },
    { headerName: 'To', field: 'to', editable: true },
    { headerName: 'Base', field: 'base', editable: true },
    { headerName: 'Break Field', field: 'breakField', editable: true },
    { headerName: 'Percentage', field: 'percentage', editable: true }
  ];

  return (
    <Paper elevation={3} style={{ padding: '20px', marginBottom: '20px' }}>
      <Typography variant="h5" gutterBottom>
        Update Matrix
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6}>
          <div
            className="ag-theme-material"
            style={{ height: '300px', width: '100%', marginBottom: '20px' }}
          >
            <AgGridReact
              columnDefs={columnDefs}
              rowData={rowData}
              suppressContextMenu={true}
            />
          </div>
        </Grid>
        <Grid item xs={12} sm={6}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={4}></Grid>
            <Grid item xs={12} sm={4}>
              <Grid item xs={12} sm={12}>
                <FormControl style={{ width: 235, marginLeft: 55 }}>
                  <InputLabel>Matrix Type</InputLabel>
                  <Select
                    value={selectedMatrixType}
                    onChange={handleMatrixTypeChange}
                  >
                    {matrixType.map(item => (
                      <MenuItem value={item.value}>{item.value}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={12}>
                {/* <FormControl style={{ width: 235, marginLeft: 55 }}>
                  <InputLabel>Source</InputLabel>
                  <Select
                    value={selectedPartsSource}
                    onChange={handleSourceChange}
                  >
                    {partsSource.map(item => (
                      <MenuItem value={item.prtsourceList}>
                        {item.prtsourceList}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl> */}
                <FormControl style={{ width: 235, marginLeft: 55 }}>
                  <InputLabel>Source</InputLabel>
                  <Select
                    multiple
                    value={selectedPartsSource}
                    onChange={e => setSelectedPartsSource(e.target.value)}
                    input={<OutlinedInput label="Multiple Select" />}
                    renderValue={selected => (
                      <div gap={1} direction="row" flexWrap="wrap">
                        {selectedPartsSource.map(value => (
                          <Chip
                            key={value}
                            label={value}
                            onDelete={() =>
                              setSelectedPartsSource(
                                selectedPartsSource.filter(
                                  item => item !== value
                                )
                              )
                            }
                            deleteIcon={
                              <CancelIcon
                                onMouseDown={event => event.stopPropagation()}
                              />
                            }
                          />
                        ))}
                      </div>
                    )}
                  >
                    {partsSource.map(item => (
                      <MenuItem
                        key={item.prtsourceList}
                        value={item.prtsourceList}
                        sx={{ justifyContent: 'space-between' }}
                      >
                        {item.prtsourceList}
                        {selectedPartsSource.includes(item.prtsourceList) ? (
                          <CheckIcon color="info" />
                        ) : null}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={12}>
                <MuiPickersUtilsProvider
                  utils={DateFnsUtils}
                  style={{ width: 200 }}
                >
                  <Grid container justifyContent="space-around">
                    <KeyboardDatePicker
                      margin="normal"
                      id="date-picker-dialog"
                      label="Date picker dialog"
                      format="MM/dd/yy"
                      value={selectedDate}
                      onChange={handleDateChange}
                      KeyboardButtonProps={{
                        'aria-label': 'change date'
                      }}
                      // style={{ paddingTop: '8px !important' }}
                    />
                  </Grid>
                </MuiPickersUtilsProvider>
              </Grid>

              <Grid container spacing={3} justify="flex-end">
                <Grid item>
                  <Button
                    variant="contained"
                    color="secondary"
                    onClick={handleCancel}
                  >
                    Cancel
                  </Button>
                </Grid>
                <Grid item>
                  <Button
                    variant="contained"
                    color="primary"
                    // onClick={handleSubmit}
                  >
                    Submit
                  </Button>
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12} sm={4}></Grid>
          </Grid>
        </Grid>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <Button variant="contained" color="primary" onClick={handleAddRow}>
              Add Row
            </Button>
            <Button
              variant="contained"
              color="secondary"
              onClick={handleRemoveLastRow}
            >
              Remove Last Row
            </Button>
          </Grid>
        </Grid>
      </Grid>
    </Paper>
  );
};

const UploadMatrixForm = props => {
  const names = [
    'Humaira Sims',
    'Santiago Solis',
    'Dawid Floyd',
    'Mateo Barlow',
    'Samia Navarro',
    'Kaden Fields',
    'Genevieve Watkins',
    'Mariah Hickman',
    'Rocco Richardson',
    'Harris Glenn'
  ];
  const [selectedNames, setSelectedNames] = useState([]);

  const classes = useStyles();
  const [isLoading, setIsLoading] = React.useState(false);
  const [files, setFiles] = useState('');
  const [fileName, setFileName] = useState('');
  const [matrixType, setMatrixType] = useState([]);
  const [selectedMatrixType, setSelectedMatrixType] = useState('');
  const [partsSource, setPartsSource] = useState([]);
  const [selectedPartsSource, setSelectedPartsSource] = useState([]);
  const [error, setError] = useState('');
  const [fileError, setFileError] = useState('');
  const [sourceError, setSourceError] = useState('');
  const [selectedDate, setSelectedDate] = React.useState(new Date());
  const [fileUploaded, setFileUploaded] = useState([]);
  const [successMsg, setSuccessMsg] = useState('');
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [allSelected, setAllSelected] = useState(false);
  const [selectedGridType, setSelectedGridType] = useState('');
  const [gridTypeError, setGridTypeError] = useState('');
  const [goalFail, setGoalFail] = useState('');
  const [installedDateError, setInstalledDateError] = useState(false);

  const handleDateChange = date => {
    setSelectedDate(date);
  };
  useEffect(() => {
    // getPartsSource('prtsource_list', result => {
    //   if (
    //     result.data.statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix.nodes
    //   ) {
    //     setPartsSource(
    //       result.data.statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix
    //         .nodes
    //     );
    //   }
    // });
    // partsMatrixType(result => {
    //   if (result.data.statelessCcPhysicalRwPartsMatrixTypeMasterDetails.nodes) {
    //     setMatrixType(
    //       result.data.statelessCcPhysicalRwPartsMatrixTypeMasterDetails.nodes
    //     );
    //     setSelectedMatrixType(
    //       result.data.statelessCcPhysicalRwPartsMatrixTypeMasterDetails.nodes[0]
    //         .value
    //     );
    //   }
    // });
  }, []);
  const handleMatrixTypeChange = event => {
    setSelectedMatrixType(event.target.value);
    // setMatrixType(event.target.value);
  };

  const handleSourceChange = event => {
    setSelectedPartsSource(event.target.value);
  };

  const handleFileChange = fileData => {
    setFileUploaded([...fileUploaded, ...fileData]);
    const file = fileData[0]; // Assuming only one file is uploaded
    if (file) {
      setFileName(file.name);
      console.log('file===', file);
      const reader = new FileReader();
      reader.onload = () => {
        const base64Data = reader.result;
        const base64Content = base64Data.split(',')[1];
        setFiles(base64Content);
        console.log('Base64 data:', base64Content, '==', files);
        // You can now use the base64Data as needed
      };
      reader.readAsDataURL(file);
    }
  };
  const handleDeleteFile = deletedFile => {
    // Remove the deleted file from the files state
    const filteredFiles = fileUploaded.filter(file => file !== deletedFile);
    setFiles('');
  };
  const handleCancel = () => {
    props.handleCancel();
  };
  const handleSubmit = () => {
    setIsLoading(true);
    setopenSnackbar(false);
    setGoalFail('');
    setSuccessMsg('');
    console.log('props.typeFor==', props.typeFor);
    if (props.typeFor == 'matrix') {
      // if (selectedPartsSource.length > 0) {
      //   console.log('enter=1=====1111====');
      //   setSourceError('');
      // } else {
      //   console.log('enter=1=====22222');
      //   setSourceError('Please select at least one parts source');
      // }
      if (files) {
        setFileError('');
      } else {
        setFileError('Please upload the parts matrix file.');
        setIsLoading(false);
      }

      if (!files) {
        setError('Please upload the parts matrix file.');
        setIsLoading(false);
        return;
      } else {
        setError('');
        var source = [];
        // source.push(selectedPartsSource);
        source.push(localStorage.getItem('dms') == 'rey' ? '' : 'All');
        console.log('source==========', source);
        var inMatrixOrFleet = props.gridRateValueStart;
        partsMatrixFileUpload(
          files,
          fileName,
          props.fleetName,
          selectedDate,
          source,
          inMatrixOrFleet,
          result => {
            setIsLoading(false);
            if (
              result.data
                .statelessCcPhysicalRwInsertGriddataDtlPartsMatrixFileUploadLog
                .results[0]
            ) {
              if (
                result.data
                  .statelessCcPhysicalRwInsertGriddataDtlPartsMatrixFileUploadLog
                  .results[0].status == 1
              ) {
                setopenSnackbar(true);
                setGoalFail(false);
                console.log('enter=1');
                // setSuccessMsg(
                //   'Parts matrix file has been successfully uploaded'
                // );
                setSuccessMsg(
                  result.data
                    .statelessCcPhysicalRwInsertGriddataDtlPartsMatrixFileUploadLog
                    .results[0].msg
                );
                props.onUpdateData(true);
                setTimeout(() => {
                  props.handleCancel();
                }, 1300);
              }
              if (
                result.data
                  .statelessCcPhysicalRwInsertGriddataDtlPartsMatrixFileUploadLog
                  .results[0].status == 0
              ) {
                setopenSnackbar(true);
                console.log('enter=1234');
                setGoalFail(true);
                setSuccessMsg(
                  result.data
                    .statelessCcPhysicalRwInsertGriddataDtlPartsMatrixFileUploadLog
                    .results[0].msg
                );
                // setSuccessMsg(
                //   'The file format is not supported. Please ensure your file matches the format outlined in the provided sample CSV file.'
                // );
                // setTimeout(() => {
                //   props.handleCancel();
                // }, 1300);
              }
            }
          }
        );
      }
    } else if (props.typeFor == 'grid') {
      // if (selectedGridType) {
      //   setGridTypeError('');
      // } else {
      //   setGridTypeError('Please Enter Grid Type.');
      // }

      if (files) {
        setFileError('');
      } else {
        setFileError('Please upload the labor grid file.');
        setIsLoading(false);
      }

      if (!files) {
        setError('Please upload the labor grid file.');
        setIsLoading(false);
        return;
      } else {
        setError('');
        // var source = [];
        // source.push(selectedPartsSource);
        var inGridOrFleet = props.gridRateValueStart;
        gridTypeFileUpload(
          files,
          fileName,
          props.fleetName,
          // selectedGridType,
          selectedDate,
          inGridOrFleet,
          result => {
            console.log('result==1=============', result);
            const data = JSON.parse(
              result.data
                .statelessCcPhysicalRwInsertGriddataDtlFileUploadLogMultiple
                .json
            );
            setIsLoading(false);
            
            // if (
            //   result.data.statelessCcPhysicalRwInsertGriddataDtlFileUploadLog
            //     .results[0]
            // ) {
            //   if (
            //     result.data.statelessCcPhysicalRwInsertGriddataDtlFileUploadLog
            //       .results[0].status == 1
            //   ) {
            //     setopenSnackbar(true);
            //     setGoalFail(false);
            //     console.log('enter=1');
            //     // setSuccessMsg('Labor grid file has been successfully uploaded');
            //     setSuccessMsg(
            //       result.data
            //         .statelessCcPhysicalRwInsertGriddataDtlFileUploadLog
            //         .results[0].msg
            //     );
            //     props.onUpdateData(true);
            //     setTimeout(() => {
            //       props.handleCancel();
            //     }, 1300);
            //   }
            //   if (
            //     result.data.statelessCcPhysicalRwInsertGriddataDtlFileUploadLog
            //       .results[0].status == 0
            //   ) {
            //     setopenSnackbar(true);
            //     console.log('enter=1234');
            //     setGoalFail(true);
            //     setSuccessMsg(
            //       result.data
            //         .statelessCcPhysicalRwInsertGriddataDtlFileUploadLog
            //         .results[0].msg
            //     );

            //     // setSuccessMsg(
            //     //   'The file format is not supported. Please ensure your file matches the format outlined in the provided sample CSV file.'
            //     // );
            //     // setTimeout(() => {
            //     //   props.handleCancel();
            //     // }, 1300);
            //   }
            // }

            data.forEach(res => {
              if (res.code == 1) {
                setopenSnackbar(true);
                setGoalFail(false);
                // setSuccessMsg('Labor grid file has been successfully uploaded');

                props.onUpdateData(true);
                setTimeout(() => {
                  props.handleCancel();
                }, 1300);
                setSuccessMsg(`${res.message}`);
              } else {
                setopenSnackbar(true);
                console.log('enter=1234');
                setGoalFail(true);
                setSuccessMsg(res.message || 'Operation completed');
              }
            });
          }
        );
      }
    }
  };

  const hidesnackbar = () => {
    setopenSnackbar(false);
  };

  const handleSelectChange = event => {
    const { value } = event.target;

    if (value.includes('all')) {
      if (allSelected) {
        setSelectedPartsSource([]);
        setAllSelected(false);
      } else {
        setSelectedPartsSource(partsSource.map(option => option.prtsourceList));
        setAllSelected(true);
      }
    } else {
      setSelectedPartsSource(value);
      setAllSelected(false);
    }
  };
  const handleGridTypeChange = event => {
    setSelectedGridType(event.target.value);
  };
  const handleChangeDate = event => {
    const { value } = event.target;
    const isValid = moment(value, 'MM/DD/YY', true).isValid();
    if (!isValid) {
      setInstalledDateError(true);
    } else {
      setInstalledDateError(false);
    }
  };
  const handleCallback = (start, end, label) => {
    console.log('start===', start);
    setSelectedDate(start.format('MM/DD/YY'));
    setInstalledDateError(false);
  };
  const downloadCSV = () => {
    const csvDataParts = [
      [
        'price_start_range',
        'price_end_range',
        'calc_base',
        'break_field',
        'add_percentage'
      ],
      ['0.01', '9.99', 'LIST%', 'LIST', '180'],
      ['10', '17.99', 'LIST%', 'LIST', '175'],
      ['18', '25.99', 'LIST%', 'LIST', '170'],
      ['26', '39.99', 'LIST%', 'LIST', '170'],
      ['40', '69.99', 'LIST%', 'LIST', '165'],
      ['70', '139.99', 'LIST%', 'LIST', '150'],
      ['140', '299.99', 'LIST%', 'LIST', '130'],
      ['300', '599.99', 'LIST%', 'LIST', '115'],
      ['600', '99999.99', 'LIST%', 'LIST', '100']
    ];

    const csvDataLabor = [
      ['', '0', '0.1', '0.2', '0.3', '0.4', '0.5', '0.6', '0.7', '0.8', '0.9'],
      [
        '0',
        '1',
        '19.5',
        '39',
        '58.5',
        '78',
        '97.5',
        '117',
        '136.5',
        '156',
        '175.5'
      ],
      [
        '1',
        '195',
        '214.61',
        '234.24',
        '253.89',
        '273.56',
        '293.25',
        '312.96',
        '332.69',
        '352.44',
        '372.21'
      ],
      [
        '2',
        '392',
        '411.81',
        '431.64',
        '451.49',
        '471.36',
        '491.25',
        '511.16',
        '531.09',
        '551.04',
        '571.01'
      ],
      [
        '3',
        '591',
        '611.01',
        '631.04',
        '651.09',
        '671.16',
        '691.25',
        '711.36',
        '731.49',
        '751.64',
        '771.81'
      ],
      [
        '4',
        '792',
        '812.21',
        '832.44',
        '852.69',
        '872.96',
        '893.25',
        '913.56',
        '933.89',
        '954.24',
        '974.61'
      ],
      [
        '5',
        '995',
        '1015',
        '1035',
        '1056',
        '1076',
        '1096',
        '1116',
        '1138',
        '1158',
        '1179'
      ],
      [
        '6',
        '1200',
        '1220',
        '1240',
        '1261',
        '1281',
        '1302',
        '1322',
        '1344',
        '1365',
        '1386'
      ],
      [
        '7',
        '1407',
        '1427',
        '1447',
        '1468',
        '1489',
        '1511',
        '1532',
        '1553',
        '1573',
        '1595'
      ],
      [
        '8',
        '1616',
        '1637',
        '1658',
        '1679',
        '1699',
        '1722',
        '1742',
        '1762',
        '1784',
        '1805'
      ],
      [
        '9',
        '1827',
        '1848',
        '1868',
        '1890',
        '1911',
        '1932',
        '1953',
        '1975',
        '1997',
        '2018'
      ],
      [
        '10',
        '2040',
        '2061',
        '2082',
        '2103',
        '2125',
        '2151',
        '2173',
        '2192',
        '2214',
        '2233'
      ],
      [
        '11',
        '2255',
        '2274',
        '2296',
        '2316',
        '2337',
        '2657',
        '2378',
        '2398',
        '2419',
        '2439'
      ],
      [
        '12',
        '2460',
        '2480',
        '2501',
        '2521',
        '2542',
        '2562',
        '2583',
        '2602',
        '2624',
        '2643'
      ],
      [
        '13',
        '2665',
        '2684',
        '2705',
        '2725',
        '2747',
        '2766',
        '2788',
        '2807',
        '2829',
        '2948'
      ],
      [
        '14',
        '2968',
        '2889',
        '2911',
        '2931',
        '2952',
        '2972',
        '2993',
        '3013',
        '3034',
        '3054.5'
      ],
      [
        '15',
        '3075',
        '3095.5',
        '3116',
        '3136.5',
        '3157',
        '3177.5',
        '3198',
        '3218.5',
        '3239.3',
        '259.5'
      ]
    ];
    let csvContent;
    if (props.typeFor === 'matrix') {
      csvContent = csvDataParts.map(e => e.join(',')).join('\n');
    } else {
      const filteredLaborData = csvDataLabor.map(row => row.slice(0, 11)); // Keep only the first 11 columns
      csvContent = filteredLaborData.map(e => e.join(',')).join('\n');
    }

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute(
      'download',
      props.typeFor === 'matrix' ? 'partsmatrix.csv' : 'laborgrid.csv'
    );
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  const handleAlert = alert => {
    if (alert.includes('removed')) {
      setopenSnackbar(true);
      setGoalFail(false);
      setSuccessMsg(alert);
      setTimeout(() => {
        setopenSnackbar(false);
      }, 1000);
    }
  };
  console.log('sourceError==', sourceError);
  return (
    <Paper elevation={3} style={{ padding: '20px', paddingBottom: '35px' }}>
      {/* <Typography variant="h5" gutterBottom>
        {props.typeFor == 'matrix' ? 'Upload New Matrix' : 'Upload New Grid'}
      </Typography> */}
      <Grid container spacing={6}>
        {/* <Grid item xs={12} sm={2}>
          {props.typeFor == 'matrix' ? (
            <TextField
              id="outlined-select-matrix-value"
              select
              size="small"
              style={{
                marginRight: 5,
                width: '100%'
                //display: this.state.partsMatrix ? 'block' : 'none'
              }}
              label="Matrix Type"
              SelectProps={{
                native: true
              }}
              classes={{
                root: 'matrix-value'
              }}
              placeholder={'Select'}
              value={selectedMatrixType}
              onChange={handleMatrixTypeChange}
              //placeholder=" select your currency"
              variant="outlined"
            >
              {matrixType.map(item => (
                <option key={item.value} value={item.value}>
                  {item.value}
                </option>
              ))}
            </TextField>
          ) : (
            <>
              <TextField
                id="outlined-select-matrix-value"
                size="small"
                style={{
                  marginRight: 5,
                  width: '100%'
                  //display: this.state.partsMatrix ? 'block' : 'none'
                }}
                label={
                  <span>
                    Grid Type <span style={{ color: 'red' }}>*</span>
                  </span>
                }
                SelectProps={{
                  native: true
                }}
                classes={{
                  root: 'matrix-value'
                }}
                placeholder={'Grid Type'}
                value={selectedGridType}
                onChange={handleGridTypeChange}
                //placeholder=" select your currency"
                variant="outlined"
              />
              {gridTypeError && (
                <p
                  style={{
                    marginTop: 5,
                    color: 'tomato',
                    marginLeft: 6,
                    fontSize: '11px'
                  }}
                >
                  {gridTypeError}
                </p>
              )}
            </>
          )}
        </Grid>
        {props.typeFor == 'matrix' && (
          <Grid item xs={12} sm={2}>
            <FormControl fullWidth>
              <InputLabel style={{ marginLeft: 16, top: '-5px' }}>
                Parts Source <span className={classes.star}>*</span>
              </InputLabel>
              <Select
                multiple
                value={selectedPartsSource}
                style={{ height: 40 }}
                onChange={handleSelectChange}
                input={<OutlinedInput label="Parts Source" />}
                startAdornment={
                  <InputAdornment position="start"></InputAdornment>
                }
                id="selectPartsSource"
                renderValue={selected => (
                  <div gap={1} direction="row" flexWrap="wrap">
                    {selectedPartsSource.map(value => (
                      <Chip
                        key={value}
                        label={value}
                        onDelete={() =>
                          setSelectedPartsSource(
                            selectedPartsSource.filter(item => item !== value)
                          )
                        }
                        deleteIcon={
                          <CancelIcon
                            onMouseDown={event => event.stopPropagation()}
                          />
                        }
                      />
                    ))}
                  </div>
                )}
                MenuProps={{
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left'
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left'
                  },
                  getContentAnchorEl: null // This ensures the menu is not anchored to the text field
                }}
                renderValue={selected => (
                  <div>
                    {selected.map(value => (
                      <Chip key={value} label={value} />
                    ))}
                  </div>
                )}
              >
                <MenuItem value="all" selected={allSelected}>
                  <Checkbox checked={allSelected} />
                  <ListItemText primary="Select All" />
                </MenuItem>
                {partsSource.map(item => (
                  <MenuItem key={item.prtsourceList} value={item.prtsourceList}>
                    <Checkbox
                      checked={
                        selectedPartsSource.indexOf(item.prtsourceList) > -1
                      }
                    />
                    <ListItemText primary={item.prtsourceList} />
                  </MenuItem>
                ))}
              </Select>
              {sourceError && (
                <p
                  style={{
                    marginTop: 5,
                    color: 'tomato',
                    marginLeft: 6,
                    fontSize: '11px'
                  }}
                >
                  {sourceError}
                </p>
              )}
            </FormControl>
          </Grid>
        )} */}
        <Grid item xs={12} sm={2}>
          <FormControl
            variant="outlined"
            margin="dense"
            className={clsx(classes.formControl, 'input-container')}
            style={{ width: '100%' }}
          >
            <DateRangePicker
              initialSettings={{
                locale: {
                  format: 'MM/DD/YY',
                  separator: ' - '
                },
                autoUpdateInput: true,
                showDropdowns: true,
                autoApply: true,
                singleDatePicker: true,

                applyClass: clsx(classes.calButton, 'apply-btn'),
                cancelClass: clsx(classes.calButton, 'apply-btn')
              }}
              value={selectedDate}
              onCallback={handleCallback}
            >
              <input
                // readOnly
                type="text"
                className="datepicker fleet-picker fleet-picker-cls"
                id="fleet-picker"
                name="picker"
                aria-labelledby="label-picker"
                onChange={handleChangeDate}
                onBlur={handleChangeDate}
                style={{ marginTop: -8, height: 40, width: '100%' }}
              />
            </DateRangePicker>
            <label
              class="labelpicker fleet-picker"
              for="picker"
              id="label-picker"
            >
              <div class="textpicker fleet-picker-label">
                Install Date <span className={classes.star}>*</span>
              </div>
            </label>
          </FormControl>
          {installedDateError == true && (
            <FormControl>
              <div
                style={{
                  position: 'absolute',
                  width: '250px',
                  paddingLeft: '40px',
                  fontSize: '13px',
                  color: 'red',
                  top: '10px'
                }}
              >
                Enter Dates in MM/DD/YY Format
              </div>
            </FormControl>
          )}
        </Grid>
        <Grid item xs={12} sm={6}></Grid>
        {props.typeFor == 'grid' && <Grid item xs={12} sm={2}></Grid>}
      </Grid>
      {/* <Typography variant="subtitle1" className={classes.headerDes}>
        {props.typeFor == 'matrix'
          ? 'Difference Between Price Start Range value and Last Price End Range Value should be 0.01'
          : 'The number of data rows in the uploaded file should not exceed 17'}
      </Typography> */}
      {isLoading && (
        <Grid justify="center" className={classes.loaderGrid}>
          <CircularProgress size={60} />
        </Grid>
      )}
      <Grid container spacing={3}>
        <Grid item xs={12} sm={3} className={classes.snackbarContainer}>
          <DropzoneArea
            // previewGridClasses={{
            //   item: classes.preview
            // }}
            acceptedFiles={['.csv']}
            maxFiles={1}
            filesLimit={1}
            dropzoneText={'Click here to upload a CSV file'}
            showAlerts={['error']}
            onChange={handleFileChange}
            onDelete={handleDeleteFile}
            maxHeight={2}
            classes={{
              root: fileError ? classes.fileError : classes.root,
              icon: classes.icon
            }}
            dropzoneClass="matrix-upload"
            iconStyle={{
              fontSize: '48px', // Example icon size
              color: 'red !important',
              marginBottom: '-5px !important'
            }}
            showFileNames
            dropzoneProps={{
              style: {
                // Custom style for the dropzone area
                border: '2px dashed #e53935 !important',
                borderRadius: '5px',
                backgroundColor: '#fafafa',
                maxHeight: '10px',
                padding: '20px',
                textAlign: 'center'
              }
            }}
            onAlert={handleAlert}
          />
          {fileError && (
            <p
              style={{
                marginTop: 5,
                color: 'tomato',
                marginLeft: 6,
                fontSize: '11px'
              }}
            >
              {fileError}
            </p>
          )}
        </Grid>
        <Grid item xs={12} sm={3}>
          <Button
            variant="contained"
            color="primary"
            onClick={downloadCSV}
            // style={{ marginTop: 20 }}
            className={clsx('reset-btn', 'btnClass')}
            style={{
              height: '24px',
              fontSize: '15px',
              marginTop: 20
            }}
          >
            Download Sample CSV
          </Button>
        </Grid>
        <Grid item xs={12} sm={3}></Grid>
      </Grid>

      <Grid item xs={2} style={{ float: 'right' }}>
        <div>
          <Button
            title="Cancel"
            className={clsx('reset-btn', 'btnClass')}
            variant="contained"
            color="primary"
            style={{
              marginLeft: 5,
              width: '55px',
              height: '24px',
              fontSize: '15px',
              marginTop: 1
            }}
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            title="Save"
            className={clsx('reset-btn')}
            style={{
              marginLeft: 5,
              width: '55px',
              height: '24px',
              fontSize: '15px',
              marginTop: 1
            }}
            variant="contained"
            onClick={handleSubmit}
            disabled={installedDateError == false ? false : true}
          >
            Save
          </Button>
        </div>
        {successMsg && (
          <SuccessSnackbar
            open={openSnackbar}
            onClose={hidesnackbar}
            msg={successMsg}
            goalFail={goalFail}
            autoHideDuration={6000} // 6 seconds
          />
        )}
      </Grid>
    </Paper>
  );
};

const App = props => {
  const [selectedOption, setSelectedOption] = useState('add');
  useEffect(() => {
    props.showPartsMatrix('add');
  }, []);
  const handleCancel = () => {
    // setSelectedOption('');
    props.handleClose(true);
  };
  const handleChange = event => {
    setSelectedOption(event.target.value);
    props.showPartsMatrix(event.target.value);
  };

  return (
    <div style={{ padding: '20px', marginLeft: -20 }}>
      <FormControl component="fieldset">
        <RadioGroup
          aria-label="matrixOption"
          name="matrixOption"
          value={selectedOption}
          onChange={handleChange}
          row // Add the row prop to the RadioGroup component
        >
          <Grid container>
            <Grid item>
              <FormControlLabel
                value="add"
                control={<Radio />}
                label={
                  props.typeFor == 'matrix'
                    ? 'Use an Existing Fleet Matrix'
                    : 'Use an Existing Fleet Grid'
                }
              />
            </Grid>
            <Grid item>
              <FormControlLabel
                value="upload"
                control={<Radio />}
                label={
                  props.typeFor == 'matrix'
                    ? 'Upload New Matrix'
                    : 'Upload New Grid'
                }
              />
            </Grid>
          </Grid>
        </RadioGroup>
      </FormControl>
      {selectedOption === 'upload' && (
        <UploadMatrixForm
          handleCancel={handleCancel}
          typeFor={props.typeFor}
          fleetName={props.fleetName}
          gridRateValueStart={props.gridRateValueStart}
          onUpdateData={props.onUpdateData}
        />
      )}
      {/* {selectedOption === 'add' && (
        <AddMatrixForm handleCancel={handleCancel} />
      )} */}
    </div>
  );
};

export default App;
