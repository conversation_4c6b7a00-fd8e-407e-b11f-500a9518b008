/* eslint-disable react/default-props-match-prop-types */
/* eslint-disable react/jsx-no-duplicate-props */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable no-shadow */
/* eslint-disable react/static-property-placement */
/* eslint-disable no-unused-vars */
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
// eslint-disable-next-line import/no-extraneous-dependencies
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  Divider,
  fade,
  LinearProgress,
  Paper,
  Tooltip,
  Typography
} from '@material-ui/core';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import ExportIcon from '@material-ui/icons/GetApp';
import { withKeycloak } from '@react-keycloak/web';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
// eslint-disable-next-line import/no-extraneous-dependencies
import $ from 'jquery';
import React from 'react';
import Page from 'src/components/Page';
import 'react-grid-layout/css/styles.css';

import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgres';
import { getGridorMatrixDetails } from 'src/utils/hasuraServices';
import Button from '@material-ui/core/Button';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import DialogTitle from '@material-ui/core/DialogTitle';
import moment from 'moment';
import Alert from '@material-ui/lab/Alert';
import IconButton from '@material-ui/core/IconButton';
import Collapse from '@material-ui/core/Collapse';
import CloseIcon from '@material-ui/icons/Close';
import PropTypes from 'prop-types';
import { SET_REFRESH_STATUS } from 'src/actions';
import { connect } from 'react-redux';
import { setRefreshStatus } from '../../actions';
import { withStyles } from '@material-ui/styles';
var Dealer = process.env.REACT_APP_DEALER;

class GridorMetrix extends React.Component {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);

    this.state = {
      rawGridApi: {},
      // eslint-disable-next-line react/no-unused-state
      gridApi: {},
      isLoading: true,
      columnDefs: [
        {
          headerName: 'Abbreviation',
          chartDataType: 'series',
          field: 'shortyKey',
          editable: false,
          width: 6.5,
          minWidth: 6.5,
          // flex:1,

          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          suppressFilter: true,
          cellStyle() {
            return {
              textAlign: 'left',
              border: ' 0px white',
              fontSize: '14px'
            };
          }
        },
        {
          headerName: 'Value',
          field: 'value',
          // flex: 1,
          width: 15,
          minWidth: 15,

          // resizable: false,
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          suppressFilter: true,
          cellStyle() {
            return {
              border: ' 0px white',
              textAlign: 'left',
              fontSize: '14px'
            };
          }
        }
      ],

      rowData: [],

      defaultColDef: {
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        enableValue: true,
        enableFilter: false,
        sortable: true,
        //  filter: true,
        //  resizable: false,
        editable: true,
        suppressMovable: false
      },
      editType: 'fullRow',
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  // eslint-disable-next-line react/sort-comp
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    this.getAgGridData();
  };

  getAgGridData() {
    this.setState({ isLoading: true });
    getGridorMatrixDetails(result => {
      this.setState({ isLoading: false });
      if (
        result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes
      ) {
        this.setState({
          rowData:
            result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
              .nodes
        });
      }
    });
  }

  onBtExport = () => {
    const params = {
      sheetName: 'Report',
      columnWidth: 150,
      columnKeys: ['opcode', 'opcategory', 'menu_sales', 'client_id'],
      fileName: 'Opcodes',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Op Codes' },
            mergeAcross: 3
          }
        ]
      ]
    };

    // eslint-disable-next-line react/destructuring-assignment
    this.state.rawGridApi.exportDataAsExcel(params);
  };

  render() {
    const { classes } = this.props;

    return (
      <div>
        <Paper
          square
          style={{
            margin: 8,
            marginTop: '40px',
            paddingTop: '6px'
            //  height: '40px',
          }}
        >
          <Grid item xs={12} style={{ justifyContent: 'left' }}>
            <Typography
              style={{ padding: 8 }}
              variant="h4"
              className={clsx(classes.mainLabel)}
            >
              Glossary
            </Typography>
            <Divider></Divider>
            {this.state.isLoading === true ? (
              <div>
                <Box style={{ padding: 25 }}>
                  <LinearProgress color="secondary" />
                  <Typography
                    variant="h6"
                    align="center"
                    style={{ padding: 25 }}
                    color="primary"
                  >
                    Processing...
                  </Typography>
                </Box>
              </div>
            ) : null}
            <div
              id="data-tab"
              className="ag-theme-balham"
              style={{
                height: `${window.innerHeight - 200}px`,
                width: '400px',
                margin: '6px',

                display: this.state.tabSelection === 'two' ? 'none' : 'block'
              }}
            >
              <AgGridReact
                className="ag-theme-balham"
                style={{
                  height: '400px',
                  width: '100%'
                }}
                autoGroupColumnDef={this.state.autoGroupColumnDef}
                getChartToolbarItems={this.getChartToolbarItems}
                modules={AllModules}
                columnDefs={this.state.columnDefs}
                defaultColDef={this.state.defaultColDef}
                onGridReady={this.onGridReady}
                suppressAggFuncInHeader
                rowData={this.state.rowData}
                excelStyles={this.state.excelStyles}
                tooltipShowDelay={0}
                editType={this.state.editType}
                suppressHorizontalScroll={true}
                suppressClickEdit={true}
                onCellClicked={this.onCellClicked}
                floatingFilter={true}
                enableRangeSelection={true}
                animateRows={true}
                enableCharts={true}
                suppressRowClickSelection={true}
                suppressDragLeaveHidesColumns={true}
                suppressContextMenu={true}
              />
            </div>
          </Grid>
        </Paper>
      </div>
    );
  }
}

GridorMetrix.propTypes = {
  keycloak: PropTypes.any,
  handleReload: PropTypes.func,
  handleRefresh: PropTypes.func,
  setRefreshStatus: PropTypes.func,
  setRefreshErrorStatus: PropTypes.func
};
const styles = theme => ({
  formControl: {
    margin: theme.spacing(4),
    minWidth: 120,
    paddingLeft: 8,
    paddingRight: 4
  },

  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    //marginTop: 10,
    display: 'flex',
    color: 'rgb(0, 61, 107)'
  },
  back: {
    marginRight: 10,
    float: 'right'
  }
});
export default withStyles(styles)(GridorMetrix);
