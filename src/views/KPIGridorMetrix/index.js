import React from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import GridorMetrix from './GridorMetrix';
import { setRefreshStatus, setReloadStatus } from 'src/actions';
import { useDispatch } from 'react-redux';

// const useStyles = makeStyles(theme => ({
//   root: {
//     paddingTop: theme.spacing(0),
//     paddingBottom: theme.spacing(3)
//   },
//   statistics: {
//     marginTop: theme.spacing(3)
//   },
//   notifications: {
//     marginTop: theme.spacing(6)
//   },
//   projects: {
//     marginTop: theme.spacing(6)
//   },
//   todos: {
//     marginTop: theme.spacing(6)
//   }
// }));

function glossary() {
  // const classes = useStyles();
  // const dispatch = useDispatch();
  const handleRefresh = status => {
    //dispatch(setRefreshStatus(status));
  };
  const handleReload = status => {
    // dispatch(setReloadStatus(status));
  };
  return (
    <Page title="Glossary">
      <GridorMetrix
        handleRefresh={handleRefresh.bind(this)}
        handleReload={handleReload.bind(this)}
      />
    </Page>
  );
}

export default glossary;
