import React from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import Technician from './Technician';
import {
  setTechnicianErrors,
  setTechnicianErrorsCount,
  setAllErrorsCount
} from 'src/actions';
import { getAllOpcodeErrors } from 'src/utils/hasuraServices';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';
import { useHistory } from 'react-router';

var lodash = require('lodash');
const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function TechniciansEdit() {
  const classes = useStyles();
  const dispatch = useDispatch();
  const history = useHistory();

  const [notifications, setNotifications] = React.useState([]);
  const session = useSelector(state => state.session);
  const addErrorCount = status => {
    let mounted = true;
    getAllOpcodeErrors(callback => {
      const technicianErrorArr = callback.filter(function(el) {
        return el.cType === 'technician';
      });
      console.log('technicianErrorArr', technicianErrorArr);
      var err = callback.reduce(
        (acc, o) => acc + parseInt(o.noncategorizedcount),
        0
      );
      var b = lodash.filter(callback, function(o) {
        if (o.noncategorizedcount > 0) return o;
      }).length;
      dispatch(setAllErrorsCount(b));
      //dispatch(setAllErrorsCount(err));
      if (technicianErrorArr.length > 0) {
        if (mounted) {
          if (technicianErrorArr[0].noncategorizedcount > 0) {
            dispatch(setTechnicianErrors(true));
            dispatch(
              setTechnicianErrorsCount(
                technicianErrorArr[0].noncategorizedcount
              )
            );
          } else {
            dispatch(setTechnicianErrors(false));
            dispatch(setTechnicianErrorsCount(0));
          }
        }
      } else {
        setNotifications([]);
        dispatch(setTechnicianErrors(false));
        dispatch(setTechnicianErrorsCount(0));
      }
    });
  };
  return (
    <Page className={classes.root} title="Technician Setup">
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <Technician
          dispatch={dispatch}
          setErrorCount={addErrorCount}
          session={session}
          history={history}
        />
      )}
    </Page>
  );
}

export default TechniciansEdit;
