import React, { useState } from 'react';
import { Value } from 'sass';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/styles';
const useStyles = makeStyles(theme => ({
  root: {},
  formControlSelectdropdown: {
    background: '#fff',
    height: '24px',
    border: '1px solid #ccc',
    borderRadius: '4px',
    marginTop: '2px',
    paddingLeft: 4,
    width: '80px'
  }
}));

function DropDownRender(params, state) {
  const [selection, setSelection] = useState(
    params.value != null ? params.value : 'None'
  );

  const [selectionStatus, setStatusSelected] = useState(false);
  const [teamTech, setTeamTech] = useState(
    typeof params.context.componentParent.state.teamTech != 'undefined'
      ? params.context.componentParent.state.teamTech
      : params.dropDown
  );
  const classes = useStyles();

  const handleDropDown = e => {
    let colId = params.column.colId;
    params.node.setDataValue(colId, e.target.value);
    setSelection(e.target.value);
    // setStatusSelected(true)
    params.onCellClicked(true);
  };

  return (
    <div className="row">
      <div className="col">
        {/* {params.context.componentParent.state.editedRowId != null ? ( */}
        <select
          style={{
            fontSize: 12,
            fontFamily: ['Roboto', 'sans - serif'].join(','),
            display:
              params.context.componentParent.state.editedRowId != null
                ? params.context.componentParent.state.editedRowId ==
                  params.rowIndex
                  ? 'block'
                  : 'none'
                : 'none'
          }}
          className={clsx(classes.formControlSelectdropdown, 'teamTechSelect')}
          onChange={handleDropDown}
          value={selection}
          disabled={
            params.context.componentParent.state.editedRowId == null
              ? true
              : params.context.componentParent.state.editedRowId !=
                params.rowIndex
              ? true
              : params.data.isTeam == 1
              ? true
              : false
          }
        >
          {teamTech.map(i => {
            return (
              <option key={i} value={i}>
                {i}
              </option>
            );
          })}
        </select>
        {/* ) : ( */}
        <span
          style={{
            width: 110,
            border: 'none',
            fontSize: 12,
            fontFamily: ['Roboto', 'sans - serif'].join(','),
            display:
              params.context.componentParent.state.editedRowId == null
                ? 'block'
                : params.context.componentParent.state.editedRowId !=
                  params.rowIndex
                ? 'block'
                : 'none'
          }}
          disabled={
            params.context.componentParent.state.editedRowId == null
              ? 'block'
              : params.context.componentParent.state.editedRowId !=
                params.rowIndex
              ? true
              : false
          }
        >
          {selection ? selection : 'None'}
        </span>
        {/* )} */}
      </div>
    </div>
  );
}

export default DropDownRender;
