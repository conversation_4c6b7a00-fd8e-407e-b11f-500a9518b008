import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  LinearProgress,
  Paper,
  Typography,
  withStyles,
  Snackbar
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import Button from '@material-ui/core/Button';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import $ from 'jquery';
import React from 'react';
import RestoreIcon from '@material-ui/icons/Restore';
import clsx from 'clsx';
import 'react-grid-layout/css/styles.css';
import { setReloadStatus } from 'src/actions';
import {
  DELETE_SERVICE_ADVISOR,
  INSERT_SERVICE_ADVISOR,
  UPDATE_TECHNICIAN_STATUS,
  UPDATE_TECH_TEAM_SETTINGS
} from 'src/graphql/queries';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import { getAllTechnicians } from 'src/utils/hasuraServices';
import { render } from 'react-dom';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import { withKeycloak } from '@react-keycloak/web';
import ActiveStatusRenderer from './ActiveStatusRenderer';
import { ReactSession } from 'react-client-session';
import NickNameCheckboxRenderer from './NickNameCheckboxRenderer';
import dropDownRenderer from './dropdownRenderer';
import { setTechnicianStatus } from 'src/actions';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');

class Technicians extends React.Component {
  componentWillMount() {
    // this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }

  componentDidUpdate() {
    // if(ReactSession.get("serviceAdvisors") != undefined) {
    //   var checkStatus = lodash.isEqual(
    //     this.state.serviceAdvisors,
    //     ReactSession.get("serviceAdvisors")
    //   );
    //   if(checkStatus == false) {
    //     this.getAgGridData();
    //   }
    // }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ isLoading: true });
        this.setState({ teamTech: [] });
        this.setState({ store: localStorage.getItem('selectedStoreId') });

        this.getTeamTech();
        this.gridApi.setSortModel(null);
        this.gridApi.setFilterModel(null);
        // his.gridApi.setColumnDefs([]);
        // this.gridApi.setColumnDefs(this.state.columnDefs);
        console.log('ttt==ttt', this.state.teamTech);
        var teamArr = this.state.teamTech;
        const newColumns = this.state.columnDefs;
        newColumns.forEach(function(colDef, index) {
          if (colDef.field == 'teamAssignedTo') {
            // teamArr = colDef.cellRendererParams.dropDown;
            // if (teamActive == '1') {
            //   if (!teamArr.includes(techno)) {
            //     //checking weather array contain the id
            //     teamArr.push(techno); //adding to array because value doesnt exists
            //   }
            // } else {
            //   if (teamArr.includes(techno)) {
            //     teamArr.splice(teamArr.indexOf(techno), 1); //deleting
            //   }
            // }
            colDef.cellRendererParams.dropDown = teamArr;
          }
        });
        this.getAgGridData();

        this.gridApi.setColumnDefs(newColumns);
        this.gridApi.redrawRows();

        this.setState({
          editedRowId: null
        });
        this.gridApi.setRowData(this.state.rowData);
      }
    }
  }
  // componentDidUpdate(prevProps) {
  //   if(prevProps.session.storeSelected && JSON.parse(localStorage.getItem('selectedStoreId')) ){
  //     if(JSON.parse(localStorage.getItem('selectedStoreId'))[0]!=JSON.parse(prevProps.session.storeSelected)[0])
  //     {
  //       console.log("stores=",JSON.parse(localStorage.getItem('selectedStoreId'))[0] == JSON.parse(prevProps.session.storeSelected)[0]);
  //       this.getAgGridData();
  //     }
  //   }
  // }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    let startEdit = this;
    this.addNewRow = this.addNewRow.bind(this);

    let editClick = false;
    console.log('techprop', props);
    this.state = {
      nickError: false,
      cancel: false,
      openSnackbar: false,
      showCharts: false,
      selectValue: 'other',
      successmsg: '',
      selectValueFor: this.props.category,
      selectValueType: this.props.reportType,
      ddLabel: '< 2',
      rawGridApi: {},
      gridApi: {},
      teamtech: [],
      isLoading: true,
      editClick: editClick,
      technicianStatus: '',
      success: false,
      // storeId: '75627608',
      dropdownSelect: false,
      isEdited: false,
      oldDataArr: [],
      newDataArr: [],
      prevDataArr: [],

      editedRowId: null,
      columnDefs: [
        {
          headerName: ' Id',
          chartDataType: 'series',

          width: 150,
          field: 'id',
          hide: true,
          editable: false
        },
        {
          headerName: 'Tech Number',
          chartDataType: 'series',
          width: 150,
          field: 'lbrtechno',

          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB, ascending) {
            // Function to check if a value is numeric
            function isNumeric(value) {
              return /^\d+$/.test(value);
            }

            // Check if both values are numeric
            var isANumeric = isNumeric(valueA);
            var isBNumeric = isNumeric(valueB);

            var compareResult;

            if (isANumeric && isBNumeric) {
              // Both are numeric, compare as numbers
              var numA = Number(valueA);
              var numB = Number(valueB);
              compareResult = numA - numB;
            } else if (!isANumeric && !isBNumeric) {
              // Both are alphabetic strings, compare as case-insensitive strings
              var lowerA = valueA.toLowerCase();
              var lowerB = valueB.toLowerCase();
              if (lowerA > lowerB) {
                compareResult = 1;
              } else if (lowerA < lowerB) {
                compareResult = -1;
              } else {
                compareResult = 0;
              }
            } else {
              // One is numeric and the other is alphabetic
              // Alphabetic values should come after numeric values
              compareResult = isANumeric ? -1 : 1;
            }

            // Reverse the result for descending order
            return ascending ? compareResult : -compareResult;
          },
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          editable: function(params) {
            if (params.data.id == '') {
              return true;
            } else {
              return false;
            }
          }
        },
        {
          headerName: 'Name',
          chartDataType: 'series',
          width: 150,
          field: 'name',
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'name',
          cellStyle() {
            return { border: ' 0px white', marginLeft: '6px' };
          }
        },
        {
          headerName: 'Nick Name',
          chartDataType: 'series',
          width: 150,
          field: 'nickname',
          suppressMenu: true,
          singleClickEdit: true,
          unSortIcon: true,
          editable: false,
          cellEditor: 'agTextCellEditor', // Use the default text editor
          cellEditor: 'agTextCellEditor',
          onCellValueChanged: params => {
            const regExp = /^[a-zA-Z\s]*$/;
            if (!regExp.test(params.newValue)) {
              startEdit.setState({ nickError: true });
              // Set the border to red when invalid input is entered
              params.node.setDataValue('nickname', params.oldValue); // Reset to the old value
              params.api.refreshCells({ rowNodes: [params.node] }); // Refresh to update the cell style
            }
          }
        },
        {
          headerName: 'Dept.',
          chartDataType: 'series',
          // width: 90,
          minWidth: 90,
          flex: 1,
          field: 'department',
          editable: true,
          suppressMenu: true,
          unSortIcon: true,
          cellEditor: 'agSelectCellEditor',
          //hide: localStorage.getItem('dms') == 'dtk' ? true : false,
          hide: false,
          //flex: 2,
          cellEditorParams: {
            values: depart
          },
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          },
          filterParams: {
            valueFormatter: function(params) {
              return lookupValue(departmentMappings, params.value);
            }
          },
          valueFormatter: function(params) {
            return lookupValue(departmentMappings, params.value);
          }
        },
        {
          headerName: 'Active / Inactive',
          chartDataType: 'series',
          width: 150,

          field: 'active',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          //  cellStyle: this.cellStyles,
          // onCellClicked: this.setAdvisorStatus,
          // cellRenderer: function(params) {
          //   var input = document.createElement('input');
          //   input.type="checkbox";
          //   input.id="advisorStatus";
          //   input.checked=params.value == 1 ? 'checked' :'';
          //   input.value=params.value;
          //   input.addEventListener('click', function (event) {
          //     input.value=event.target.checked ? 1 : 0;
          //   });
          //   return input;
          // }
          // cellRenderer: params => {
          //   var index = params.rowIndex;
          //   return `<input type='checkbox' value=${params.value}
          //   id="advisorStatus${index}" disabled=disabled
          //   ${params.value == 1 ? 'checked' : ''} />`;
          // }
          cellEditor: 'agSelectCellEditor',
          //hide: localStorage.getItem('dms') == 'dtk' ? true : false,
          hide: false,
          //flex: 2,
          valueFormatter: function(params) {
            return params.value == 1
              ? 'Active'
              : params.value == null
              ? '----'
              : 'Inactive';
          },
          cellEditorParams: {
            values: this.extractValues(this.objCategoryMappings)
          },
          filterParams: {
            valueFormatter: function(params) {
              return params.value == 1
                ? 'Active'
                : params.value == null
                ? null
                : 'Inactive';
            }
          }
        },
        {
          headerName: 'Team Tech',
          width: 120,
          field: 'isTeam',

          cellRenderer: 'NickNameCheckboxRenderer',
          suppressMenu: true,
          unSortIcon: true,
          hide: false,
          filter: false,
          editable: false,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Team Assigned To',
          chartDataType: 'series',
          width: 180,
          field: 'teamAssignedTo',

          suppressMenu: true,
          unSortIcon: true,
          //flex: 1,
          editable: false,
          singleClickEdit: true,

          cellStyle() {
            return { border: ' 0px white' };
          },
          cellRenderer: 'dropDownRenderer',
          cellRendererParams: {
            dropDown: startEdit.getTeamTech(),

            onCellClicked: this.onCellClickedDD
          },
          valueGetter: function(params) {
            if (params.data.endDate != null) {
              return 'None';
            } else {
              return params.data.teamAssignedTo;
            }
          }
        },
        {
          headerName: 'End Date',
          chartDataType: 'series',
          width: 180,
          field: 'endDate',

          suppressMenu: true,
          unSortIcon: true,
          hide: true,
          editable: false,
          singleClickEdit: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Action',
          cellRenderer: 'buttonRenderer',
          // tooltip: function(params) {
          //   return 'Edit';
          // },
          width: 200,
          filter: false,
          cellStyle: this.cellStyles,
          sortable: false,
          editable: false,
          suppressMenu: true,

          onCellClicked: this.setEditStatus,
          cellRenderer: function(params) {
            var index = params.rowIndex;
            var eDiv = document.createElement('div');

            if (params.data.id == '' && params.data.name == '') {
              eDiv.innerHTML =
                '<button  title="Edit" id="btnedittechnician' +
                index +
                '" style="background: #384163; color: #fff; display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button-technician"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncanceltechnician' +
                index +
                '" style="background: #384163;color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button-technician" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdatetechnician' +
                index +
                '" style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button-technician" ><i class="fas fa-save"></i></button>&nbsp;<button title="delete" id="btndelete' +
                index +
                '" style="background: #384163;  color: #fff; border-radius: 3px;  width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-button" ><i class="fas fa-trash-alt"></i></button>';
            } else {
              eDiv.innerHTML =
                '<button  title="Edit" id="btnedittechnician' +
                index +
                '" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button-technician"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncanceltechnician' +
                index +
                '" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button-technician" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdatetechnician' +
                index +
                '" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button-technician" ><i class="fas fa-save"></i></button>&nbsp;<button title="delete" id="btndelete' +
                index +
                // '" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-button" ><i class="fas fa-trash-alt"></i></button>';
                '</button>';
            }
            //  this.setState({editClick: true})
            if (
              props.keycloak &&
              props.keycloak.realmAccess.roles.includes('admin') == false &&
              props.keycloak.realmAccess.roles.includes('superadmin') ==
                false &&
              props.keycloak.realmAccess.roles.includes('user') == false
            ) {
              $(document).ready(function() {
                $('.edit-button-technician').attr('disabled', 'disabled');
                $('.edit-button-technician').css('background', '#38416373');
                $('.edit-button-technician').css('cursor', 'default');
              });
              $('.editNickName').attr('disabled', true);
            }
            if (index != undefined) {
              var eButton = eDiv.querySelectorAll('.edit-button-technician')[0];
              var uButton = eDiv.querySelectorAll(
                '.update-button-technician'
              )[0];
              var cButton = eDiv.querySelectorAll(
                '.cancel-button-technician'
              )[0];
              //  var dButton = eDiv.querySelectorAll('.delete-button')[0];
              eButton.addEventListener('click', () => {
                // startEdit.setState({
                //   editedRowId: null
                // });

                $(`.cancel-button-technician`).hide();
                $(`.edit-button-technician`).show();
                $(`.update-button-technician`).hide();
                $(`.teamTechSelect`).hide();
                $('.teamTechCheck').attr('disabled', 'disabled');
                let oldIndex = localStorage.getItem('oldIndex');
                if (oldIndex) {
                  startEdit.onBtStopEditing(oldIndex);
                  let rowData = startEdit.state.oldDataArr;
                  var rowNode = params.api.getDisplayedRowAtIndex(oldIndex);
                  if (rowNode && rowData && rowData.length > 0) {
                    rowNode.setDataValue('nickname', rowData[0].nickname);
                    rowNode.setDataValue('active', rowData[0].active);
                    rowNode.setDataValue('department', rowData[0].department);
                  }
                }
                // if (oldIndex) {
                //   startEdit.onBtStopEditing(oldIndex);
                //   let rowData = startEdit.state.oldDataArr;

                //   var rowNode = params.api.getDisplayedRowAtIndex(oldIndex);

                //   if (rowData.length > 0 && rowNode) {
                //     rowNode.setDataValue('nickname', rowData[0].nickname);
                //     rowNode.setDataValue('active', rowData[0].active);

                //     rowNode.setDataValue(
                //       'teamAssignedTo',
                //       rowData[0].teamAssignedTo == null ||
                //         rowData[0].teamAssignedTo == 'None'
                //         ? 'None'
                //         : rowData[0].teamAssignedTo
                //     );
                //     console.log('rowData===', rowNode);
                //   }
                // }
                if (startEdit.state.editedRowId != null) {
                  var rowNode = params.api.getDisplayedRowAtIndex(
                    startEdit.state.editedRowId
                  );

                  startEdit.setState({
                    prevEditedRowId: startEdit.state.editedRowId
                  });

                  startEdit.setState({
                    editedRowId: null
                  });
                  startEdit.setState({
                    isCodeEdited: false
                  });
                  params.api.refreshCells({
                    columns: ['nickname', 'active', 'teamAssignedTo'],
                    rowNodes: [rowNode],
                    force: true
                  });
                }
                let rowData = startEdit.state.oldDataArr;

                // rowData.map((object, i) => {
                //   let rowNode = params.api.getDisplayedRowAtIndex(object.index);
                //   console.log('rowData=', index, object.index, rowNode);
                //   rowNode.setDataValue('nickname', rowData[i].nickname);
                //   rowNode.setDataValue('active', rowData[i].active);
                //   $('#btnedittechnician' + object.index).show();
                //   $('#btncanceltechnician' + object.index).hide();
                //   $('#btnupdatetechnician' + object.index).hide();
                //   $('#btndelete' + object.index).hide();
                //   $('#techStatus' + object.index).prop(
                //     'checked',
                //     rowData.active == 1 ? true : false
                //   );
                //   $('#techStatus' + object.index).prop('disabled', 'disabled');
                // });

                localStorage.setItem('index', index);
                startEdit.setState({ cancel: false });
                localStorage.setItem('techId', params.data.id);
                localStorage.setItem('nickname', params.data.nickname);
                startEdit.onBtStartEditing(index);
                startEdit.setState({
                  editedRowId: index
                });
                let oldArr = {
                  index: index,
                  name: params.data.name,
                  nickname: params.data.nickname,
                  active: params.data.active,
                  isTeam: params.data.isTeam,
                  teamAssignedTo: params.data.teamAssignedTo,
                  department: params.data.department
                };

                var rowPrevArray = [];
                let indexArr = rowPrevArray.findIndex(
                  ({ name }) => name == params.data.name
                );

                if (indexArr === -1) {
                  rowPrevArray.push(oldArr);
                }

                startEdit.setState({
                  oldDataArr: rowPrevArray
                });
                // startEdit.setState({
                //   oldDataArr: oldArr
                // });
                startEdit.onBtStartEditing(index);
                $('#btnedittechnician' + index).hide();
                $('#btncanceltechnician' + index).show();
                $('#btnupdatetechnician' + index).show();
                $('#btndelete' + index).show();
                $('#techStatus' + index).prop('disabled', '');
                localStorage.setItem('oldIndex', index);
              });

              uButton.addEventListener('click', () => {
                startEdit.onBtStopEditing(index);

                var teamTech = startEdit.state.teamtech;

                var rowNode = params.api.getDisplayedRowAtIndex(
                  params.rowIndex
                );
                if (
                  params.data.teamAssignedTo != null &&
                  !teamTech.includes(params.data.teamAssignedTo)
                ) {
                  console.log('teamTech==aaa==111', teamTech);
                  rowNode.data['teamAssignedTo'] = 'None';
                }
                var selectedId = params.data.lbrtechno;
                var nickName = params.data.nickname;
                nickName = nickName ? nickName.trim() : '';
                var techStatus = params.data.active;
                var selectedDepartment = params.data.department;
                var nickNameOld = localStorage.getItem('nickname').trim();
                startEdit.setState({ cancel: false });
                startEdit.setState({
                  editedRowId: null
                });
                let arr = startEdit.state.oldDataArr;

                if (
                  arr[0].nickname != params.data.nickname ||
                  arr[0].active != params.data.active ||
                  arr[0].department != params.data.department
                ) {
                  let arrNickname = params.data.nickname
                    ? params.data.nickname.trim()
                    : '';
                  let arrNickname1 = arr[0].nickname
                    ? arr[0].nickname.trim()
                    : '';

                  if (
                    arrNickname != arrNickname1 ||
                    arr[0].active != params.data.active ||
                    arr[0].department != params.data.department
                  ) {
                    var regExp = /^[a-zA-Z\s']+$/;
                    if (
                      (regExp.test(nickName) || nickName == '') &&
                      nickName.length <= 80
                    ) {
                      startEdit.updateTechnicianStatus(
                        selectedId,
                        nickName,
                        techStatus,
                        selectedDepartment
                      );
                      startEdit.setState({
                        oldDataArr: []
                      });
                    } else {
                      let rowData = startEdit.state.oldDataArr;
                      var rowNode = params.api.getDisplayedRowAtIndex(index);
                      // rowNode.setDataValue('nickname', rowData[0].nickname);
                      rowNode.setDataValue('active', rowData[0].active);
                      rowNode.setDataValue(
                        'teamAssignedTo',
                        rowData[0].teamAssignedTo
                      );
                      rowNode.setDataValue('isTeam', rowData[0].isTeam);
                      rowNode.setDataValue('department', rowData[0].department);
                    }
                  }
                }

                let chkOldTeamAssigned = '';
                if (
                  arr[0].teamAssignedTo === null ||
                  arr[0].teamAssignedTo == 'None'
                ) {
                  chkOldTeamAssigned = 'None';
                }

                if (
                  arr[0].isTeam != params.data.isTeam ||
                  chkOldTeamAssigned != params.data.teamAssignedTo
                ) {
                  if (
                    arr[0].isTeam != params.data.isTeam ||
                    // arr[0].active != params.data.active ||
                    // arr[0].nickname != params.data.nickname ||
                    arr[0].teamAssignedTo != params.data.teamAssignedTo
                  ) {
                    startEdit.updateTechTeamSettings(
                      params.data.teamAssignedTo != 'None'
                        ? '1'
                        : params.data.teamAssignedTo == 'None' &&
                          params.data.isTeam == 1
                        ? '1'
                        : '0',
                      params.data.isTeam && params.data.isTeam.toString(),
                      params.data.teamAssignedTo != 'None'
                        ? params.data.teamAssignedTo
                        : arr[0].teamAssignedTo,
                      params.data.name,
                      params.data.lbrtechno,
                      arr.length > 0 && arr[0].teamAssignedTo
                    );
                  }
                }
                startEdit.gridApi.refreshCells({ force: true });
                startEdit.gridApi.redrawRows();
                // }
                // else if ((techStatus != startEdit.state.technicianStatus) && startEdit.state.technicianStatus!='') {
                //   startEdit.updateTechnicianStatus(
                //     selectedId,
                //     nickName,
                //     techStatus
                //   );
                // }else if((nickNameOld!=''||nickNameOld!='undefined') && (nickName!=undefined)&&nickNameOld != nickName){
                //     startEdit.updateTechnicianStatus(
                //       selectedId,
                //       nickName,
                //       techStatus
                //     );
                // }
                //  else{
                //     startEdit.getAgGridData();
                // }
                // if((startEdit.state.technicianStatus != techStatus)&&(startEdit.state.technicianStatus!='')){
                //   startEdit.updateTechnicianStatus(
                //     selectedId,
                //     nickName,
                //     techStatus
                //   );
                // }else if((nickNameOld!=''||nickNameOld!='undefined') && (nickName!=undefined)&&nickNameOld != nickName){
                //   startEdit.updateTechnicianStatus(
                //     selectedId,
                //     nickName,
                //     techStatus
                //   );
                // }else{
                //   startEdit.getAgGridData();
                // }
                // if (((selectedId != '') && (nickNameOld!=nickName) && (nickNameOld != 'undefined')) || (startEdit.state.technicianStatus != techStatus )) {
                //   startEdit.updateTechnicianStatus(
                //     selectedId,
                //     nickName,
                //     techStatus
                //   );
                // }
                $('#btnedittechnician' + index).show();
                $('#btncanceltechnician' + index).hide();
                $('#btnupdatetechnician' + index).hide();
                $('#btndelete' + index).hide();
                $('#techStatus' + index).prop('disabled', 'disabled');
              });

              cButton.addEventListener('click', function() {
                startEdit.setState({ cancel: true });
                startEdit.onBtStopEditing(index);
                let status = $('#techStatus' + index).prop('checked');
                let data = $('#techStatus' + index).val() == 1 ? true : false;
                let techStatus = status == data ? status : !status;
                startEdit.setState({
                  editedRowId: null
                });
                startEdit.setState({
                  teamTech: startEdit.state.allTeamTech
                });
                let rowData = startEdit.state.oldDataArr;
                var rowNode = params.api.getDisplayedRowAtIndex(index);
                rowNode.setDataValue('nickname', rowData[0].nickname);
                rowNode.setDataValue('active', rowData[0].active);
                rowNode.setDataValue('department', rowData[0].department);
                rowNode.setDataValue(
                  'teamAssignedTo',
                  rowData[0].teamAssignedTo
                );
                rowNode.setDataValue('isTeam', rowData[0].isTeam);
                $('#btnedittechnician' + index).show();
                $('#btncanceltechnician' + index).hide();
                $('#btnupdatetechnician' + index).hide();
                $('#btndelete' + index).hide();
                $('#techStatus' + index).prop(
                  'checked',
                  rowData.active == 1 ? true : false
                );
                $('#techStatus' + index).prop('disabled', 'disabled');
                // rowData.map((object, i) => {
                //   let rowNode = params.api.getDisplayedRowAtIndex(object.index);
                //   console.log('rowData=', index, object.index, rowNode);
                //   rowNode.setDataValue('nickname', rowData[i].nickname);
                //   rowNode.setDataValue('active', rowData[i].active);
                //   $('#btnedittechnician' + object.index).show();
                //   $('#btncanceltechnician' + object.index).hide();
                //   $('#btnupdatetechnician' + object.index).hide();
                //   $('#btndelete' + object.index).hide();
                //   $('#techStatus' + object.index).prop(
                //     'checked',
                //     rowData.active == 1 ? true : false
                //   );
                //   $('#techStatus' + object.index).prop('disabled', 'disabled');
                // });

                // var rowNode = params.api.getDisplayedRowAtIndex(index);
                // rowNode.setDataValue('nickname', rowData[0].nickname);
                // rowNode.setDataValue('active', rowData[0].active);
                // $('#btnedittechnician' + index).show();
                // $('#btncanceltechnician' + index).hide();
                // $('#btnupdatetechnician' + index).hide();
                // $('#btndelete' + index).hide();
                // $('#techStatus' + index).prop('checked',rowData.active == 1 ? true : false);
                // $('#techStatus' + index).prop('disabled', 'disabled');
              });

              /* dButton.addEventListener('click', function () {
                var selectedId = params.data.id;
                if (selectedId != '') {
                  startEdit.deleteServiceAdvisor(selectedId);
                }
                $('#btnedittechnician' + index).show();
                $('#btncanceltechnician' + index).hide();
                $('#btnupdatetechnician' + index).hide();
                $('#btndelete' + index).hide();
              });*/
            }
            return eDiv;
          }
        }
      ],

      rowData: [],
      editType: 'fullRow',
      chartName: null,
      context: { componentParent: this },
      frameworkComponents: {
        activeStatusRenderer: ActiveStatusRenderer,
        NickNameCheckboxRenderer: NickNameCheckboxRenderer,
        dropDownRenderer: dropDownRenderer
      },
      // components: { singleClickEditRenderer: getRenderer() },
      defaultColDef: {
        enableValue: true,
        suppressKeyboardEvent: params => params.event.keyCode === 13,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: true,
        editable: true,
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        suppressMovable: false
      }
    };
  }

  cellStyles = () => {
    return {
      textAlign: 'center',
      border: ' 0px white'
    };
  };
  getTeamTech = params => {
    var tech = ['None'];
    let uniqueChars = [];

    getAllTechnicians(result => {
      if (result.data.statelessCcPhysicalRwGetTechnicians.nodes) {
        var roData = lodash.orderBy(
          result.data.statelessCcPhysicalRwGetTechnicians.nodes,
          ['active', 'categorized'],
          ['desc', 'asc']
        );

        var isteam = roData.filter(e => e.isTeam == 1);
        isteam.map(item => {
          tech.push(item.lbrtechno);
        });

        tech.forEach(c => {
          if (!uniqueChars.includes(c)) {
            uniqueChars.push(c);
          }
        });
        console.log('uniqueChars===', uniqueChars);
        this.setState({ allTeamTech: uniqueChars });
        this.setState({ teamTech: uniqueChars });
      }
    });

    return uniqueChars;
  };
  onCellValueChanged = params => {
    if (params.newValue != params.oldValue) {
      this.setState({ isEdited: true });
    }
  };
  onCellValueChanged = params => {
    console.log('params=s=', params, this.state.cancel);
    if (this.state.cancel == false) {
      this.setState({ isEdited: true });
      var selectedId = params.data.lbrtechno;
      var nickName = params.data.nickname;
      nickName = nickName ? nickName.trim() : '';
      var techStatus = params.data.active;
      var selectedDepartment = params.data.department;
      var nickNameOld = localStorage.getItem('nickname').trim();
      if (selectedId != '') {
        this.updateTechnicianStatus(
          selectedId,
          nickName,
          techStatus,
          selectedDepartment
        );
      }
    }
  };
  setTechStatus = data => {
    data
      ? this.setState({
          technicianStatus: data.event.target.checked == true ? '1' : '0'
        })
      : this.setState({ technicianStatus: '' });
  };
  onBtStopEditing = () => {
    this.gridApi.stopEditing();
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = false;
    groupColumn[4]['editable'] = false;
    this.state.rawGridApi.setColumnDefs(groupColumn);
  };
  setEditStatus = () => {
    this.setState({ editClick: true });
  };
  objCategoryMappings = [
    {
      1: 'Active',
      0: 'Inactive'
    }
  ];
  extractValues(mappings) {
    return Object.keys(mappings[0]);
  }
  setTechStatus = data => {
    data
      ? this.setState({
          advisorNewStatus: data.event.target.checked == true ? '1' : '0'
        })
      : this.setState({ advisorNewStatus: '' });
  };
  updateTechnicianStatus = (techno, nickName, techStatus, department) => {
    let statusval = this.state.technicianStatus;
    console.log(
      'status',
      statusval != '' ? statusval : techStatus == null ? 0 : techStatus
    );
    const client = makeApolloClient;
    client
      .mutate({
        mutation: UPDATE_TECHNICIAN_STATUS,
        variables: {
          techno: techno,
          pCategorized: techStatus == null ? 0 : 1,
          user_id: localStorage.getItem('userID'),
          statusval:
            statusval != '' ? statusval : techStatus == null ? 0 : techStatus,
          nickName: nickName ? nickName.trim() : null,
          department: department,
          user_id: localStorage.getItem('userID')
          // store_id: this.state.storeId
        }
      })
      .then(result => {
        var arrSelecte = [];
        this.state.rawGridApi.forEachNode(node => {
          arrSelecte.push(node.data);
        });
        this.props.setErrorCount(true);
        this.state.rowData.push(arrSelecte[0]); // Add new Item
        this.getAgGridData();
        this.state.rawGridApi.setRowData(this.state.rowData); // Refresh grid

        this.setState({ technicianStatus: '' });
        //alert('Technician Details Updated');
        this.setState({ successmsg: 'Technician Details Updated!' });
        this.setState({ openSnackbar: true });
        // window.location.reload(false);
        //

        var techStatus =
          statusval == '' ? (techStatus == null ? 0 : techStatus) : statusval;

        var activeTechs = this.props.session.activeTechs;
        // this.props.dispatch(setAdvisorStatus(statusval));
        if (techStatus == 1) {
          activeTechs.push(techno);
        } else {
          activeTechs.unshift(techno);
        }

        this.props.dispatch(setTechnicianStatus(activeTechs));
      });
  };

  updateTechTeamSettings = (
    status,
    teamActive,
    teamAssigned,
    techName,
    techno,
    oldTeamAssigned
  ) => {
    const client = makeApolloClient;

    client
      .mutate({
        mutation: UPDATE_TECH_TEAM_SETTINGS,
        variables: {
          oldTeamAssigned: oldTeamAssigned,
          status: status,
          storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
          teamActive: teamActive == null ? '0' : teamActive.toString(),
          teamAssigned: teamActive == 1 ? 'None' : teamAssigned,
          techName: techName,
          userId: localStorage.getItem('userID'),
          techno: techno
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        this.setState({ successmsg: 'Team tech details updated!' });
        this.setState({ openSnackbar: true });
        var teamArr = [];
        const newColumns = this.state.columnDefs;
        newColumns.forEach(function(colDef, index) {
          if (colDef.field == 'teamAssignedTo') {
            teamArr = colDef.cellRendererParams.dropDown;
            if (teamActive == '1') {
              if (!teamArr.includes(techno)) {
                //checking weather array contain the id
                teamArr.push(techno); //adding to array because value doesnt exists
              }
            } else {
              if (teamArr.includes(techno)) {
                teamArr.splice(teamArr.indexOf(techno), 1); //deleting
              }
            }
            colDef.cellRendererParams.dropDown = teamArr;
          }
        });
        this.setState({ teamtech: teamArr });
        console.log('cccc==aaaddd', newColumns);
        this.gridApi.setColumnDefs(newColumns);

        this.getAgGridData();

        //window.location.reload();
      });
  };
  onRowEditingStarted(params) {
    params.api.refreshCells({
      columns: ['nickname', 'active', 'isTeam', 'teamAssignedTo'],
      rowNodes: [params.node],
      force: true
    });
  }
  onRowEditingStopped(params) {
    params.api.refreshCells({
      columns: ['nickname', 'active', 'isTeam', , 'teamAssignedTo'],
      rowNodes: [params.node],
      force: true
    });
  }
  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    this.gridApi.redrawRows();
  };
  onSortChanged = e => {
    this.gridApi.redrawRows();
  };
  insertServiceAdvisor = (Id, Name, ServiceAdvisor) => {
    const client = makeApolloClient;
    client
      .mutate({
        mutation: INSERT_SERVICE_ADVISOR,
        variables: {
          name: Name,
          serviceadvisor: ServiceAdvisor
        }
      })
      .then(result => {
        var arrSelecte = [];
        this.state.rawGridApi.forEachNode(node => {
          arrSelecte.push(node.data);
        });
        this.state.rowData.push(arrSelecte[0]); // Add new Item
        this.getAgGridData();

        this.state.rawGridApi.setRowData(this.state.rowData); // Refresh grid

        alert('Row Added');
      });
  };
  deleteServiceAdvisor = Id => {
    const client = makeApolloClient;
    client
      .mutate({
        mutation: DELETE_SERVICE_ADVISOR,
        variables: {
          id: Id
        }
      })
      .then(result => {
        var selected = this.state.rawGridApi.getFocusedCell();
        this.state.rowData.splice(selected.rowIndex, 1);
        this.state.rawGridApi.setRowData(this.state.rowData);
        if (window.confirm('Are you sure you want to delete?')) {
          alert('Row deleted');
        }
      });
  };
  onBtStartEditing = (index, key, char, pinned) => {
    // columnController
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = true;
    groupColumn[4]['editable'] = true;
    groupColumn[5]['editable'] = true;
    this.state.rawGridApi.setColumnDefs(groupColumn);
    this.state.rawGridApi.columnController.columnDefs[1].editable = true;
    this.state.rawGridApi.setFocusedCell(index, 'nickname', 'active', pinned);
    this.state.rawGridApi.startEditingCell({
      rowIndex: index,
      colKey: 'nickname',
      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
  };
  // onBtStartEditing = (index, key, char, pinned) => {
  //   // columnController
  //   this.state.rawGridApi.columnController.columnDefs[1].editable = true;

  //   this.state.rawGridApi.setFocusedCell(index, 'active', pinned);
  //   // this.state.rawGridApi.startEditingCell({
  //   //   rowIndex: index,
  //   //   colKey: 'active',
  //   //   // colKey: 'serviceadvisor',
  //   //   rowPinned: pinned,
  //   //   keyPress: key,
  //   //   charPress: char
  //   // });
  // };
  onGridReady = params => {
    localStorage.removeItem('oldIndex');
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    if (
      this.props.keycloak &&
      this.props.keycloak.authenticated &&
      this.props.keycloak.token
    ) {
      this.getAgGridData();
    }
    this.gridApi.addEventListener('cellKeyDown', function(event) {
      if (event.event.keyCode === 13) {
        event.event.preventDefault();
        console.log('Enter key press deactivated.');
      }
    });
  };
  onCellClickedDD = cellValue => {
    // console.log('Cell Value:', cellValue);
    this.setState({ dropdownSelect: cellValue });
  };
  getRowStyle = params => {
    if (
      params.data.categorized == 0 ||
      (params.data.categorized == 1 && params.data.active == 0)
    ) {
      return { background: 'rgb(221, 234, 244)' };
    } else {
      return { background: 'rgb(255, 255, 255)' };
    }
  };
  getAgGridData() {
    // const gridApi = gridRef.current.api;
    const filterModel = this.gridApi.getFilterModel(); // Store the filter state

    this.setState({ isLoading: true });
    var tech = ['None'];
    let uniqueChars = [];
    getAllTechnicians(result => {
      this.setState({
        rowData: []
      });
      if (result.data.statelessCcPhysicalRwGetTechnicians.nodes) {
        var roData = lodash.orderBy(
          result.data.statelessCcPhysicalRwGetTechnicians.nodes,
          ['active', 'categorized'],
          ['desc', 'asc']
        );
        roData.forEach(roData => {
          roData.name =
            roData && roData.name && roData.name.replace(/^\s+/g, '');
        });

        this.setState({
          rowData: roData
        });
        this.gridApi.setFilterModel(filterModel);
        this.gridApi.onFilterChanged();
        var isteam = roData.filter(e => e.isTeam == 1);
        isteam.map(item => {
          tech.push(item.lbrtechno);
        });

        this.setState({ teamtech: tech });

        tech.forEach(c => {
          if (!uniqueChars.includes(c)) {
            uniqueChars.push(c);
          }
        });

        this.setState({ isLoading: false });
        // this.setState({ teamtech: tech });
      }
    });

    this.state.rawGridApi.refreshCells();
    // this.state.rawGridApi.refreshCells({
    //   columns: ['nickname', 'active', 'isTeam', , 'teamAssignedTo'],
    //   rowNodes: [this.props.node],
    //   force: true
    // });
  }
  addNewRow() {
    this.state.rawGridApi.updateRowData({
      addIndex: 0,
      add: [{ id: '', name: '', serviceadvisor: '' }]
    });

    this.state.rawGridApi.setFocusedCell(0, 'name', '');
  }
  handleOk = () => {
    this.setState({ success: false });
  };
  setTeamTech = (techArr, node) => {
    console.log('techArr==', techArr);
    this.setState({ teamTech: techArr });
    this.gridApi.refreshCells({
      columns: ['teamAssignedTo'],
      rowNodes: [node],
      force: true
    });
  };
  resetReportGrid = () => {
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.gridApi.redrawRows();

    this.gridApi.setColumnDefs([]);
    this.gridApi.setColumnDefs(this.state.columnDefs);
    this.setState({
      editedRowId: null
    });
    this.gridApi.setRowData(this.state.rowData);
    this.getAgGridData();
  };
  onCellClicked = params => {
    let index = localStorage.getItem('index');
    const id = localStorage.getItem('techId');
    let rowId = this.state.editedRowId;
    if (params.data.id != id) {
      $(`.edit-button-technician`).show();
      $(`.update-button-technician`).hide();
      $(`.cancel-button-technician`).hide();
      $(`#btncanceltechnician` + rowId).click();
      this.setState({ cancel: true });
      this.onBtStopEditing(index);

      this.setState({ editedRowId: null });
      let data = $('#techStatus' + index).val() == 1 ? true : false;

      this.gridApi.redrawRows();
      // var rowNode = params.api.getDisplayedRowAtIndex(index);
      // rowNode.setDataValue('nickname', rowData[0].nickname);
      // rowNode.setDataValue('active', rowData[0].active);
      // $('#techStatus' + index).prop('checked',rowData.active == 1 ? true : false);
      // $('#techStatus' + index).prop('disabled', 'disabled');
    }
  };
  hidesnackbar = () => {
    this.setState({ openSnackbar: false });
    this.setState({ nickError: false });
  };
  handleclick = () => {
    this.props.history.goBack();
  };
  render() {
    const { classes } = this.props;
    return (
      <>
        <div>
          {/* <Paper square style={{ margin: 8 }}> */}
          <Paper
            square
            style={{
              margin: 8,

              backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
              border:
                Dealer === 'Armatus'
                  ? '1px solid #003d6b'
                  : '1px solid #C2185B',
              color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b',
              cursor: 'default'
            }}
          >
            <Tabs
              value={this.state.tabSelection}
              onChange={this.handleTabChange}
              variant="fullWidth"
              indicatorColor="secondary"
              textColor="secondary"
              aria-label="icon label tabs example"
            >
              {this.props.history &&
                this.props.history.location &&
                this.props.history.location.state &&
                this.props.history.location.state.isFrom && (
                  <Button
                    variant="contained"
                    className={'bck-btn'}
                    onClick={this.handleclick}
                    style={{ width: 'auto', top: '9px' }}
                    fullWidth={false}
                  >
                    <Typography variant="body1" align="left">
                      Back
                    </Typography>
                  </Button>
                )}
              <Tab
                // style={{
                //   pointerEvents: 'none',
                //   textTransform: 'none',
                //   backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                //   border:
                //     Dealer === 'Armatus'
                //       ? '1px solid #003d6b'
                //       : '1px solid #C2185B',
                //   color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
                // }}
                style={{
                  textTransform: 'none',
                  pointerEvents: 'none',
                  borderColor: '#e7eef3',
                  color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
                }}
                label={<div>Technician Details</div>}
                value="one"
              />
            </Tabs>
          </Paper>
          <Paper>
            {' '}
            <Button
              variant="contained"
              id="reset-layout"
              className={clsx(classes.back, 'reset-btn')}
              onClick={this.resetReportGrid}
            >
              <RestoreIcon />
              <Typography variant="body1" align="left">
                Reset Layout
              </Typography>
            </Button>
          </Paper>
          {/* <Paper square style={{ margin: 8, padding: 8 }}>
          <Button
            variant="outlined"
            style={{ height: 30 }}
            onClick={this.addNewRow}
          >
            Add Technician
          </Button>
        </Paper> */}
          {this.state.isLoading == true ? (
            <div>
              <Box style={{ padding: 25 }}>
                <LinearProgress color="secondary" />
                <Typography
                  variant="h6"
                  align="center"
                  style={{ padding: 25 }}
                  color="primary"
                >
                  Processing...
                </Typography>
              </Box>
            </div>
          ) : null}
          {/* <Dialog
          aria-labelledby="confirmation-dialog-title"
          open={this.state.success}
        >
          <DialogContent dividers>
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              Technician details updated!
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={this.handleOk} color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog> */}
          <div
            id="data-tab-tech"
            className="ag-theme-balham"
            style={{
              height: window.innerHeight - 170 + 'px',
              // width: '98.8%',
              width: '1190px',
              alignContent: 'center',
              marginLeft: '8px',
              //margin: 8,
              display: this.state.isLoading == true ? 'none' : 'block'
            }}
          >
            <AgGridReact
              className="ag-theme-balham"
              style={{
                height: '500px',
                width: '100%'
              }}
              frameworkComponents={this.state.frameworkComponents}
              enableRangeSelection={true}
              autoGroupColumnDef={this.state.autoGroupColumnDef}
              getChartToolbarItems={this.getChartToolbarItems}
              animateRows={true}
              enableCharts={true}
              modules={AllModules}
              getRowStyle={this.getRowStyle}
              columnDefs={this.state.columnDefs}
              defaultColDef={this.state.defaultColDef}
              onGridReady={this.onGridReady}
              suppressAggFuncInHeader={true}
              // onCellValueChanged={this.onCellValueChanged.bind(this)}
              onRowEditingStarted={this.onRowEditingStarted}
              onRowEditingStopped={this.onRowEditingStopped}
              onFilterChanged={this.onFilterChanged}
              onSortChanged={this.onSortChanged}
              rowData={this.state.rowData}
              suppressClickEdit={true}
              context={this.state.context}
              components={this.state.components}
              onCellClicked={this.onCellClicked}
              floatingFilter={true}
              suppressRowClickSelection={true}
              suppressHorizontalScroll={true}
              editType={this.state.editType}
              suppressDragLeaveHidesColumns={true}
              suppressContextMenu={true}
            />
          </div>
        </div>

        <Snackbar
          open={this.state.openSnackbar}
          autoHideDuration={6000}
          onClose={this.hidesnackbar}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbar} severity="success">
            {this.state.successmsg}
          </Alert>
        </Snackbar>
        <Snackbar
          open={this.state.nickError}
          autoHideDuration={6000}
          onClose={this.hidesnackbar}
          style={{ marginBottom: 750 }}
        >
          <Alert onClose={this.hidesnackbar} severity="warning">
            Please enter valid Nick Name
          </Alert>
        </Snackbar>
      </>
    );
  }
}
const departmentMappings = {
  'Body Shop': 'Body Shop',
  Service: 'Service'
};
function extractValues(mappings) {
  return Object.keys(mappings);
}
const depart = extractValues(departmentMappings);
function lookupValue(mappings, key) {
  return mappings[key];
}
const styles = theme => ({
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  back: {
    marginRight: 13,
    float: 'right',
    marginTop: -44
  }
});
function getRenderer(params) {
  function CellRenderer() {}
  CellRenderer.prototype.createGui = function() {
    const template =
      '<span><input type="checkbox" class="editNickName" id="changeNickName" style="cursor: pointer" title="Click to Edit" /><span id="changedNickNameValue" style="padding-left: 4px;"></span></span>';
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = template;
    this.eGui = tempDiv.firstElementChild;
  };
  CellRenderer.prototype.init = function(params) {
    this.createGui();
    this.params = params;
    const eValue = this.eGui.querySelector('#changedNickNameValue');
    eValue.innerHTML = typeof params.value != 'undefined' ? params.value : '';
    this.eButton = this.eGui.querySelector('#changeNickName');
    this.buttonClickListener = this.onButtonClicked.bind(this);
    this.eButton.addEventListener('click', this.buttonClickListener);
  };
  CellRenderer.prototype.onButtonClicked = function() {
    let index = this.params.rowIndex;
    $('#btnedittechnician' + index).hide();
    $('#btncanceltechnician' + index).show();
    $('#btnupdatetechnician' + index).show();
    const startEditingParams = {
      rowIndex: this.params.rowIndex,
      colKey: this.params.column.getId()
    };
    this.params.api.startEditingCell(startEditingParams);
  };
  CellRenderer.prototype.getGui = function() {
    return this.eGui;
  };
  CellRenderer.prototype.destroy = function() {
    this.eButton.removeEventListener('click', this.buttonClickListener);
  };
  return CellRenderer;
}
// render(<Technicians></Technicians>, document.querySelector('#root'));
export default withStyles(styles)(withKeycloak(Technicians));
