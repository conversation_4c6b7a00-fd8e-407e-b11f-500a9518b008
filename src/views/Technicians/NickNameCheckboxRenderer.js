import React, { Component } from 'react';
import $ from 'jquery';
export default class extends Component {
  constructor(props) {
    super(props);
    this.checkedHandler = this.checkedHandler.bind(this);
    this.state = {
      nicknameChecked: false,
      setMessage: ''
    };
  }
  checkedHandler(event) {
    let checked = event.target.checked;
    let colId = this.props.column.colId;
    // this.setState({
    //   nicknameChecked: true
    // });d
    this.props.node.setDataValue(colId, checked == true ? 1 : 0);

    var teamtech = this.props.context.componentParent.state.teamtech;
    if (checked == false) {
      teamtech = teamtech.filter(val => val !== this.props.data.lbrtechno);
      this.props.context.componentParent.setTeamTech(teamtech, this.props.node);
    } else {
      teamtech.push(this.props.data.lbrtechno);
      this.props.context.componentParent.setTeamTech(teamtech, this.props.node);
    }
    console.log('event', this.props.node);
    if (checked == true) {
      this.props.node.setDataValue('teamAssignedTo', 'None');
    }
    this.props.api.refreshCells({
      columns: ['teamAssignedTo'],
      rowNodes: [this.props.node],
      force: true
    });
  }
  render() {
    return (
      <>
        <input
          type="checkbox"
          className="teamTechCheck"
          disabled={
            this.props.context.componentParent.state.editedRowId == null
              ? true
              : this.props.context.componentParent.state.editedRowId !=
                this.props.rowIndex
              ? true
              : false
          }
          onClick={this.checkedHandler}
          // checked={this.props.value}
          checked={this.props.value == 1 ? true : false}
        />
      </>
    );
  }
}
