import MomentUtils from '@date-io/moment';
import { MuiPickersUtilsProvider } from '@material-ui/pickers';
import { ThemeProvider } from '@material-ui/styles';
import { KeycloakProvider } from '@react-keycloak/web';
import { createBrowserHistory } from 'history';
import Keycloak from 'keycloak-js';
import { ErrorBoundary } from 'react-error-boundary';
import React, { useState, useEffect } from 'react';
import ReactGA from 'react-ga';
import { useLocation } from 'react-router-dom';
import 'react-perfect-scrollbar/dist/css/styles.css';
import {
  Provider as StoreProvider,
  useDispatch,
  useSelector
} from 'react-redux';
import { CookiesProvider, useCookies } from 'react-cookie';
import { renderRoutes } from 'react-router-config';
import { Router } from 'react-router-dom';
import './assets/scss/main.scss';
import GoogleAnalytics from './components/GoogleAnalytics';
import ScrollReset from './components/ScrollReset';
import StylesProvider from './components/StylesProvider';
import './mixins/chartjs';
import './mixins/moment';
import './mixins/prismjs';
import './mixins/validate';
import './mock';
import routes from './routes';
import { configureStore } from './store';
import { theme, themeWithRtl } from './theme';
import { setToken } from './actions';
import TagManager from 'react-gtm-module';
import { cancelAllRequests } from 'src/utils/abortableHttpLink';

require('dotenv').config();

const history = createBrowserHistory();
export const store = configureStore();

if (process.env.REACT_APP_GTM_ID && process.env.REACT_APP_GTM_ID != '') {
  const tagManagerArgs = {
    gtmId: process.env.REACT_APP_GTM_ID
  };
  TagManager.initialize(tagManagerArgs);
}
// else{
//   // Initialize google analytics page view tracking
//   const trackingId = process.env.REACT_APP_GA_MEASUREMENT_ID;
//   ReactGA.initialize(trackingId, {
//     cookieDomain: 'auto',
//     debug: true
//   });
// }

// history.listen(location => {
//   ReactGA.set({ page: location.pathname }); // Update the user's current page
//   ReactGA.pageview(location.pathname); // Record a pageview for the given page
// });
// function RouteWatcher() {
//   const location = useLocation();

//   useEffect(() => {
//     // Cancel any ongoing requests when the route changes
//     cancelAllRequests();
//   }, [location.pathname]);

//   return null;
// }
function Application() {
  const dispatch = useDispatch();
  const [direction] = useState('ltr');
  let url = window.location.href;
  var arr = url.split('/');
  const settings = { ...process.env };
  if (process.env.REACT_APP_PRODUCTION == 'false') {
    var env = 'REACT_APP_KEYCLOAK_REALM_' + arr[2].split('-')[0].toUpperCase();
    var keyUrl = 'REACT_APP_KEYCLOAK_URL_' + arr[2].split('-')[0].toUpperCase();
  } else {
    var env = 'REACT_APP_KEYCLOAK_REALM_' + arr[2].split('.')[0].toUpperCase();
    var keyUrl = 'REACT_APP_KEYCLOAK_URL_' + arr[2].split('.')[0].toUpperCase();
  }
  console.log(
    'settings===',
    settings[env],
    keyUrl,
    settings[keyUrl],
    process.env.REACT_APP_KEYCLOAK_URL
  );

  const keycloak = new Keycloak({
    realm: settings[env],
    // url: settings[keyUrl],
    url: process.env.REACT_APP_KEYCLOAK_URL,
    clientId: process.env.REACT_APP_KEYCLOAK_CLIENT_ID
  });
  const ErrorFallback = ({ error, resetErrorBoundary }) => {
    return null;
  };
  const onKeycloakEvent = (event, error) => {};

  const onKeycloakTokens = tokens => {
    dispatch(setToken('Bearer ' + tokens.token));
    localStorage.setItem('keycloakToken', tokens.token);
    localStorage.setItem('userID', keycloak.tokenParsed.preferred_username);
    localStorage.setItem('realms', keycloak.realm);
  };

  return (
    <KeycloakProvider
      keycloak={keycloak}
      onEvent={onKeycloakEvent}
      onTokens={onKeycloakTokens}
      initConfig={{
        checkLoginIframe: false
      }}
    >
      <CookiesProvider>
        <ThemeProvider theme={direction === 'rtl' ? themeWithRtl : theme}>
          <StylesProvider direction={direction}>
            <MuiPickersUtilsProvider utils={MomentUtils}>
              <ErrorBoundary
                FallbackComponent={ErrorFallback}
                onError={(error, info) => {
                  console.log('UI Error', error);
                }}
              >
                <Router history={history}>
                  {/* <RouteWatcher /> */}
                  <ScrollReset />
                  <GoogleAnalytics />
                  {renderRoutes(routes)}
                </Router>
              </ErrorBoundary>
            </MuiPickersUtilsProvider>
          </StylesProvider>
        </ThemeProvider>
      </CookiesProvider>
    </KeycloakProvider>
  );
}

function App() {
  return (
    <StoreProvider store={store}>
      <Application></Application>
    </StoreProvider>
  );
}

export default App;
