// import { Apollo<PERSON>lient } from 'apollo-boost';
import ApolloClient from 'apollo-client';
import { createHttpLink } from 'apollo-link-http';
import { InMemoryCache } from 'apollo-cache-inmemory';
import { HttpLink, from } from '@apollo/client';
import { setContext } from 'apollo-link-context';
import { onError } from 'apollo-link-error';
require('dotenv').config();
const jwt = require('jsonwebtoken');

const httpLink = createHttpLink({
  uri: process.env.REACT_APP_POSTGRES_ENDPOINT_WRITE
});
const link = onError(({ graphQLErrors, networkError }) => {
  console.log(`[GraphQL error]: Message: ${graphQLErrors}`);
  if (graphQLErrors)
    graphQLErrors.map(({ message, locations, path }) =>
      console.log(
        `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
      )
    );

  if (networkError) console.log(`[Network error]: ${networkError}`);
});
let url = window.location.href;
var arr = url.split('/');
const settings = { ...process.env };

if (process.env.REACT_APP_PRODUCTION == 'false') {
  var env = 'REACT_APP_KEYCLOAK_REALM_' + arr[2].split('-')[0].toUpperCase();
} else {
  var env = 'REACT_APP_KEYCLOAK_REALM_' + arr[2].split('.')[0].toUpperCase();
}
const authLink = setContext((_, { headers }) => {
  const token = localStorage.getItem('keycloakToken');

  if (jwt.decode(token).exp < Date.now() / 1000) {
    console.log('Graphql -> Token expired');
    setTimeout(() => {
      const newtoken = localStorage.getItem('keycloakToken');
      if (newtoken && jwt.decode(newtoken).exp < Date.now() / 1000) {
        localStorage.setItem('tokenexpired', true);
        window.location.href = '/auth/login';
        // window.location.href = '/login';
      } else {
        return {
          headers: {
            authorization: newtoken ? `Bearer ${newtoken}` : '',
            storeid:
              localStorage.getItem('storeIdSelected') != null
                ? localStorage.getItem('storeIdSelected')
                : localStorage.getItem('selectedStoreId')
                ? JSON.parse(localStorage.getItem('selectedStoreId'))[0] || null
                : '',
            //realm: 'lupient'
            realm: settings[env]
          }
        };
      }
    }, 20000);
  } else {
    return {
      headers: {
        authorization: token ? `Bearer ${token}` : '',
        storeid:
          localStorage.getItem('storeIdSelected') != null
            ? localStorage.getItem('storeIdSelected')
            : localStorage.getItem('selectedStoreId')
            ? JSON.parse(localStorage.getItem('selectedStoreId'))[0] || null
            : '',
        realm: settings[env]
        //realm: 'lupient'
      }
    };
  }
});
const makeApolloClientPostgres = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache()
});

export default makeApolloClientPostgres;
