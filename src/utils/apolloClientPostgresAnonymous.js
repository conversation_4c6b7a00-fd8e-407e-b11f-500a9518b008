// import { ApolloClient } from 'apollo-boost';
import Apollo<PERSON><PERSON> from 'apollo-client';
import { createHttpLink } from 'apollo-link-http';
import { InMemoryCache } from 'apollo-cache-inmemory';
import { HttpLink, from } from '@apollo/client';
import { setContext } from 'apollo-link-context';
import { onError } from 'apollo-link-error';
require('dotenv').config();
const jwt = require('jsonwebtoken');

const httpLink = createHttpLink({
  uri: process.env.REACT_APP_POSTGRES_ENDPOINT_ANONYMOUS
});
const link = onError(({ graphQLErrors, networkError }) => {
  console.log(`[GraphQL error]: Message: ${graphQLErrors}`);
  if (graphQLErrors)
    graphQLErrors.map(({ message, locations, path }) =>
      console.log(
        `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
      )
    );

  if (networkError) console.log(`[Network error]: ${networkError}`);
});
let url = window.location.href;
var arr = url.split('/');
const settings = { ...process.env };

if (process.env.REACT_APP_PRODUCTION == 'false') {
  var env = 'REACT_APP_KEYCLOAK_REALM_' + arr[2].split('-')[0].toUpperCase();
} else {
  var env = 'REACT_APP_KEYCLOAK_REALM_' + arr[2].split('.')[0].toUpperCase();
}
const authLink = setContext((_, { headers }) => {
  return {
    headers: {
      storeid:
        localStorage.getItem('storeIdSelected') != null
          ? localStorage.getItem('storeIdSelected')
          : localStorage.getItem('selectedStoreId')
          ? JSON.parse(localStorage.getItem('selectedStoreId'))[0] || null
          : '',
      realm: settings[env]
      //realm: 'lupient'
    }
  };
});
const makeApolloClientPostgresAnonymous = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache()
});

export default makeApolloClientPostgresAnonymous;
