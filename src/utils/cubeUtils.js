//import cubejs from '@cubejs-client/core';
import cubejs, { HttpTransport } from '@cubejs-client/core';
require('dotenv').config();
const jwt = require('jsonwebtoken');

const cubejsApi = () => {
  const CUBE_API_SECRET =
    '752e5d8af86f787ecde5cdf9e254b985479bc0db6f1529abd980a8749b9c8e3207f82748d2246e1d9166d2265d03b04fadf196cfff29d1cc90fcf6c74179f708';
  let cubejsToken = '';
  const token = localStorage.getItem('keycloakToken');
  if (token && jwt.decode(token).exp < Date.now() / 1000) {
    console.log('Cube -> Token expired');
    setTimeout(() => {
      const newtoken = localStorage.getItem('keycloakToken');
      if (newtoken && jwt.decode(newtoken).exp < Date.now() / 1000) {
        localStorage.setItem('tokenexpired', true);
        window.location.href = '/auth/login';
        // window.location.href = '/login';
      } else {
        const API_URL = process.env.REACT_APP_CUBE_SERVER_URL; // change to your actual endpoint
        return cubejs(
          // cubejsToken,
          newtoken ? `Bearer ${newtoken}` : '',
          {
            apiUrl: API_URL + '/cubejs-api/v1',
            headers: {
              realm: localStorage.getItem('realms'),
              storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
            }
          }
        );
      }
    }, 2000);
  } else {
    const API_URL = process.env.REACT_APP_CUBE_SERVER_URL; // change to your actual endpoint
    return cubejs(
      // cubejsToken,
      token ? `Bearer ${token}` : '',
      {
        apiUrl: API_URL + '/cubejs-api/v1',
        headers: {
          realm: localStorage.getItem('realms'),
          storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        }
      }
    );
  }

  // if (JSON.parse(localStorage.getItem('selectedStoreId')) != null) {

  //   cubejsToken = jwt.sign(
  //      { u: { storeId: JSON.parse(localStorage.getItem('selectedStoreId')), userRole: 'admin' } },
  //      CUBE_API_SECRET,
  //      { expiresIn: '30d' }
  //    );

  // }
};
export default cubejsApi;
