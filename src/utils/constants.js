export const GROUP_TYPE_1 = 'Labor';
export const GROUP_TYPE_2 = 'Parts';
export const GROUP_TYPE_3 = 'ELR';

export const patterns = {
  // Allows alphanumeric characters, spaces, hyphens, and apostrophes
  alphanumericWithSpecialCharsRegex: /^[a-zA-Z0-9\s-']*$/,

  // Allows alphanumeric characters, spaces, and certain special characters used in addresses
  alphanumericWithAddressSpecialCharsRegex: /^[a-zA-Z0-9\s@#&()-,.]*$/,

  // Allows only alphabetic characters
  alphabeticOnlyRegex: /^[a-zA-Z]*$/,

  // Allows alphanumeric characters only
  alphanumericOnlyRegex: /^[a-zA-Z0-9]*$/,

  // Allows valid website URLs (with or without protocol and www)
  urlRegex: /^(https?:\/\/)?(www\.)?([a-zA-Z0-9_-]+\.){1,}[a-zA-Z]{2,}(\/[^\s]*)?(\?(\w+=\w+(&\w+=\w+)*)?)?$/,

  // Allows lowercase alphabetic characters and numbers
  lowercaseAlphanumericRegex: /^[a-z0-9]*$/,

  // Used for generating IDs by replacing non-alphanumeric characters
  nonAlphanumericReplaceRegex: /[^a-z0-9]+/g,

  // Allows numeric values, including decimal numbers
  numericWithOptionalDecimalRegex: /^(?:\d+)?(?:\.\d+)?$/,

  // Allows alphanumeric characters with double underscore and single underscore as separators
  alphanumericWithDoubleUnderscoreAndSingleUnderscoreRegex: /^[a-zA-Z0-9]+__[a-zA-Z0-9]+_[a-zA-Z0-9]+$/
};

export const csvDataParts = [
  [
    'price_start_range',
    'price_end_range',
    'calc_base',
    'break_field',
    'add_percentage'
  ],
  ['0.01', '9.99', 'LIST%', 'LIST', '180'],
  ['10', '17.99', 'LIST%', 'LIST', '175'],
  ['18', '25.99', 'LIST%', 'LIST', '170'],
  ['26', '39.99', 'LIST%', 'LIST', '170'],
  ['40', '69.99', 'LIST%', 'LIST', '165'],
  ['70', '139.99', 'LIST%', 'LIST', '150'],
  ['140', '299.99', 'LIST%', 'LIST', '130'],
  ['300', '599.99', 'LIST%', 'LIST', '115'],
  ['600', '99999.99', 'LIST%', 'LIST', '100']
];

export const csvDataLabor = [
  ['', '0', '0.1', '0.2', '0.3', '0.4', '0.5', '0.6', '0.7', '0.8', '0.9'],
  [
    '0',
    '1',
    '19.5',
    '39',
    '58.5',
    '78',
    '97.5',
    '117',
    '136.5',
    '156',
    '175.5'
  ],
  [
    '1',
    '195',
    '214.61',
    '234.24',
    '253.89',
    '273.56',
    '293.25',
    '312.96',
    '332.69',
    '352.44',
    '372.21'
  ],
  [
    '2',
    '392',
    '411.81',
    '431.64',
    '451.49',
    '471.36',
    '491.25',
    '511.16',
    '531.09',
    '551.04',
    '571.01'
  ],
  [
    '3',
    '591',
    '611.01',
    '631.04',
    '651.09',
    '671.16',
    '691.25',
    '711.36',
    '731.49',
    '751.64',
    '771.81'
  ],
  [
    '4',
    '792',
    '812.21',
    '832.44',
    '852.69',
    '872.96',
    '893.25',
    '913.56',
    '933.89',
    '954.24',
    '974.61'
  ],
  [
    '5',
    '995',
    '1015',
    '1035',
    '1056',
    '1076',
    '1096',
    '1116',
    '1138',
    '1158',
    '1179'
  ],
  [
    '6',
    '1200',
    '1220',
    '1240',
    '1261',
    '1281',
    '1302',
    '1322',
    '1344',
    '1365',
    '1386'
  ],
  [
    '7',
    '1407',
    '1427',
    '1447',
    '1468',
    '1489',
    '1511',
    '1532',
    '1553',
    '1573',
    '1595'
  ],
  [
    '8',
    '1616',
    '1637',
    '1658',
    '1679',
    '1699',
    '1722',
    '1742',
    '1762',
    '1784',
    '1805'
  ],
  [
    '9',
    '1827',
    '1848',
    '1868',
    '1890',
    '1911',
    '1932',
    '1953',
    '1975',
    '1997',
    '2018'
  ],
  [
    '10',
    '2040',
    '2061',
    '2082',
    '2103',
    '2125',
    '2151',
    '2173',
    '2192',
    '2214',
    '2233'
  ],
  [
    '11',
    '2255',
    '2274',
    '2296',
    '2316',
    '2337',
    '2657',
    '2378',
    '2398',
    '2419',
    '2439'
  ],
  [
    '12',
    '2460',
    '2480',
    '2501',
    '2521',
    '2542',
    '2562',
    '2583',
    '2602',
    '2624',
    '2643'
  ],
  [
    '13',
    '2665',
    '2684',
    '2705',
    '2725',
    '2747',
    '2766',
    '2788',
    '2807',
    '2829',
    '2948'
  ],
  [
    '14',
    '2968',
    '2889',
    '2911',
    '2931',
    '2952',
    '2972',
    '2993',
    '3013',
    '3034',
    '3054.5'
  ],
  [
    '15',
    '3075',
    '3095.5',
    '3116',
    '3136.5',
    '3157',
    '3177.5',
    '3198',
    '3218.5',
    '3239.3',
    '259.5'
  ]
];

const baseROIOptions = [
  { key: 'all', label: 'All' },
  { key: 'monthlyPts', label: 'Monthly Pts & Lbr GP' },
  { key: 'monthlyElr', label: 'Monthly ELR' }
];

export const oneMonthRoiOption = [...baseROIOptions];

export const threeMonthRoiOption = [
  ...baseROIOptions,
  { key: 'annualizedPts', label: '3 MTH Annualized Pts & Lbr GP' },
  { key: 'annualizedElr', label: '3 MTH Annualized ELR' }
];

export const workmixOption = [
  { key: 'Competitive', label: 'Competitive' },
  { key: 'Maintenance', label: 'Maintenance' },
  { key: 'Repair', label: 'Repair' }
];

export const threeMonthRoiSelectedOptions = [
  ...baseROIOptions.map(option => option.key),
  ...threeMonthRoiOption.slice(baseROIOptions.length).map(option => option.key)
];

export const oneMonthRoiSelectedOptions = baseROIOptions.map(
  option => option.key
);
