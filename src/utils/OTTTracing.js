import { context, trace, Span, SpanStatusCode } from '@opentelemetry/api';
import { WebTracerProvider } from '@opentelemetry/sdk-trace-web';
import { Resource } from '@opentelemetry/resources';
import { SimpleSpanProcessor } from '@opentelemetry/sdk-trace-base';
import { CollectorTraceExporter } from '@opentelemetry/exporter-collector';
//import { ZoneContextManager } from "@opentelemetry/context-zone";
//import { ZoneContextManager } from "@opentelemetry/context-zone-peer-dep";
//import { FetchInstrumentation } from "@opentelemetry/instrumentation-fetch";
// import { FetchError } from "@opentelemetry/instrumentation-fetch/build/src/types";
// import { registerInstrumentations } from "@opentelemetry/instrumentation";
const opentelemetry = require('@opentelemetry/api');
const resource = new Resource({
  'service.name': String(process.env.REACT_APP_SERVICE_NAME)
});
const provider = new WebTracerProvider({ resource });

const collector = new CollectorTraceExporter({
  url: process.env.REACT_APP_TRACING_URL,
  headers: {}
});
provider.addSpanProcessor(new SimpleSpanProcessor(collector));
provider.register();
//provider.register({ contextManager: new ZoneContextManager() });

const webTracerWithZone = provider.getTracer(
  String(process.env.REACT_APP_SERVICE_NAME)
);

//const window;
// var bindingSpan;

// window.startBindingSpan = (
//      traceId ,
//      spanId,
//      traceFlags
// ) => {
//   bindingSpan = webTracerWithZone.startSpan("");
//   bindingSpan.spanContext().traceId = traceId;
//   bindingSpan.spanContext().spanId = spanId;
//   bindingSpan.spanContext().traceFlags = traceFlags;
// };

// window.flushTraces = () => {
//   provider.activeSpanProcessor.forceFlush().then(() => console.log("flushed"));
// };

// registerInstrumentations({
//   instrumentations: [
//     // @ts-ignore
//     new FetchInstrumentation({
//       propagateTraceHeaderCorsUrls: ["/.*/g"],
//       clearTimingResources: true,
//       applyCustomAttributesOnSpan: (
//         span,
//         request,
//         result,
//       ) => {
//         const attributes = span.attributes;
//         if (attributes.component === "fetch") {
//           span.updateName(
//             `${attributes["http.method"]} ${attributes["http.url"]}`
//           );
//         }
//         if (result.status && result.status > 299) {
//           span.setStatus({ code: SpanStatusCode.ERROR });
//         }
//       },
//     }),
//   ],
// });

// export function traceSpan(
//   name,
//   customContext

// ) {
//     if(process.env.REACT_APP_TRACING_ENABLE !== undefined && process.env.REACT_APP_TRACING_ENABLE=="true"){
//       var singleSpan;
//       const domain = new URL(window.location.href);
//       const spanAttribute = customContext;
//       spanAttribute.pageUrl = domain.pathname;
//       spanAttribute.origin = domain.origin;
//       if (bindingSpan) {
//       const ctx = trace.setSpan(context.active(), bindingSpan);
//       singleSpan = webTracerWithZone.startSpan(name, undefined, ctx);

//       bindingSpan = undefined;
//       } else {
//       singleSpan = webTracerWithZone.startSpan(name);
//       }
//       singleSpan.setAttributes(spanAttribute);
//       return context.with(trace.setSpan(context.active(), singleSpan), () => {
//       try {
//         const result = "";//func();
//         singleSpan.end();
//         return result;
//       } catch (error) {
//         singleSpan.setStatus({ code: SpanStatusCode.ERROR });
//         singleSpan.end();
//         throw error;
//       }
//       });
//     }
// }
export function traceSpan(spanName, context) {
  if (
    process.env.REACT_APP_TRACING_ENABLE !== undefined &&
    process.env.REACT_APP_TRACING_ENABLE == 'true'
  ) {
    const domain = new URL(window.location.href);
    const userID = localStorage.getItem('userID');
    const realmID = localStorage.getItem('realm');
    const storeID = localStorage.getItem('selectedStoreId');
    const store = localStorage.getItem('storeSelected');
    const tracer = opentelemetry.trace.getTracer('basic');
    let span, spanContext, parent;
    if (context == '') {
      spanContext = JSON.parse(getOTCookie('otCtx'));
    } else {
      spanContext = context;
    }
    //console.log("span");
    if (!getOTCookie('userIP')) {
      getIp();
    }
    if (!getOTCookie('otContext')) {
      span = tracer.startSpan(spanName);
    } else {
      spanContext.traceState = undefined;
      parent = opentelemetry.trace.wrapSpanContext(
        JSON.parse(getOTCookie('otContext'))
      );
      const ctx = opentelemetry.trace.setSpan(
        opentelemetry.context.active(),
        parent
      );
      span = tracer.startSpan(spanName, undefined, ctx);
    }

    const spanAttribute = spanContext;
    spanAttribute.pageUrl = domain.pathname;
    spanAttribute.origin = domain.origin;
    spanAttribute.fingerPrint = getOTCookie('finger');
    spanAttribute.userName = userID;
    spanAttribute.realmID = realmID;
    spanAttribute.storeID = storeID;
    spanAttribute.storeName = store;
    //console.log(spanAttribute);
    spanAttribute.userAgent =
      window && window.navigator ? window.navigator.userAgent : '';
    spanAttribute.userIP = getOTCookie('userIP');
    spanAttribute.userLocation = getOTCookie('userLocation');

    span.setAttributes(spanAttribute);

    span.addEvent('invoking work');
    const pctx = span.spanContext();
    //console.log(getOTCookie("otContext"));
    if (
      !getOTCookie('otContext') ||
      getOTCookie('otContext') == undefined ||
      getOTCookie('otContext') == null
    ) {
      setOTCookie('otContext', JSON.stringify(pctx));
    }
    span.end();
  }
}
function getOTCookie(key) {
  const keyValue = document.cookie.match('(^|;) ?' + key + '=([^;]*)(;|$)');
  return keyValue ? keyValue[2] : null;
}

function setOTCookie(key, value) {
  const expires = new Date();
  expires.setTime(expires.getTime() + 365 * 24 * 60 * 60 * 1000);
  document.cookie =
    key + '=' + value + ';expires=' + expires.toUTCString() + ';path=/';
}
function getIp() {
  fetch('https://geolocation-db.com/json/')
    .then(res => res.json())
    .then(result => {
      console.log(result);
      setOTCookie('userIP', result.IPv4);
      setOTCookie(
        'userLocation',
        result.country_name + '-' + result.state + '-' + result.city
      );
    })
    .catch(error => {
      // handle the error
    });
}
