import { ApolloLink, Observable } from 'apollo-link';
import { createHttpLink } from 'apollo-link-http';

const abortControllers = new Map();

export const cancelAllRequests = () => {
  abortControllers.forEach(controller => controller.abort());
  abortControllers.clear();
};

export const abortableHttpLink = createHttpLink({
  uri: process.env.REACT_APP_POSTGRES_ENDPOINT,
  fetch: (uri, options) => {
    const controller = new AbortController();
    abortControllers.set(options, controller);
    options.signal = controller.signal;

    return fetch(uri, options).finally(() => {
      abortControllers.delete(options);
    });
  }
});
