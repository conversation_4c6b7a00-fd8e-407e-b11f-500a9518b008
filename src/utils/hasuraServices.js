import makeApolloClient from './apolloRootClient';
import makeApolloClientPostgres from './apolloRootClientPostgres';
import makeApolloClientPostgresWrite from './apolloRootClientPostgresWrite';
import makeApolloClientPostgresAnonymous from './apolloClientPostgresAnonymous';

import gql from 'graphql-tag';
import moment from 'moment';
import { traceSpan } from 'src/utils/OTTTracing';
import { useDispatch, useSelector } from 'react-redux';

import {
  ADD_CHART_TO_FAVOURITE,
  DELETE_ALL_FAVOURITE_CHARTS,
  GET_ADD_ON_SERVICE_ADVISOR_DETAILS,
  GET_ADD_ON_VS_NON_ADD_ON_REVENUE_PERCENT,
  GET_ALL_CHART_DETAILS,
  GET_ALL_FAVOURITE_CHARTS,
  GET_ALL_SERVICE_ADVISOR_DETAILS,
  GET_ALL_SERVICEADVISORS,
  GET_ALL_TECHNICIANS,
  GET_AVERAGE_HOURS_SOLD_PER_TECH_DATA,
  GET_CLIENT_DETAILS,
  GET_COMPARATIVE_MIX_LEFT,
  GET_COMPARISON_DATA_MONTHWISE_LABOR,
  GET_COMPARISON_DATA_MONTHWISE_PARTS,
  GET_CP_GROSS_PROFITPERCENT_940,
  GET_CP_ELR_DATA,
  GET_CP_LABOR_1079_DETAILS,
  GET_CP_LABOR_1083_DETAILS,
  GET_CP_LABOR_ELR_1127_details,
  GET_CP_LABOR_ELR_1127_PAYTYPE_DETAILS,
  GET_CP_LABOR_RO_HOURS_DETAILS,
  GET_CP_LABOR_RO_HOURS_PAYTYPE_DETAILS,
  GET_CP_LABORSALE_PER_RO_DETAILS,
  GET_CP_LABORSALE_PER_RO_PAYTYPE_DETAILS,
  GET_CP_OVERVIEW_ELR_DETAILS_HASURA,
  GET_CP_OVERVIEW_ELR_PAYTYPE_DETAILS_HASURA,
  GET_CP_PART_MARKUP_PAYTYPE_DETAILS,
  GET_CP_PARTMARKUP_DETAILS,
  GET_CP_PARTS_RO_BY_YEAR,
  GET_CP_PARTSRO_BY_PAYTYPE_DETAILS,
  GET_CP_PARTSRO_BYCATEGORY_DETAILS,
  GET_DASHBOARD_DETAILS,
  GET_DATA_FOR_SERVICE_ADVISOR_SUMMARY,
  GET_DATA_FOR_SERVICE_ADVISOR_SUMMARY_BY_SA,
  GET_DATA_FOR_TECHNICIAN_SUMMARY,
  GET_DATA_FOR_TECHNICIAN_SUMMARY_BY_TECH_NO,
  GET_DATA_FOR_TECHNICIAN_VIEW,
  GET_DATA_RETURN_CURRENT_MONTH,
  GET_DATA_RETURN_LAST_MONTHS,
  GET_DISCOUNT_BY_SERVICE_ADVISOR,
  GET_DISCOUNT_BY_SERVICE_ADVISOR_OPCATEGORY,
  GET_DISCOUNT_RO_PERC_BY_SERVICE_ADVISOR,
  GET_DISCOUNTED_CP_PER_TOTAL_DISCOUNTED_CP_ROS,
  GET_DISCOUNTED_SALE_PERECENTAGE,
  GET_DISCOUNTS_PER_TOTAL_CP_ROS,
  GET_DISCOUNTS_PER_TOTAL_DISCOUNTED_CP_ROS,
  GET_DRILL_DOWN_CALCULATION_DATA_SA_SUMMARY,
  GET_DRILL_DOWN_DATA_BY_OPCODE,
  GET_DRILL_DOWN_DATA_BY_SERVICE_ADVISOR,
  GET_DRILL_DOWN_DATA_BY_TECH_NO,
  GET_DRILL_DOWN_DATA_FOR_ADD_ON,
  GET_DRILL_DOWN_DATA_FOR_DISCOUNT,
  GET_DRILL_DOWN_DATA_FOR_DISCOUNT_JOB_COUNT,
  GET_DRILL_DOWN_DATA_FOR_DISCOUNT_SUMMARY,
  GET_DRILL_DOWN_DATA_FOR_DISCOUNT_SUMMARY_PARTS,
  GET_DRILL_DOWN_DATA_FOR_DISCOUNT_SUMMARY_WITH_SA,
  GET_DRILL_DOWN_DATA_FOR_DISCOUNT_SUMMARY_WITH_SA_PARTS,
  GET_DRILL_DOWN_DATA_FOR_DISCOUNTED_JOBS_PER_RO,
  GET_DRILL_DOWN_DATA_FOR_LABOR,
  GET_DRILL_DOWN_DATA_FOR_LABOR_BY_OPCODE,
  GET_DRILL_DOWN_DATA_FOR_LABOR_ITEMIZATION,
  GET_DRILL_DOWN_DATA_FOR_MASTER_DISCOUNT_JOBS,
  GET_DRILL_DOWN_DATA_FOR_MOVING_ELR,
  GET_DRILL_DOWN_DATA_FOR_MOVING_PARTS_MARKUP,
  GET_DRILL_DOWN_DATA_FOR_OPCODE_VIEW,
  GET_DRILL_DOWN_DATA_FOR_PARTS,
  GET_DRILL_DOWN_DATA_FOR_PARTS_BY_OPCODE,
  GET_DRILL_DOWN_DATA_FOR_PARTS_ITEMIZATION,
  GET_DRILL_DOWN_DATA_FOR_RETURN_RATE,
  GET_DRILL_DOWN_DATA_FOR_RETURN_RATE_SA,
  GET_DRILL_DOWN_DATA_FOR_SH_BY_PAY_TYPE,
  GET_DRILL_DOWN_DATA_FOR_SINGLE_JOB,
  GET_DRILL_DOWN_DATA_FOR_MULTI_JOB,
  GET_DRILL_DOWN_DATA_FOR_SOLD_HOURS,
  GET_DRILL_DOWN_DATA_FOR_TECHNICIAN,
  GET_DRILL_DOWN_DATA_FOR_TECHNICIAN_BY_TECH_NO,
  GET_DRILL_DOWN_DATA_LABOR_DISCOUNT,
  GET_DRILL_DOWN_DATA_PARTS_DISCOUNT,
  GET_DRILL_DOWN_DISCOUNT_BY_RO,
  GET_DRILL_DOWN_DISCOUNT_SUMMARY_LABOR,
  GET_DRILL_DOWN_DISCOUNT_SUMMARY_LABOR_SA,
  GET_DRILL_DOWN_DISCOUNT_SUMMARY_PARTS,
  GET_DRILL_DOWN_DISCOUNT_SUMMARY_PARTS_SA,
  GET_DRILL_DOWN_FOR_AVERAGE_HOURS_SOLD_PER_TECH,
  GET_DRILL_DOWN_FOR_AVERAGE_HOURS_SOLD_PER_TECH_SA,
  GET_DRILL_DOWN_MONTH_YEAR_BY_OPCODE,
  GET_GROSS_PROFIT_DRILL_DOWN,
  GET_LABOR_PARTS_DISCOUNT_BY_MONTH,
  GET_LAST_THIRTEEN_MONTHS,
  GET_OPCODE_DETAILS,
  GET_NONCPOPCODE_DETAILS,
  ALL_STORE_NAMES,
  GET_PARTS_GROSS_OPPORTUNITY_DRILL_DOWN,
  GET_PARTS_GROSS_OPPORTUNITY_DRILL_DOWN_BY_DATE_RANGE,
  GET_PARTS_MARKUP_DRILL_DOWN,
  GET_PAY_TYPE_DETAILS_DRILL_DOWN,
  GET_PAY_TYPE_ERRORS,
  GET_PAYTYPE_MASTER_DETAILS,
  GET_SERVICE_ADVISOR_DRILL_DOWN,
  GET_SERVICE_ADVISOR_EFFICIENCY_BY_OPCATEGORY,
  GET_SERVICE_ADVISOR_REVENUES,
  GET_SERVICE_EFFICIENCY_BY_MONTHS,
  GET_SPECIALMETRICS_1174,
  GET_SPECIALMETRICS_1175,
  GET_SPECIALMETRICS_1357,
  GET_TECH_DETAIL_DRILL_DOWN,
  GET_TECH_EFFICIENCY_BY_MONTHS,
  GET_TOTAL_REVENUE_DETAILS_DRILL_DOWN,
  GET_VIEW_DETAILS,
  GET_WORKMIX_REPORT_LABOR,
  GET_WORKMIX_REPORT_PARTS,
  GET_WORKMIX_REPORT_TECH_EFFICIENCY,
  GET_WORKMIX_REPORT_TOTAL_VOLUME,
  GET_WORKMIX_REPORT_TOTAL_VOLUME_PARTS,
  REMOVE_CHART_FROM_FAVOURITE,
  GET_TECHNICIAN_REVENUE_AND_HOURS,
  GET_MOST_FREQUENT_TECHNICIAN,
  GET_STORE_DETAILS,
  GET_SHOP_SUPPLIES_DRILLDOWN,
  GET_STORE_NAMES_BY_STORE_ID,
  REFRESH_VIEWS,
  REFRESH_VIEWS_DATA,
  REFRESH_VIEWS_STATUS,
  REFRESH_STATUS_LOG,
  UPDATE_REFRESH_STATUS,
  GET_MPI_PERCENTAGE,
  GET_TOTAL_CUSTOMER_REVENUE_DETAILS_DRILL_DOWN,
  GET_MENU_PENETRATION_PERCENTAGE,
  GET_CP_PARTS_RO_HOURS_PAYTYPE_DETAILS,
  GET_SEARCHBYRO_DETAILS,
  GET_SEARCHBYRO_XML,
  GET_SEARCHBYRO_JSON,
  GET_DATA_FOR_TOTAL_PRICING_OPPORTUNITY,
  GET_TRANCHE_REPORT_FOR_LABOR_RATE_ANALYSIS,
  GET_TRANCHE_REPORT_FOR_INCLUDED_OPCODES,
  GET_TRANCHE_REPORT_FOR_EXCLUDED_OPCODES,
  GET_ELR_WARRANTY_PAY_TYPES,
  GET_TOP_FIVE_OPPORTUNITIES,
  GET_SEARCHBYRO_JSON_FERRARIO,
  GET_LATEST_CLOSED_DATE,
  GET_SEARCHBYRO_XML_RENOLDS,
  GET_VALID_MAKES,
  GET_INCLUDED_MAKES_DETAILS,
  GET_EXCLUDED_MAKES_DETAILS,
  GET_DRILL_DOWN_DATA_FOR_PARTS_MARKUP_DETAILS,
  GET_QUALIFIED_MAPPER_OPCODES,
  GET_NON_QUALIFIED_MAPPER_OPCODES,
  GET_FIXEDOPS_VS_MAPPER_OPCATEGORIZATION,
  GET_SHARED_SEQUENCES_BY_MAKE_OVERALL,
  GET_SHARED_SEQUENCES_BY_MAKE_MONTHLY,
  GET_NEW_CAR_WARRANTY_OVERALL,
  GET_NEW_CAR_WARRANTY_MONTHLY,
  GET_NEW_CAR_WARRANTY_SIX_MONTHS,
  GET_REVENUE_SUMMARY_LABOR_REVENUE_BY_CATEGORY,
  GET_REVENUE_SUMMARY_PARTS_REVENUE_BY_CATEGORY,
  GET_REVENUE_SUMMARY_WARRANTY_VOLUMES_LABOR,
  GET_REVENUE_SUMMARY_WARRANTY_VOLUMES_PARTS,
  GET_WARRANTY_RATES_LABOR,
  GET_DRILLDOWN_MONTHYEARS,
  GET_WARRANTY_RATES_PARTS,
  GET_WARRANTY_REFERENCE_LABOR,
  GET_WARRANTY_REFERENCE_PARTS,
  GET_MONTH_YEARS,
  GET_DRILL_DOWN_DATA_FOR_ALL_REVENUE,
  GET_LABOR_TOTAL_OPPORTUNITY,
  GET_PARTS_TOTAL_OPPORTUNITY,
  GET_ELR_TOTAL_OPPORTUNITY,
  GET_ALL_DUPLICATE_DASHBOARDS,
  GET_CHART_DETAILS,
  GET_LABOR_OPPORTUNITY_BASELINE,
  GET_PARTS_OPPORTUNITY_BASELINE,
  GET_ELR_OPPORTUNITY_BASELINE,
  GET_KPI_TOGGLE_OPTIONS,
  GET_DATA_FOR_CWITOTAL_CHARTS,
  GET_DATA_FOR_CWITOTAL_ADVISOR_CHARTS,
  GET_DATA_FOR_ROSHARE_CHART,
  GET_DATA_FOR_ROSHARE_ADVISOR_CHART,
  GET_DATA_FOR_KPI_LINERO_LT_SIXTY_K,
  GET_DATA_FOR_KPI_LINERO_ADVISOR_LT_SIXTY_K,
  GET_DATA_FOR_KPI_LINERO_GT_SIXTY_K,
  GET_DATA_FOR_KPI_LINERO_ADVISOR_GT_SIXTY_K,
  GET_DATA_FOR_KPI_AVG_AGE_MILES,
  GET_DATA_FOR_KPI_AVG_AGE_MILES_ADVISOR,
  GET_DATA_FOR_KPI_FLAT_RATE_HRS,
  GET_DATA_FOR_KPI_FLAT_RATE_HRS_ADVISOR,
  GET_DATA_FOR_KPI_LABOR_GP_RO,
  GET_DATA_FOR_KPI_LABOR_GP_RO_ADVISOR,
  GET_DATA_FOR_KPI_PARTS_GP_RO,
  GET_DATA_FOR_KPI_PARTS_GP_RO_ADVISOR,
  GET_DATA_FOR_KPI_TOTAL_GP_RO,
  GET_DATA_FOR_KPI_TOTAL_GP_RO_ADVISOR,
  GET_DATA_FOR_KPI_WORK_MIX,
  GET_DATA_FOR_KPI_WORK_MIX_ADVISOR,
  GET_DATA_FOR_KPI_LABOR_GRID,
  GET_DATA_FOR_KPI_LABOR_GRID_ADVISOR,
  GET_DATA_FOR_KPI_LINE_RO,
  GET_DATA_FOR_KPI_LINE_RO_ADVISOR,
  GET_LATEST_OPEN_DATE,
  GET_DATA_FOR_KPI_PARTS_GRID,
  GET_DATA_FOR_KPI_PARTS_GRID_ADVISOR,
  GET_DATA_FOR_LABOR_MISSES,
  GET_DATA_FOR_PARTS_MISSES,
  GET_GLOSSARY_DETAILS,
  GET_MENU_OPCODES,
  GET_MPI_OPCODES,
  OPPORTUNITY_MW_REFRESH,
  GET_MPI_OPCODE_DETAILS,
  GET_MENU_OPCODE_DETAILS,
  GET_TARGET_GRID_RATE,
  GET_KPI_TOGGLE_OPTIONS_WITH_TIMEZONE,
  GET_DATA_FOR_LABOR_MISSES_WITH_RO,
  GET_DATA_FOR_PARTS_MISSES_WITH_RO,
  GET_MENU_OPCODES_LIST,
  GET_MPI_OPCODES_LIST,
  GET_ADVISOR_NAME,
  GET_KPI_SCORE_CARD_GOAL,
  GET_CLIENT_ID,
  CHECK_INTERNAL_TOGGLE_EXISTANCE,
  GET_SET_KPI_SCORE_CARD_SETTINGS_EMAIL,
  GET_SET_STORE_SETTINGS,
  GET_SEARCHBYRO_JSON_DATA,
  GET_GRIDORMETRIX_DETAILS,
  GET_OPCODE_ERRORS,
  GET_MATRIX_PART_SOURCE,
  GET_NOTIFICATIONS_USER_LOGIN_HISTORY_STATUS,
  GET_DAILY_DATA_LOAD_STATUS,
  GET_GRIDORMETRIX_PAYTYPE_DETAILS,
  GET_GRIDORMETRIX_PERIODS,
  GET_OPCODE_DETAILED_VIEW_MONTH_YEARS,
  GET_DAILY_UPDATE_STATUS,
  GET_DAILY_UPDATE_STATUS_ALL,
  GET_UNIQUE_OPCODES,
  GET_LABOR_OPPORTUNITY_BASELINE_ADVISOR,
  GET_TOTAL_LABOR_OPPORTUNITY,
  GET_LABOR_TOTAL_OPPORTUNITY_ADVISOR,
  GET_PARTS_OPPORTUNITY_BASELINE_ADVISOR,
  GET_TOTAL_PARTS_OPPORTUNITY,
  GET_PARTS_TOTAL_OPPORTUNITY_ADVISOR,
  GET_TOTAL_ELR_OPPORTUNITY,
  GET_ELR_OPPORTUNITY_BASELINE_ADVISOR,
  GET_ELR_OPPORTUNITY_ADVISOR,
  GET_FLEET_CUSTOMER_NAMES,
  GET_FLEET_LIST,
  INSERT_FLEET_DETAILS,
  GET_FIXED_RATE_DETAILS,
  ADD_FLEET_TO_LIST,
  GET_RO_LIST,
  GET_AXCESSA_REPORT_SUMMARY,
  GET_SERVICE_ADVISOR_DATA,
  GET_SERVICE_ADVISOR_DATA_SA,
  GET_SCATTER_PLOT_REPORT_DATA,
  GET_WARRANTY_RATES_LABOR_DATERANGE,
  GET_TOTAL_REVENUE_DETAILS_DRILL_DOWN_DATE_RANGE,
  GET_PAYTYPE_LIST,
  GET_KPI_REPORT_2_DATA,
  GET_AVERAGE_DAYS_OPEN_BY_PAYTYPES,
  GET_LAUNCH_DATE,
  GET_PAYTYPE_FIXED_RATE_DETAILS,
  GET_USERS_LIST,
  GET_REALMS_ROLES,
  GET_GROUPS,
  GET_USER_ASSIGNED_ROLES,
  GET_USER_ASSIGNED_GRPS,
  GET_USER_CREDENTIALS,
  GET_RO_COUNT_FOR_SINGLE_AND_MULTI_LINE,
  GET_KPI_SCORE_CARD_DATA_STATUS,
  GET_STORE_ADVISOR_DETAILS,
  GET_CP_REVENUE_DATA,
  GET_CP_GP_DATA,
  GET_CP_GP_PERCENTAGE_DATA,
  GET_CP_LABOR_SOLD_HOURS_DATA,
  GET_CP_PARTS_MARKUP_DATA,
  GET_CP_PARTS_MARKUP_DATA_1238,
  GET_CP_LABOR_GP_DATA,
  GET_CP_LABOR_SOLDHOURS_DATA,
  GET_CP_LABOR_ELR_DATA,
  GET_CP_LABOR_HOURS_PER_RO_DATA,
  GET_CP_LABOR__RO_COUNT_DATA,
  GET_CP_LABOR__JOB_COUNT_DATA,
  GET_CP_LABOR__AVG_SALE_DATA,
  GET_DATA_FOR_ADVISOR_AND_TECH_LABOR_MISSES,
  GET_DATA_FOR_ADVISOR_AND_TECH_PARTS_MISSES,
  GET_PAYTYPE_WITHOUT_STORE,
  GET_STORE_ADVISOR_DETAILS_CDK,
  GET_CP_LABOR__REVENUE_DATA,
  GET_CP_LABOR_GP_PERC_DATA,
  GET_CP_LABOR_ELR_REPAIR_DATA,
  GET_CP_LABOR_COMP_MAINT_DATA,
  GET_CP_LABOR__DETAILS_GP_DATA,
  GET_CP_LABOR__DETAILS_SOLD_HOURS_DATA,
  GET_CP_LABOR__DETAILS_JOB_COUNT_DATA,
  GET_CP_LABOR__DETAILS_RO_COUNT_DATA,
  GET_SOLD_HOURS_BY_PAY_TYPE,
  GET_ELR_BY_PAY_TYPE,
  GET_PARTS_REVENUE_BY_SERVICE_ADVISOR,
  GET_PARTS_GROSSPROFIT_PERCENTAGE_BY_SERVICE_ADVISOR,
  GET_PARTS_MARKUP_BY_YEAR,
  GET_PARTS_HOURS_PER_RO_BY_YEAR,
  GET_PARTS_GROSS_PROFIT_BY_YEAR,
  GET_PARTS_REVENUE_PER_RO_BY_YEAR,
  GET_PARTS_RO_COUNT_BY_YEAR,
  GET_PARTS__RO_COUNT_BY_YEAR_PARTS_ONLY,
  GET_PARTS_MARKUP_REPAIR_AND_COMPETITIVE,
  GET_CP_LABOR__DETAILS_REVENUE_DATA,
  GET_CP_LABOR__DETAILS_ELR_DATA,
  GET_CP_LABOR_DETAILS_HOURS_PER_RO_DATA,
  GET_CP_LABOR_DETAILS_AVG_SALE_RO_COUNT_DATA,
  GET_CP_PARTS_DETAILS_REVENUE,
  GET_CP_PARTS_DETAILS_GP,
  GET_CP_PARTS_DETAILS_REVENUE_PER_RO_COUNT,
  GET_CP_PARTS_DETAILS_RO_COUNT,
  GET_CP_PARTS_DETAILS_HOURS_PER_RO_PARTS_ONLY,
  GET_CP_PARTS_DETAILS_RO_COUNT_PARTS_ONLY,
  GET_DISCOUNT_LABOR_PARTS_DATA,
  GET_DISCOUNTS_BY_DISC_LEVEL,
  GET_DISCOUNTED_RO_PERCENT,
  GET_PERCENT_DISCOUNTS_1115,
  GET_TOTAL_DISCOUNTS_1232,
  GET_TOTAL_DISCOUNTS_1236,
  GET__SPECIALMETRICS_AVERAGE_OPEN_DATE_DATA,
  GET__SPECIALMETRICS_SINGLE_LINE_RO_COUNT_PERC_DATA,
  GET__SPECIALMETRICS_MULTI_LINE_RO_COUNT_PERC_DATA,
  GET__SPECIALMETRICS_PARTS_TO_LABOR_RATIO_DATA,
  GET__SPECIALMETRICS_LABOR_SOLD_HOURS_DATA,
  GET__SPECIALMETRICS_PARTS_TO_TABOR_RATIO_BY_CATEGORY_DATA,
  GET__SPECIALMETRICS_MPI_PENETRATION_PERC_DATA,
  GET__SPECIALMETRICS_MENU_PENETRATION_PERC_DATA,
  GET__SPECIALMETRICS_MULTI_JOB_RO_COUNTS_DATA,
  GET__SPECIALMETRICS_SHOP_SUPPLIES_DATA,
  GET__SPECIALMETRICS_SINGLE_JOB_RO_COUNTS_DATA,
  GET__SPECIALMETRICS_RETURN_RATE_DATA,
  GET_OPPORTUNITY_LABOR_TOTAL_LABOR_OPPORTUNITY,
  GET_OPPORTUNITY_PARTS_TOTAL_PARTS_OPPORTUNITY,
  GET_ELR_TOTAL_PRICING_OPPORTUNITY,
  GET_LABOR_WORKMIX_DATA,
  GET_PARTS_WORKMIX_DATA,
  GET_TECHNICIAN_ESTIMATEDTECHNICIAN_SOLDHOURS_WEEKLY,
  GET__TECHNICIAN_TECH_JOB,
  GET_TECHNICIAN_ESTIMATEDTECH_PRODUCTIVITY_WEEKLY,
  GET_TECHNICIAN_DRILLDOWN_TECH_HOURS,
  GET_CUSTOMERHISTORY_DATA,
  GET_DRILL_DOWN_DATA_FOR_TECH_REPORT,
  GET_DRILL_DOWN_DATA_FOR_TECH_REPORT_ALL,
  GET_TECHNICIAN_SINGLE_JOB_RO_COUNT,
  GET_DRILL_DOWN_DATA_FOR_TECH_METRICS_SINGLE_JOB,
  GET_GRIDORMETRIX_PERIODS_PRTSOURCE,
  GET_GRIDORMETRIX_DETAILS_PRTS,
  GET_PARTS_MATRIX,
  GET_PARTS_MATRIX_DETAILS,
  GET_MPI_VALUE,
  INSERT_MPI_VALUE,
  GET_KPI_COMPARATIVE_REPORT,
  GET_KPI_COMPARATIVE_STORE_REPORT,
  GET_KPI_SCORE_CARD_VALUES,
  GET_HOME_KPIS,
  GET_MENU_SERVICE_TYPE,
  GET_MENU_NAMES,
  MENU_DETAILS,
  GET_MENU_NAMES_DATA,
  GET_MENU_POPUP,
  GET_FILTERED_OPCODE,
  EDIT_HISTORY_ALL,
  EDIT_HISTORY,
  GET_DATA_FOR_ADVISOR_AND_TECH_LABOR_MISSES_OLD,
  GET_DATA_FOR_LABOR_MISSES_OLD,
  GET_DATA_FOR_LABOR_MISSES_WITH_RO_OLD,
  GET_DATA_FOR_PARTS_MISSES_OLD,
  GET_DATA_FOR_PARTS_MISSES_WITH_RO_OLD,
  GET_DATA_FOR_ADVISOR_AND_TECH_PARTS_MISSES_OLD,
  GET_VERSION_FLAGS,
  GET_MATRIX_PART_SOURCE_OLD,
  GET_GRIDORMETRIX_PAYTYPE_DETAILS_OLD,
  GET_GRIDORMETRIX_PERIODS_OLD,
  GET_GRIDORMETRIX_PERIODS_PRTSOURCE_OLD,
  GET_SEARCHBYRO_DETAILS_OLD,
  GET_GRIDORMETRIX_DETAILS_OLD,
  GET_GRIDORMETRIX_DETAILS_PRTS_OLD,
  GET_MATRIX_TYPE,
  GET_MATRIX_DETAIL,
  GET_MENU_MODEL_DATA,
  CRUD_MENU_MODELS,
  GET_EDIT_HISTORY_TOGGLE_OPTIONS_WITH_TIMEZONE,
  GET_VERSION_FLAG_BEFORE_LOGIN,
  GET_TECH_PRODUCTIVITY_ALL_ROS,
  GET_LAST_THREE_YEARS,
  GET_LABOR_MISSES_MODELS,
  INSERT_MAKE_DETAILS,
  GET_LABOR_MISSES_GRID_TYPES,
  GET_GRIDORMETRIX_PAYTYPE_DETAILS_BY_GRID_TYPE,
  GET_PARTS_LIST,
  GET_PARTS_DETAILS,
  GET_OPCODE_DETAILS_OLD,
  GET_FIXED_RATE_DETAILS_OLD,
  GET_PAYTYPE_FIXED_RATE_DETAILS_OLD,
  PARTS_MATRIX_FILE_UPLOAD,
  PARTS_MATRIX_TYPE,
  GET_PARTS_SOURCE,
  GET_WORKMIX_REPORT_LABOR_OLD,
  GET_WORKMIX_REPORT_PARTS_OLD,
  GET_WORKMIX_REPORT_TOTAL_VOLUME_OLD,
  GET_WORKMIX_REPORT_TOTAL_VOLUME_PARTS_OLD,
  GET_SERVICE_ADVISOR_REVENUES_OLD,
  GRID_TYPE_FILE_UPLOAD,
  GET_WORKMIX_REPORT_TECH_EFFICIENCY_OLD,
  GET_MATRIX_DETAIL_NEW,
  GET_MATRIX_TYPE_NEW,
  INSERT_FLEET_DETAILS_NEW,
  GET_PARTS_LIST_NEW,
  PARTS_MATRIX_FILE_UPLOAD_NEW,
  GRID_TYPE_FILE_UPLOAD_NEW,
  PARTS_MATRIX_TYPE_NEW,
  GET_PARTS_SOURCE_NEW,
  GET_STORE_NICKNAME,
  GET_GRIDORMETRIX_MISSES_DETAILS,
  GET_GRIDORMETRIX_PAYTYPE_DETAILS_REPORT,
  GET_PARTSMATRIX_DETAILS,
  GET_MATRIX_DETAILS,
  GET_GRID_TYPE_OPTIONS,
  INSERT_OR_UPDATE_PARTSMATRIX,
  GET_LABOR_GRID_LIST,
  CREATE_MATRIX_NAME,
  FETCH_OPCODE_LIST,
  INSERT_OR_UPDATE_GRID_NAME,
  FETCH_GRID_TYPES,
  CREATE_GRID_WITH_DOOR_RATE,
  INSERT_OR_UPDATE_LABOR_GRID,
  GET_DATA_FOR_ONE_LINE_RO_DRILLDOWN,
  GET_MPI_STATS_DRILLDOWN_REPORT,
  GET_DRILL_DOWN_TECH_ADVISORS_WEEKLY_REVENUES,
  GET_DRILLDOWN_TECH_RO_COUNT,
  INSERT_KPI_REPORT_NAME,
  GET_KPI_SAVED_REPORTS,
  GET_KPI_REPORT,
  GET_EMAIL,
  GET_SAVED_REPORT_DETAILS,
  DELETE_KPI_REPORT,
  SEND_SAVED_REPORT_EMAILS,
  GET_RECIPIENT_EMAILS,
  GET_DETAIL_SUMMARY_DATA,
  GET_REVENUE_DETAILS_DRILL_DOWN,
  GET_RO_HIDE_RULES,
  GET_RO_HIDE_RULES_LIST,
  EDIT_RO_HIDE_RULES_LIST,
  DELETE_RO_HIDE_RULES,
  VIEW_RO_HIDE,
  INSERT_RO_HIDE_RULES,
  GET_RO_HIDE_COLUMNS,
  GET_RO_HIDE_OPERATOR,
  GET_REVENUE_SUMMARY_WARRANTY_VOLUMES_LABOR_NEW,
  GET_REVENUE_SUMMARY_WARRANTY_VOLUMES_PARTS_NEW,
  GET_REVENUE_DETAILS_DRILL_DOWN_SA,
  UPDATE_USER_ENABLE_STATUS,
  GET_KPI_SCORE_CARD_GOAL_TECHNICIAN,
  GET_MONTHLY_CLIENT_REPORT_CARD_DETAILS,
  GET_THREE_MONTH_CLIENT_REPORT_CARD_DETAILS,
  GET_ONEMONTH_REPORT_MONTHS,
  INSERT_KPI_MONTH_CARD_REPORT_NAME,
  GET_SAVED_REPORT_CARD_DETAILS,
  ENABLE_USER_MFA_STATUS,
  DISABLE_USER_MFA_STATUS,
  GET_PAYTYPE_RETAIL_FLAG,
  UPDATE_OR_DELETE_LABOR_GRID,
  GET_STORE_RETAIL_FLAGS,
  INSERT_MPI_STATS_REPORT_NAME,
  INSERT_KPI_ONELINE_SAVED_REPORT,
  INSERT_LABOR_MISSES_REPORT,
  GET_DRILL_DOWN_TECH_ADVISORS_WEEKLY_SOLD_HOURS,
  INSERT_PARTS_MISSES_REPORT,
  GET_DRILL_DOWN_DATA_LABOR_PARTS_DISCOUNT,
  GET_CP_LABOR_OVERVIEW_DATA,
  GET_CP_PARTS_OVERVIEW_DATA,
  GET_CP_SUMMARY_OVERVIEW_DATA,
  GET_COMPARISON_DATA_MONTHWISE_LABOR_ALL,
  GET_SERVICE_EFFICIENCY_BY_MONTHS_ALL,
  GET_COMPARISON_DATA_MONTHWISE_PARTS_ALL,
  GET_SERVICE_ADVISOR_EFFICIENCY_BY_OPCATEGORY_ALL,
  GET_TECHNICIAN_REVENUE_AND_HOURS_ALL,
  GET_SPECIAL_METRICS_CHART_DATA,
  GET_SPECIAL_METRICS_SUMMARY_DATA
  // GET_DRILL_DOWN_DATA_FOR_TECH_REPORT_ALL
} from 'src/graphql/queries';
import { getLast13Months, getTimeZone } from './Utils';
import { string } from 'prop-types';
import { ConsoleSpanExporter } from '@opentelemetry/sdk-trace-web';
import { pre } from 'react-dom-factories';
const apolloClient = makeApolloClient;
const apolloClientPostgres = makeApolloClientPostgres;
const apolloClientPostgresWrite = makeApolloClientPostgresWrite;
const apolloClientPostgresAnonymous = makeApolloClientPostgresAnonymous;

let storeId;
if (localStorage.getItem('selectedStoreId')) {
  storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
}
var timeZone = getTimeZone();
const start = new Date();

export const getDrillDownTechAdvisorsweeklyRevenues = (
  wstartdate,
  wenddate,
  lbrTechno,
  serviceAdvisor,
  monthyear,
  paytypes,
  chartId,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query:
        chartId == 1363
          ? GET_DRILL_DOWN_TECH_ADVISORS_WEEKLY_REVENUES
          : GET_DRILL_DOWN_TECH_ADVISORS_WEEKLY_SOLD_HOURS,
      fetchPolicy: 'no-cache',
      variables: {
        // storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        wstartdate: wstartdate,
        wenddate: wenddate,
        lbrTechno: lbrTechno,
        serviceAdvisor: serviceAdvisor,
        monthyear: monthyear,
        paytypes: paytypes
      }
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/TechnicianPerformance',
        origin: '',
        event: 'Menu Load',
        is_from:
          chartId == 1363
            ? 'GET_DRILL_DOWN_TECH_ADVISORS_WEEKLY_REVENUES'
            : 'GET_DRILL_DOWN_TECH_ADVISORS_WEEKLY_SOLD_HOURS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getPartsSource = (pCallType, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_PARTS_SOURCE,
      fetchPolicy: 'no-cache',
      variables: {
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        pCallType: pCallType
      }
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/FleetAccounts',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_PARTS_SOURCE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const partsMatrixType = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: PARTS_MATRIX_TYPE,
      fetchPolicy: 'no-cache',
      variables: {
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      }
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/FleetAccounts',
        origin: '',
        event: 'Menu Load',

        is_from: 'PARTS_MATRIX_TYPE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const menuDetails = (menuname, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: MENU_DETAILS,
      fetchPolicy: 'no-cache',
      variables: {
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        menuname: menuname
      }
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Mpi',
        origin: '',
        event: 'Menu Load',
        is_from: 'MENU_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getMenuPopup = (menuName, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_MENU_POPUP,
      variables: {
        menuName: menuName,
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      console.log('result====', result);
      const spanAttribute = {
        pageUrl: '/Mpi',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_MENU_POPUP',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getFilteredOpcode = (menuName, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_FILTERED_OPCODE,
      variables: {
        menuName: menuName,
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      console.log('result====', result);
      const spanAttribute = {
        pageUrl: '/Mpi',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_FILTERED_OPCODE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getEditHistoryAll = (
  startdate,
  enddate,
  optionName,
  timezoneOffset,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: EDIT_HISTORY_ALL,
      variables: {
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        startdate: startdate,
        enddate: enddate,
        argoptionName: optionName,
        timezoneOffset: timezoneOffset
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      console.log('result====', result);
      const spanAttribute = {
        pageUrl: '/EditHistoryAll',
        origin: '',
        event: 'Menu Load',
        is_from: 'EDIT_HISTORY_ALL',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getEditHistory = (
  startdate,
  enddate,
  optionName,
  timezoneOffset,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: EDIT_HISTORY,
      variables: {
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        startdate: startdate,
        enddate: enddate,
        argoptionName: optionName,
        timezoneOffset: timezoneOffset
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      console.log('result====', result);
      const spanAttribute = {
        pageUrl: '/EditHistory',
        origin: '',
        event: 'Menu Load',
        is_from: 'EDIT_HISTORY',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getMenuNames = callback => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_MENU_NAMES,
      variables: {
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      console.log('result====', result);
      const spanAttribute = {
        pageUrl: '/Mpi',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_MENU_NAMES',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getMenuData = (menuName, serviceType, callback) => {
  console.log('callback====', callback);
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_MENU_NAMES_DATA,
      variables: {
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        username: localStorage.getItem('userID'),
        inMenuname: menuName,
        inServicetype: serviceType
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      console.log('result====', result);
      const spanAttribute = {
        pageUrl: '/Mpi',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_MENU_NAMES_DATA',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getMenuServiceType = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_MENU_SERVICE_TYPE,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Mpi',
        origin: '',
        event: 'Menu Load',
        is_from: 'REFRESH_STATUS_LOG',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getRefreshViewsStatus = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: REFRESH_VIEWS_STATUS,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      // const spanAttribute = {
      //   pageUrl: '/PayTypeMaster',
      //   origin: '',
      //   event: 'Menu Load',
      //   is_from: 'REFRESH_VIEWS_STATUS',
      //   value: new Date() - start,
      provenance: localStorage.getItem('provenance');
      // };
      // traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getRefreshStatusLog = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: REFRESH_STATUS_LOG,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PayTypeMaster',
        origin: '',
        event: 'Menu Load',
        is_from: 'REFRESH_STATUS_LOG',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const updateRefreshViews = callback => {
  apolloClientPostgres
    .query({
      query: REFRESH_VIEWS,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const updateRefreshStatus = (status, callback) => {
  const start = new Date();
  apolloClientPostgresWrite
    .query({
      query: UPDATE_REFRESH_STATUS,
      variables: {
        status: status
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/OPcodes',
        origin: '',
        event: 'Menu Load',
        is_from: 'UPDATE_REFRESH_STATUS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const updateRefreshViewsData = (user_id, callback) => {
  apolloClientPostgres
    .query({
      query: REFRESH_VIEWS_DATA,
      variables: {
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        user_id: user_id
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      // const spanAttribute = {
      //   pageUrl: '/PayTypeMaster',
      //   origin: '',
      //   event: 'Menu Load',
      //   is_from: 'REFRESH_VIEWS_DATA',
      //   value: new Date() - start,
      provenance: localStorage.getItem('provenance');
      // };
      // traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      callback(error);
      return error;
    });
};
export const getmvrefreshstatus = (user_id, callback) => {
  apolloClientPostgres
    .query({
      query: REFRESH_VIEWS_DATA,
      variables: {
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        user_id: user_id
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      callback(error);
      return error;
    });
};
export const getDrillDownData = (queryMonth, callback) => {
  apolloClient
    .query({
      query: GET_PARTS_GROSS_OPPORTUNITY_DRILL_DOWN,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDrillDownByMonth = (queryMonths, callback) => {
  apolloClient
    .query({
      query: GET_PARTS_GROSS_OPPORTUNITY_DRILL_DOWN_BY_DATE_RANGE,
      variables: {
        startDate: queryMonths[0],
        endDate: queryMonths[1],
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getWorkMixGridData = (query, callback) => {
  apolloClient
    .query({
      query: GET_COMPARATIVE_MIX_LEFT,
      variables: {
        dtfrom: query[0],
        dtto: query[1],
        group_by: query[2],
        mon: query[3],
        filter_by: query[4]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDrillDownDataForTotalRevenue = (
  queryMonth,
  selectedStoreId,
  paytype,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_TOTAL_REVENUE_DETAILS_DRILL_DOWN,
      variables: {
        month_year: queryMonth,
        paytype: paytype,
        store_id: selectedStoreId ? selectedStoreId : storeId
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/AnalyzeData',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_TOTAL_REVENUE_DETAILS_DRILL_DOWN',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForPayTypeErrors = (
  PayTypeDrilldown,
  callback
) => {
  apolloClientPostgres
    .query({
      query: GET_PAY_TYPE_DETAILS_DRILL_DOWN,
      variables: {
        paytype: PayTypeDrilldown,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDrillDownDataForGrossProfit = (queryMonth, callback) => {
  apolloClient
    .query({
      query: GET_GROSS_PROFIT_DRILL_DOWN,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDrillDownDataForPartsMarkup = (queryMonth, callback) => {
  apolloClient
    .query({
      query: GET_PARTS_MARKUP_DRILL_DOWN,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getWorkMixReportForParts = (viewFor, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_WORKMIX_REPORT_PARTS
          : GET_WORKMIX_REPORT_PARTS_OLD,
      variables: {
        view_for: viewFor,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsWorkMixAnalysis',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_WORKMIX_REPORT_PARTS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getWorkMixReportForLabor = (viewFor, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_WORKMIX_REPORT_LABOR
          : GET_WORKMIX_REPORT_LABOR_OLD,
      variables: {
        view_for: viewFor,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/LaborWorkMixAnalysis',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_WORKMIX_REPORT_LABOR',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDrillDownDataForLabor = (
  queryMonth,
  opCode,
  duration,
  callback
) => {
  if (opCode != '') {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_DRILL_DOWN_DATA_FOR_LABOR_BY_OPCODE,
        variables: {
          month_year: queryMonth,
          lbropcode: opCode,
          duration: duration
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/LaborWorkMixAnalysis',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_DRILL_DOWN_DATA_FOR_LABOR_BY_OPCODE',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(result);
      })
      .catch(error => {
        return error;
      });
  } else {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_DRILL_DOWN_DATA_FOR_LABOR,
        variables: {
          //month_year: queryMonth,
          duration: duration,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/LaborWorkMixAnalysis',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_DRILL_DOWN_DATA_FOR_LABOR',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(result);
      })
      .catch(error => {
        return error;
      });
  }
};
export const getDrillDownDataForParts = (
  queryMonth,
  opCode,
  duration,
  callback
) => {
  if (opCode != '') {
    apolloClientPostgres
      .mutate({
        mutation: GET_DRILL_DOWN_DATA_FOR_PARTS_BY_OPCODE,
        variables: {
          month_year: queryMonth,
          lbropcode: opCode,
          duration: duration
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  } else {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_DRILL_DOWN_DATA_FOR_PARTS,
        variables: {
          //month_year: queryMonth,
          duration: duration,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/PartsWorkMixAnalysis',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_DRILL_DOWN_DATA_FOR_PARTS',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(result);
      })
      .catch(error => {
        return error;
      });
  }
};
export const getDrillDownDataByOpcode = (opcode, queryMonth, callback) => {
  if (opcode != '') {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DATA_BY_OPCODE,
        variables: {
          lbropcode: opcode,
          month_year: queryMonth,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  } else {
    const start = new Date();
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DATA_FOR_OPCODE_VIEW,
        variables: {
          month_year: queryMonth,
          lbropcode: '',
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/LaborWorkMixAnalysis',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_DRILL_DOWN_DATA_FOR_OPCODE_VIEW',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(result);
      })
      .catch(error => {
        return error;
      });
  }
};

export const getDrillDownMonthYearByOpcode = (opcode, callback) => {
  apolloClient
    .query({
      query: GET_DRILL_DOWN_MONTH_YEAR_BY_OPCODE,
      variables: {
        lbropcode: opcode,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDrillGraphDataForMonthWiseComparisonParts = (
  parameters,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_COMPARISON_DATA_MONTHWISE_PARTS,
      variables: {
        mon1: parameters[0],
        mon2: parameters[1],
        view_for: parameters[2],
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsWorkMixAnalysis',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_COMPARISON_DATA_MONTHWISE_PARTS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDrillGraphDataForMonthWiseComparisonLabor = (
  parameters,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_COMPARISON_DATA_MONTHWISE_LABOR,
      variables: {
        mon1: parameters[0],
        mon2: parameters[1],
        view_for: parameters[2],
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/LaborWorkMixAnalysis',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_COMPARISON_DATA_MONTHWISE_LABOR',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getWorkMixReportTotalVolume = (tblFor, viewFor, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_WORKMIX_REPORT_TOTAL_VOLUME
          : GET_WORKMIX_REPORT_TOTAL_VOLUME_OLD,
      variables: {
        table_for: tblFor,
        view_for: viewFor,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/WorkMixVolume',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_WORKMIX_REPORT_TOTAL_VOLUME',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForReturnRate = (queryMonth, callback) => {
  apolloClientPostgres
    .query({
      query: GET_DRILL_DOWN_DATA_FOR_RETURN_RATE,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForMovingElr = callback => {
  apolloClientPostgres
    .query({
      query: GET_DRILL_DOWN_DATA_FOR_MOVING_ELR,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDataReturnedForCurrentMonth = (queryMonth, callback) => {
  apolloClientPostgres
    .query({
      query: GET_DATA_RETURN_CURRENT_MONTH,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getAllChartDetailsForDisplay = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_ALL_CHART_DETAILS
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/ChartMaster',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_ALL_CHART_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDataReturnedForLastMonths = (queryMonth, callback) => {
  apolloClientPostgres
    .query({
      query: GET_DATA_RETURN_LAST_MONTHS,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForMovingPartsMarkup = callback => {
  apolloClientPostgres
    .query({
      query: GET_DRILL_DOWN_DATA_FOR_MOVING_PARTS_MARKUP,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDatForSingleJob = (queryMonth, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_DRILL_DOWN_DATA_FOR_SINGLE_JOB,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/AnalyzeData',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DRILL_DOWN_DATA_FOR_SINGLE_JOB',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDatForMultiJob = (queryMonth, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_DRILL_DOWN_DATA_FOR_MULTI_JOB,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/AnalyzeData',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DRILL_DOWN_DATA_FOR_MULTI_JOB',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForLaborItemization = (
  tabSelection,
  timeZoneVal,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_DRILL_DOWN_DATA_FOR_LABOR_ITEMIZATION,
      variables: {
        Period:
          tabSelection == 'one'
            ? 30
            : tabSelection == 'two'
            ? 90
            : tabSelection == 'four'
            ? 7
            : 180,
        timeZone: timeZoneVal ? timeZoneVal : timeZone,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/LaborItemization',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DRILL_DOWN_DATA_FOR_LABOR_ITEMIZATION',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDatForSHByPayType = (queryMonth, callback) => {
  apolloClient
    .query({
      query: GET_DRILL_DOWN_DATA_FOR_SH_BY_PAY_TYPE,
      variables: { month_year: queryMonth },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForPartsItemization = (
  tabSelection,
  timeZoneVal,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_DRILL_DOWN_DATA_FOR_PARTS_ITEMIZATION,
      variables: {
        duration:
          tabSelection == 'one'
            ? 30
            : tabSelection == 'two'
            ? 90
            : tabSelection == 'four'
            ? 7
            : 180,
        timeZone: timeZoneVal ? timeZoneVal : timeZone,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsItemization',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DRILL_DOWN_DATA_FOR_PARTS_ITEMIZATION',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getROCountForSingleAndMultiLine = (queryMonth, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_RO_COUNT_FOR_SINGLE_AND_MULTI_LINE,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsItemization',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_RO_COUNT_FOR_SINGLE_AND_MULTI_LINE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};
// export const getROCountForTotalRevenue = (
//   queryMonth,
//   serviceAdvisor,
//   callback
// ) => {
//   console.log('hif', serviceAdvisor);
//   if (serviceAdvisor != 'All') {
//     apolloClientPostgres
//       .query({
//         query: GET_RO_COUNT_FOR_TOTAL_REVENUE_SA,
//         variables: {
//           month_year: queryMonth,
//           serviceadvisor: serviceAdvisor,
//           store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
//         },
//         fetchPolicy: 'no-cache'
//       })
//       .then(result => {
//         callback(result);
//       })
//       .catch(error => {
//         return error;
//       });
//   } else {
//     apolloClientPostgres
//       .query({
//         query: GET_RO_COUNT_FOR_TOTAL_REVENUE,
//         variables: {
//           month_year: queryMonth,
//           store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
//         },
//         fetchPolicy: 'no-cache'
//       })
//       .then(result => {
//         callback(result);
//       })
//       .catch(error => {
//         return error;
//       });
//   }
// };
export const getDrillDownDataForSoldHours = (queryMonth, callback) => {
  apolloClient
    .query({
      query: GET_DRILL_DOWN_DATA_FOR_SOLD_HOURS,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getPayTypeMasterDetails = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_PAYTYPE_MASTER_DETAILS,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      //console.log('startttt==', start, new Date(), new Date() - start);
      const spanAttribute = {
        pageUrl: '/PayTypeMaster',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_PAYTYPE_MASTER_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getPayTypeRetailFlag = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_PAYTYPE_RETAIL_FLAG,
      variables: {
        // store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      //console.log('startttt==', start, new Date(), new Date() - start);
      const spanAttribute = {
        pageUrl: '/PayTypeMaster',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_PAYTYPE_RETAIL_FLAG',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getAllTechnicians = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_ALL_TECHNICIANS,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Technicians',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_ALL_TECHNICIANS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getTechEfficiencyByMonths = (mon1, mon2, viewFor, callback) => {
  apolloClientPostgres
    .query({
      query: GET_TECH_EFFICIENCY_BY_MONTHS,
      variables: {
        mon1: mon1,
        mon2: mon2,
        view_for: viewFor,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getServiceEfficiencyByMonths = (mon1, mon2, viewFor, callback) => {
  apolloClientPostgres
    .query({
      query: GET_SERVICE_EFFICIENCY_BY_MONTHS,
      variables: {
        mon1: mon1,
        mon2: mon2,
        view_for: viewFor,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getAllServiceAdvisors = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_ALL_SERVICEADVISORS,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/AnalyzeData',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_ALL_SERVICEADVISORS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDrillDownDataForTechDetail = (techNo, callback) => {
  apolloClientPostgres
    .query({
      query: GET_TECH_DETAIL_DRILL_DOWN,
      variables: {
        lbrtechno: techNo,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getServiceAdvisorEfficiencyByOpCategory = (
  mon,
  viewFor,
  callback
) => {
  apolloClientPostgres
    .mutate({
      mutation: GET_SERVICE_ADVISOR_EFFICIENCY_BY_OPCATEGORY,
      variables: {
        mon: mon,
        view_for: viewFor,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDiscountByServiceAdvisor = (queryMonth, callback) => {
  apolloClientPostgres
    .query({
      query: GET_DISCOUNT_BY_SERVICE_ADVISOR,
      variables: {
        month1: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getAddOnVsNonAddOnRevenuePercent = (queryMonth, callback) => {
  apolloClientPostgres
    .query({
      query: GET_ADD_ON_VS_NON_ADD_ON_REVENUE_PERCENT,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getAddOnServiceAdvisorDetails = (month1, month2, callback) => {
  var queryMonth = [month1, month2];
  apolloClientPostgres
    .query({
      query: GET_ADD_ON_SERVICE_ADVISOR_DETAILS,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getServiceAdvisorReport = callback => {
  apolloClientPostgres
    .mutate({
      mutation: GET_SERVICE_ADVISOR_DRILL_DOWN,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getServiceAdvisorRevenues = (viewFor, callback) => {
  apolloClientPostgres
    .mutate({
      mutation:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_SERVICE_ADVISOR_REVENUES
          : GET_SERVICE_ADVISOR_REVENUES_OLD,
      variables: {
        view_for: viewFor,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataTechnicianSummary = (
  queryMonth,
  techNo,
  callback
) => {
  if (techNo != '') {
    apolloClientPostgres
      .query({
        query: GET_DATA_FOR_TECHNICIAN_SUMMARY_BY_TECH_NO,
        variables: {
          month_year: queryMonth,
          lbrtechno: techNo,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  } else {
    apolloClientPostgres
      .query({
        query: GET_DATA_FOR_TECHNICIAN_SUMMARY,
        variables: {
          month_year: queryMonth,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  }
};

export const getDrillDownDataByTechNo = (techNo, queryMonth, callback) => {
  if (techNo != '') {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DATA_BY_TECH_NO,
        variables: {
          lbrtechno: techNo,
          month_year: queryMonth,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  } else {
    apolloClientPostgres
      .query({
        query: GET_DATA_FOR_TECHNICIAN_VIEW,
        variables: {
          month_year: queryMonth,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  }
};

export const getDrillDownDataServiceAdvisorSummary = (
  queryMonth,
  serviceAdvisor,
  callback
) => {
  if (serviceAdvisor != 'All') {
    apolloClientPostgres
      .mutate({
        mutation: GET_DATA_FOR_SERVICE_ADVISOR_SUMMARY_BY_SA,
        variables: {
          month_year: queryMonth && queryMonth.length == 13 ? null : queryMonth,
          serviceadvisor: serviceAdvisor,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  } else {
    apolloClientPostgres
      .mutate({
        mutation: GET_DATA_FOR_SERVICE_ADVISOR_SUMMARY,
        variables: {},
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  }
};

export const getDrillDownDataByServiceAdvisor = (
  serviceAdvisor,
  queryMonth,
  callback
) => {
  if (serviceAdvisor != 'All') {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DATA_BY_SERVICE_ADVISOR,
        variables: {
          serviceadvisor: serviceAdvisor == '' ? [] : serviceAdvisor,
          month_year: queryMonth,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  } else {
    apolloClientPostgres
      .query({
        query: GET_DATA_FOR_TECHNICIAN_VIEW,
        variables: {
          month_year: queryMonth,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  }
};

export const getDrillDownDataForDiscount = (queryMonth, callback) => {
  apolloClient
    .query({
      query: GET_DRILL_DOWN_DATA_FOR_DISCOUNT,
      variables: { month_year: queryMonth },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForAddOns = (monthYear, callback) => {
  apolloClientPostgres
    .query({
      query: GET_DRILL_DOWN_DATA_FOR_ADD_ON,
      variables: {
        month_year: monthYear,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getChartsDataFromViews = (advisors, chartId, callback) => {
  // if (chartId == 1072 || chartId == 967 || chartId == 1097) {
  //   const start = new Date();
  //   apolloClientPostgres
  //     .query({
  //       query: GET_CP_GROSS_PROFITPERCENT_940,
  //       variables: {
  //         advisor: advisors,
  //         month_year: getLast13Months(),
  //         store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
  //       },
  //       fetchPolicy: 'no-cache'
  //     })
  //     .then(result => {
  //       const spanAttribute = {
  //         pageUrl: '/CPOverview',
  //         origin: '',
  //         event: 'Menu Load',
  //         is_from: 'GET_CP_GROSS_PROFITPERCENT_940',
  //         value: new Date() - start,
  //provenance: localStorage.getItem('provenance')
  //       };
  //       traceSpan('Menu Load', spanAttribute);
  //       callback(
  //         result.data
  //           .statelessDbdCpOverviewGetGrossProfitPercentageServiceAdvisor.nodes
  //       );
  //     })
  //     .catch(error => {
  //       return error;
  //     });
  // }

  if (chartId == 946) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_ELR_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_ELR_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpOverviewGetElrAll
            .statelessDbdCpOverviewUxElrAlls
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 942) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_REVENUE_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_REVENUE_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpOverviewGetUxRevenue
            .statelessDbdCpOverviewUxRevenues
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 939) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_GP_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_GP_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpOverviewGetGrossProfit
            .statelessDbdCpOverviewUxGrossProfits
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 940) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_GP_PERCENTAGE_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_GP_PERCENTAGE_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpOverviewGetGrossProfitPercentageAll
            .statelessDbdCpOverviewUxGrossProfitPercentageAlls
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 920) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR_SOLD_HOURS_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR_SOLD_HOURS_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpOverviewGetLaborSoldHours
            .statelessDbdCpOverviewUxLaborSoldHours
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (
    chartId == 1225 ||
    chartId == 1226 ||
    chartId == 1227 ||
    chartId == 1228 ||
    chartId == 1229 ||
    chartId == 1230 ||
    chartId == 1231
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_PARTS_MARKUP_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_PARTS_MARKUP_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpPartsGetMarkupbyPaytypes.markupbyPaytypes
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (chartId == 1238) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_PARTS_MARKUP_DATA_1238,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_PARTS_MARKUP_DATA_1238',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpOverviewGetPartsMarkup
            .statelessDbdCpOverviewPartsMarkupAlls
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (chartId == 960) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR__REVENUE_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPLaborOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR__REVENUE_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpLaborGetRevenueByYear
            .statelessDbdCpLaborRevenueByYears
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 944) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR_GP_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPLaborOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR_GP_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpLaborGetLaborProfitByYear
            .statelessDbdCpLaborLaborProfitByYears
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1073) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR_GP_PERC_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPLaborOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR_GP_PERC_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpLaborGetGrossProfitPercentageServiceAdvisor
            .statelessDbdCpLaborGrossProfitPercentageServiceAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1133) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR_SOLDHOURS_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPLaborOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR_SOLDHOURS_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpLaborGetLaborSoldHoursByYear
            .statelessDbdCpLaborLaborSoldHoursByYears
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1127) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR_ELR_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPLaborOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR_ELR_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpLaborGetEffectiveLaborRateServiceAdvisor
            .statelessDbdCpLaborEffectiveLaborRateServiceAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1098) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR_ELR_REPAIR_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPLaborOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR_ELR_REPAIR_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpLaborGetElrRepairServiceAdvisor
            .statelessDbdCpLaborElrRepairServiceAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1356) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR_COMP_MAINT_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPLaborOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR_COMP_MAINT_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdCpLaborGetElrCompetitiveMaintenanceServiceAdvisor
            .statelessDbdCpLaborElrCompetitiveMaintenanceServiceAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1138) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR__RO_COUNT_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPLaborOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR__RO_COUNT_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpLaborGetRepairOrderCountByYear
            .statelessDbdCpLaborRepairOrderCountByYears
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (chartId == 955) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR__AVG_SALE_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPLaborOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR__AVG_SALE_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdCpLaborGetAverageRateByRepairOrderByYearServiceAdvisor
            .statelessDbdCpLaborAverageRateByRepairOrderByYearServiceAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 918) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR__JOB_COUNT_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPLaborOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR__JOB_COUNT_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpLaborGetRepairOrderCountCpByCategoryByYear
            .statelessDbdCpLaborRepairOrderCountCpByCategoryByYears
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1044) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR_HOURS_PER_RO_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPLaborOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR_HOURS_PER_RO_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdCpLaborGetHoursPerRepairOrderByYearServiceAdvisor
            .statelessDbdCpLaborHoursPerRepairOrderByYearServiceAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1234) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_DISCOUNT_LABOR_PARTS_DATA,

        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/Discounts',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_DISCOUNT_LABOR_PARTS_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdDiscountsGetLaborAndPartsDiscountByMonth
            .statelessCcPhysicalRwGetAllSchemaFunctionReturns
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (
    chartId == 1007 ||
    chartId == 1008 ||
    chartId == 1009 ||
    chartId == 1010 ||
    chartId == 1152 ||
    chartId == 1153 ||
    chartId == 1168
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR__DETAILS_GP_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR__DETAILS_GP_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpLaborGetDetailLaborProfitCombined
            .statelessDbdCpLaborDetailLaborProfitCombineds
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (
    chartId == 1134 ||
    chartId == 1135 ||
    chartId == 1136 ||
    chartId == 1137 ||
    chartId == 1154 ||
    chartId == 1155 ||
    chartId == 1169
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR__DETAILS_SOLD_HOURS_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR__DETAILS_SOLD_HOURS_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpLaborGetDetailLaborSoldHoursCombined
            .statelessDbdCpLaborDetailLaborSoldHoursCombineds
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (
    chartId == 1204 ||
    chartId == 1205 ||
    chartId == 1206 ||
    chartId == 1207 ||
    chartId == 1208 ||
    chartId == 1209 ||
    chartId == 1210
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR__DETAILS_JOB_COUNT_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR__DETAILS_JOB_COUNT_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpLaborGetDetailJobCountByPaytypes
            .statelessDbdCpLaborDetailJobCountByPaytypes
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (
    chartId == 1139 ||
    chartId == 1140 ||
    chartId == 1141 ||
    chartId == 1142 ||
    chartId == 1156 ||
    chartId == 1157 ||
    chartId == 1173
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR__DETAILS_RO_COUNT_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR__DETAILS_RO_COUNT_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpLaborGetDetailRepairOrderCountCombined
            .statelessDbdCpLaborDetailRepairOrderCountCombineds
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (
    chartId == 937 ||
    chartId == 988 ||
    chartId == 987 ||
    chartId == 986 ||
    chartId == 1066
  ) {
    const start = new Date();
    apolloClientPostgres
      .query({
        query: GET_CP_OVERVIEW_ELR_DETAILS_HASURA,
        variables: {
          advisor: advisors,
          mon_year: getLast13Months(),
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: ' GET_CP_OVERVIEW_ELR_DETAILS_HASURA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(result.data.dbdCpOverviewFnDetailElrServiceAdvisor.nodes);
      })
      .catch(error => {
        return error;
      });
  }

  // Parts
  if (chartId == 1049) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_PARTS_REVENUE_BY_SERVICE_ADVISOR,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPPartsOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_PARTS_REVENUE_BY_SERVICE_ADVISOR',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpPartsGetPartsRevenueByYear
            .partsRevenueByYears
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 966) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_PARTS_GROSSPROFIT_PERCENTAGE_BY_SERVICE_ADVISOR,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPPartsOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_PARTS_GROSSPROFIT_PERCENTAGE_BY_SERVICE_ADVISOR',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdCpPartsGetPartsGrossprofitPercentageServiceAdvisor
            .partsGrossprofitPercentageServiceAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 916) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_PARTS_MARKUP_BY_YEAR,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPPartsOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_PARTS_MARKUP_BY_YEAR',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpPartsGetPartsmarkupByYear.partsmarkupByYears
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (chartId == 1318) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_PARTS_HOURS_PER_RO_BY_YEAR,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPPartsOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_PARTS_HOURS_PER_RO_BY_YEAR',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpPartsGetHoursperRepairOrderByYear
            .hoursperRepairOrderByYears
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 952) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_PARTS_GROSS_PROFIT_BY_YEAR,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPPartsOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_PARTS_GROSS_PROFIT_BY_YEAR',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpPartsGetPartsProfitByYear.partsprofitByYears
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 953) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_PARTS_REVENUE_PER_RO_BY_YEAR,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPPartsOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_PARTS_REVENUE_PER_RO_BY_YEAR',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpPartsGetPartsRevenuePerRoByYear
            .partsrevenuePerRoByYears
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1143) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_PARTS_RO_COUNT_BY_YEAR,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPPartsOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_PARTS_RO_COUNT_BY_YEAR',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpPartsGetRepairorderCountByYear
            .repairorderCountByYears
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1326) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_PARTS__RO_COUNT_BY_YEAR_PARTS_ONLY,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPPartsOverview',
          origin: '',
          event: 'Menu Load',
          is_from: ' GET_PARTS__RO_COUNT_BY_YEAR_PARTS_ONLY',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpPartsGetPrtsrepairOrderCountByYear
            .prtsrepairOrderCountByYears
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1334) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_PARTS_MARKUP_REPAIR_AND_COMPETITIVE,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPPartsOverview',
          origin: '',
          event: 'Menu Load',
          is_from: ' GET_PARTS_MARKUP_REPAIR_AND_COMPETITIVE',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdCpPartsGetPartsmarkupRepairAndCompetitiveServiceAdvisor
            .partsmarkupRepairAndCompetitiveServiceAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (
    chartId == 1128 ||
    chartId == 1129 ||
    chartId == 1130 ||
    chartId == 1131 ||
    chartId == 1132
  ) {
    const start = new Date();
    apolloClientPostgres
      .query({
        query: GET_CP_LABOR_ELR_1127_details,
        variables: {
          advisor: advisors,
          mon_year: getLast13Months(),
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: '  GET_CP_LABOR_ELR_1127_details',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(result.data.dbdCpLaborFnDetailElrServiceAdvisor.nodes);
      })
      .catch(error => {
        return error;
      });
  }

  if (chartId == 949 || chartId == 1076 || chartId == 1077 || chartId == 1078) {
    const start = new Date();
    apolloClientPostgres
      .query({
        query: GET_CP_LABOR_RO_HOURS_DETAILS,
        variables: {
          advisor: advisors,
          month_year: getLast13Months(),
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: '  GET_CP_LABOR_RO_HOURS_DETAILS',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.dbdCpLaborFnDetailHoursPerRepairOrderServiceAdvisor.nodes
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (chartId == 1079) {
    const start = new Date();
    apolloClientPostgres
      .query({
        query: GET_CP_LABOR_1079_DETAILS,
        variables: {
          advisor: advisors,
          month_year: getLast13Months(),
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: '  GET_CP_LABOR_1079_DETAILS',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .dbdCpLaborFnDetailHoursRepairOrderCountPercentageServiceAdvisor
            .nodes
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (chartId == 1083) {
    const start = new Date();
    apolloClient
      .query({
        query: GET_CP_LABOR_1083_DETAILS,
        variables: {
          advisor: advisors,
          month_year: getLast13Months(),
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: ' GET_CP_LABOR_1083_DETAILS',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .dbd_cp_labor_fn_detail_repair_order_count_percentage_service_advisor
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (
    chartId == 1012 ||
    chartId == 1015 ||
    chartId == 1014 ||
    chartId == 1013
  ) {
    const start = new Date();
    apolloClient
      .query({
        query: GET_CP_PARTSRO_BYCATEGORY_DETAILS,
        variables: { advisor: advisors, month_year: getLast13Months() },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: ' GET_CP_PARTSRO_BYCATEGORY_DETAILS',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .dbd_cp_parts_fn_detail_revenue_per_ro_by_category_service_advisor
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (
    chartId == 1091 ||
    chartId == 1092 ||
    chartId == 1093 ||
    chartId == 1094
  ) {
    const start = new Date();
    apolloClient
      .query({
        query: GET_CP_PARTMARKUP_DETAILS,
        variables: { advisor: advisors, month_year: getLast13Months() },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_PARTMARKUP_DETAILS',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.dbd_cp_parts_fn_detail_parts_markup_service_advisor
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (
    chartId == 1084 ||
    chartId == 1085 ||
    chartId == 1086 ||
    chartId == 1087 ||
    chartId == 1088
  ) {
    const start = new Date();
    apolloClientPostgres
      .query({
        query: GET_CP_LABORSALE_PER_RO_DETAILS,
        variables: {
          advisor: advisors,
          month_year: getLast13Months(),
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABORSALE_PER_RO_DETAILS',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.dbdCpLaborFnDetailAverageRateByRoCategoryServiceAdvisor
            .nodes
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (chartId == 1174) {
    const start = new Date();
    apolloClientPostgres
      .query({
        query: GET_SPECIALMETRICS_1174,
        variables: {
          advisor: advisors,
          month_year: getLast13Months(),
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/SpecialMetrics',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_SPECIALMETRICS_1174',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.dbdSpecialMetricsFnAverageSalePerTechnicianServiceAdvisor
            .nodes
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1175) {
    const start = new Date();
    apolloClientPostgres
      .query({
        query: GET_SPECIALMETRICS_1175,
        variables: {
          advisor: advisors,
          month_year: getLast13Months(),
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/SpecialMetrics',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_SPECIALMETRICS_1175',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.dbdSpecialMetricsFnAverageSalePerTechnicianServiceAdvisor
            .nodes
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (
    chartId == 1176 ||
    chartId == 1177 ||
    chartId == 1178 ||
    chartId == 1179 ||
    chartId == 1180 ||
    chartId == 1181 ||
    chartId == 1182
  ) {
    const start = new Date();
    apolloClientPostgres
      .query({
        query: GET_CP_OVERVIEW_ELR_PAYTYPE_DETAILS_HASURA,
        variables: {
          advisor: advisors,
          mon_year: getLast13Months(),
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_OVERVIEW_ELR_PAYTYPE_DETAILS_HASURA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpOverviewGetDetailElrPaytypeServiceAdvisor
            .nodes
        );
      })
      .catch(error => {
        return error;
      });
  }
  // if (
  //   chartId == 1183 ||
  //   chartId == 1184 ||
  //   chartId == 1185 ||
  //   chartId == 1186 ||
  //   chartId == 1187 ||
  //   chartId == 1188 ||
  //   chartId == 1189
  // ) {
  //   const start = new Date();
  //   apolloClientPostgres
  //     .query({
  //       query: GET_CP_LABOR_ELR_1127_PAYTYPE_DETAILS,
  //       variables: {
  //         advisor: advisors,
  //         mon_year: getLast13Months(),
  //         store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
  //       },
  //       fetchPolicy: 'no-cache'
  //     })
  //     .then(result => {
  //       const spanAttribute = {
  //         pageUrl: '/GraphDetailsView',
  //         origin: '',
  //         event: 'Menu Load',
  //         is_from: 'GET_CP_LABOR_ELR_1127_PAYTYPE_DETAILS',
  //         value: new Date() - start,
  provenance: localStorage.getItem('provenance');
  //       };
  //       traceSpan('Menu Load', spanAttribute);
  //       callback(
  //         result.data.statelessDbdCpLaborGetDetailElrPaytypeServiceAdvisor.nodes
  //       );
  //     })
  //     .catch(error => {
  //       return error;
  //     });
  // }

  if (
    chartId == 1197 ||
    chartId == 1198 ||
    chartId == 1199 ||
    chartId == 1200 ||
    chartId == 1201 ||
    chartId == 1202 ||
    chartId == 1203
  ) {
    const start = new Date();
    apolloClientPostgres
      .query({
        query: GET_CP_LABOR_RO_HOURS_PAYTYPE_DETAILS,
        variables: {
          advisor: advisors,
          month_year: getLast13Months(),
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR_RO_HOURS_PAYTYPE_DETAILS',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdCpLaborGetDetailHoursPerRoByPaytypesServiceAdvisor
            .nodes
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (
    chartId == 1319 ||
    chartId == 1320 ||
    chartId == 1321 ||
    chartId == 1322 ||
    chartId == 1323 ||
    chartId == 1324 ||
    chartId == 1325
  ) {
    const start = new Date();
    apolloClientPostgres
      .query({
        query: GET_CP_PARTS_RO_HOURS_PAYTYPE_DETAILS,
        variables: {
          advisor: advisors,
          month_year: getLast13Months(),
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_PARTS_RO_HOURS_PAYTYPE_DETAILS',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdCpPartsGetDetailHoursPerRepairOrderByPaytypesServiceAdvisor
            .nodes
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (
    chartId == 1211 ||
    chartId == 1212 ||
    chartId == 1213 ||
    chartId == 1214 ||
    chartId == 1215 ||
    chartId == 1216 ||
    chartId == 1217
  ) {
    const start = new Date();
    apolloClientPostgres
      .query({
        query: GET_CP_LABORSALE_PER_RO_PAYTYPE_DETAILS,
        variables: {
          advisor: advisors,
          month_year: getLast13Months(),
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABORSALE_PER_RO_PAYTYPE_DETAILS',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdCpLaborGetDetailAverageSaleByPaytypesServiceAdvisor
            .nodes
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (
    chartId == 1218 ||
    chartId == 1219 ||
    chartId == 1220 ||
    chartId == 1221 ||
    chartId == 1222 ||
    chartId == 1223 ||
    chartId == 1224
  ) {
    const start = new Date();
    apolloClientPostgres
      .query({
        query: GET_CP_PARTSRO_BY_PAYTYPE_DETAILS,
        variables: {
          advisor: advisors,
          month_year: getLast13Months(),
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_PARTSRO_BY_PAYTYPE_DETAILS',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdCpPartsGetPartsRevenuePerRoCountByPaytypeServiceAdvisor
            .nodes
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (chartId == 931) {
    if (advisors.includes('All')) {
      const start = new Date();
      apolloClientPostgres
        .query({
          query: GET_OPPORTUNITY_LABOR_TOTAL_LABOR_OPPORTUNITY,
          variables: {
            month_year: getLast13Months('opportunity'),
            store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/LaborGrossAndVolumeOpportunity',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_OPPORTUNITY_LABOR_TOTAL_LABOR_OPPORTUNITY',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data
              .statelessDbdCpOpportunityLaborGetTotalLaborOpportunityCharts
              .nodes
          );
        })
        .catch(error => {
          return error;
        });
    } else {
      const start = new Date();
      apolloClientPostgres
        .query({
          query: GET_TOTAL_LABOR_OPPORTUNITY,
          variables: {
            advisor: advisors,
            month_year: getLast13Months('opportunity'),
            store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/LaborGrossAndVolumeOpportunity',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_TOTAL_LABOR_OPPORTUNITY',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data
              .statelessDbdCpOpportunityLaborGetChartsTotalLaborOpportunityServiceadvisor
              .nodes
          );
        })
        .catch(error => {
          return error;
        });
    }
  }
  if (chartId == 926) {
    if (advisors.includes('All')) {
      const start = new Date();
      apolloClientPostgres
        .query({
          query: GET_OPPORTUNITY_PARTS_TOTAL_PARTS_OPPORTUNITY,
          variables: {
            month_year: getLast13Months('opportunity'),
            store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/PartsGrossAndVolumeOpportunity',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_OPPORTUNITY_PARTS_TOTAL_PARTS_OPPORTUNITY',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data
              .statelessDbdCpOpportunityPartsGetTotalPartsOpportunityCharts
              .nodes
          );
        })
        .catch(error => {
          return error;
        });
    } else {
      const start = new Date();
      apolloClientPostgres
        .query({
          query: GET_TOTAL_PARTS_OPPORTUNITY,
          variables: {
            advisor: advisors,
            month_year: getLast13Months('opportunity'),
            store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/PartsGrossAndVolumeOpportunity',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_TOTAL_PARTS_OPPORTUNITY',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data
              .statelessDbdCpOpportunityPartsGetChartsTotalPartsOpportunityServiceadvisor
              .nodes
          );
        })
        .catch(error => {
          return error;
        });
    }
  }
  if (chartId == 921) {
    if (advisors.includes('All')) {
      const start = new Date();
      apolloClientPostgres
        .query({
          query: GET_ELR_TOTAL_PRICING_OPPORTUNITY,
          variables: {
            month_year: getLast13Months('elr_opportunity'),
            store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/CPELROpportunity',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_ELR_TOTAL_PRICING_OPPORTUNITY',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data
              .statelessDbdCpOpportunityElrGetChartsPricingElrOpportunityByCategory
              .nodes
          );
        })
        .catch(error => {
          return error;
        });
    } else {
      const start = new Date();
      apolloClientPostgres
        .query({
          query: GET_TOTAL_ELR_OPPORTUNITY,
          variables: {
            advisor: advisors,
            month_year: getLast13Months('elr_opportunity'),
            store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/CPELROpportunity',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_TOTAL_ELR_OPPORTUNITY',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data
              .statelessDbdCpOpportunityElrGetTotalPricingOpportunityByServiceadvisor
              .nodes
          );
        })
        .catch(error => {
          return error;
        });
    }
  }

  if (
    chartId == 968 ||
    chartId == 971 ||
    chartId == 969 ||
    chartId == 970 ||
    chartId == 1148 ||
    chartId == 1149 ||
    chartId == 1166
  ) {
    const start = new Date();
    apolloClientPostgres
      .query({
        query: GET_SOLD_HOURS_BY_PAY_TYPE,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_SOLD_HOURS_BY_PAY_TYPE',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpOverviewGetDetailLaborSoldHours
            .statelessDbdCpOverviewDetailLaborSoldHours
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (
    chartId == 1176 ||
    chartId == 1177 ||
    chartId == 1178 ||
    chartId == 1179 ||
    chartId == 1180 ||
    chartId == 1181 ||
    chartId == 1182
  ) {
    const start = new Date();
    apolloClientPostgres
      .query({
        query: GET_ELR_BY_PAY_TYPE,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_ELR_BY_PAY_TYPE',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpOverviewGetDetailElrByPaytype
            .statelessDbdCpOverviewDetailElrByPaytypes
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (
    chartId == 1190 ||
    chartId == 1191 ||
    chartId == 1192 ||
    chartId == 1193 ||
    chartId == 1194 ||
    chartId == 1195 ||
    chartId == 1196
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR__DETAILS_REVENUE_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: ' GET_CP_LABOR__DETAILS_REVENUE_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpLaborGetDetailRevenueByPaytypes
            .statelessDbdCpLaborDetailRevenueByPaytypes
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (
    chartId == 1183 ||
    chartId == 1184 ||
    chartId == 1185 ||
    chartId == 1186 ||
    chartId == 1187 ||
    chartId == 1188 ||
    chartId == 1189
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR__DETAILS_ELR_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_LABOR__DETAILS_ELR_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpLaborGetDetailElrByPaytype
            .statelessDbdCpLaborDetailElrByPaytypes
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (
    chartId == 1197 ||
    chartId == 1198 ||
    chartId == 1199 ||
    chartId == 1200 ||
    chartId == 1201 ||
    chartId == 1202 ||
    chartId == 1203
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR_DETAILS_HOURS_PER_RO_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: ' GET_CP_LABOR_DETAILS_HOURS_PER_RO_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpLaborGetDetailHoursPerRepairOrderByPaytypes
            .statelessDbdCpLaborDetailHoursPerRepairOrderByPaytypes
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (
    chartId == 1211 ||
    chartId == 1212 ||
    chartId == 1213 ||
    chartId == 1214 ||
    chartId == 1215 ||
    chartId == 1216 ||
    chartId == 1217
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_LABOR_DETAILS_AVG_SALE_RO_COUNT_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/GraphDetailsView',
          origin: '',
          event: 'Menu Load',
          is_from: ' GET_CP_LABOR_DETAILS_AVG_SALE_RO_COUNT_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpLaborGetDetailAverageSaleRoCountByPaytypes
            .statelessDbdCpLaborDetailAverageSaleRoCountByPaytypes
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (
    chartId == 1001 ||
    chartId == 1002 ||
    chartId == 457 ||
    chartId == 456 ||
    chartId == 1158 ||
    chartId == 1159 ||
    chartId == 1172
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_PARTS_DETAILS_REVENUE,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPPartsOverview',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_CP_PARTS_DETAILS_REVENUE',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpPartsGetDetailPartsrevenueByCategory
            .detailPartsrevenueByCategories
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (
    chartId == 1028 ||
    chartId == 1029 ||
    chartId == 1030 ||
    chartId == 1031 ||
    chartId == 1160 ||
    chartId == 1161 ||
    chartId == 1171
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_PARTS_DETAILS_GP,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPPartsOverview',
          origin: '',
          event: 'Menu Load',
          is_from: ' GET_CP_PARTS_DETAILS_GP',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpPartsGetDetailPartsProfitByCategory
            .detailpartsProfitByCategories
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (
    chartId == 1218 ||
    chartId == 1219 ||
    chartId == 1220 ||
    chartId == 1221 ||
    chartId == 1222 ||
    chartId == 1223 ||
    chartId == 1224
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_PARTS_DETAILS_REVENUE_PER_RO_COUNT,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPPartsOverview',
          origin: '',
          event: 'Menu Load',
          is_from: ' GET_CP_PARTS_DETAILS_REVENUE_PER_RO_COUNT',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdCpPartsGetDetailPartsRevenuePerRoCountByPaytypes
            .detailpartsRevenuePerRoCountByPaytypes
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (
    chartId == 1144 ||
    chartId == 1145 ||
    chartId == 1146 ||
    chartId == 1147 ||
    chartId == 1162 ||
    chartId == 1163 ||
    chartId == 1170
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_PARTS_DETAILS_RO_COUNT,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPPartsOverview',
          origin: '',
          event: 'Menu Load',
          is_from: ' GET_CP_PARTS_DETAILS_RO_COUNT',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpPartsGetDetailPartsRepairOrderCountCombined
            .detailpartsRepairOrderCountCombineds
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (
    chartId == 1319 ||
    chartId == 1320 ||
    chartId == 1321 ||
    chartId == 1322 ||
    chartId == 1323 ||
    chartId == 1324 ||
    chartId == 1325
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_PARTS_DETAILS_HOURS_PER_RO_PARTS_ONLY,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/CPPartsOverview',
          origin: '',
          event: 'Menu Load',
          is_from: ' GET_CP_PARTS_DETAILS_HOURS_PER_RO_PARTS_ONLY',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdCpPartsGetDetailhoursPerRepairOrderByPaytypes
            .detailhoursPerRepairOrderByPaytypes
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (
    chartId == 1327 ||
    chartId == 1328 ||
    chartId == 1329 ||
    chartId == 1330 ||
    chartId == 1331 ||
    chartId == 1332 ||
    chartId == 1333
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_CP_PARTS_DETAILS_RO_COUNT_PARTS_ONLY,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/Discounts',
          origin: '',
          event: 'Menu Load',
          is_from: ' GET_CP_PARTS_DETAILS_RO_COUNT_PARTS_ONLY',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdCpPartsGetDetailpartsOnlyRepairOrderCountCombined
            .detailpartsOnlyRepairOrderCountCombineds
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1113) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_DISCOUNTS_BY_DISC_LEVEL,
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/Discounts',
          origin: '',
          event: 'Menu Load',
          is_from: '  GET_DISCOUNTS_BY_DISC_LEVEL',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdDiscountsGetDiscountsByDiscountlevelDetails
            .statelessCcPhysicalRwGetAllSchemaFunctionReturns
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1123) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_DISCOUNTED_RO_PERCENT,
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/Discounts',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_DISCOUNTED_RO_PERCENT',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdDiscountsGetDiscountRoPercentageDetails
            .statelessCcPhysicalRwGetAllSchemaFunctionReturns
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1115 || chartId == 1235) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_PERCENT_DISCOUNTS_1115,
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/Discounts',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_PERCENT_DISCOUNTS_1115',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdDiscountsGetDiscountedSalePercentageDetails
            .statelessCcPhysicalRwGetAllSchemaFunctionReturns
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1232 || chartId == 1233) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_TOTAL_DISCOUNTS_1232,
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/Discounts',
          origin: '',
          event: 'Menu Load',
          is_from: ' GET_TOTAL_DISCOUNTS_1232',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdDiscountsGetDiscountPercDiscountedPerTotalCpDiscounts
            .statelessCcPhysicalRwGetAllSchemaFunctionReturns
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1236 || chartId == 1165 || chartId == 1237) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_TOTAL_DISCOUNTS_1236,
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/Discounts',
          origin: '',
          event: 'Menu Load',
          is_from: ' GET_TOTAL_DISCOUNTS_1236',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdDiscountsGetDiscountsPerRoDetails
            .statelessCcPhysicalRwGetAllSchemaFunctionReturns
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (chartId == 1357) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET__SPECIALMETRICS_AVERAGE_OPEN_DATE_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/SpecialMetrics',
          origin: '',
          event: 'Menu Load',
          is_from: ' GET__SPECIALMETRICS_AVERAGE_OPEN_DATE_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdSpecialMetricsGetAverageDaysOpenByPaytypesServiceAdvisor
            .averageDaysOpenByPaytypes
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 923) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET__SPECIALMETRICS_SINGLE_LINE_RO_COUNT_PERC_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/SpecialMetrics',
          origin: '',
          event: 'Menu Load',
          is_from: '  GET__SPECIALMETRICS_SINGLE_LINE_RO_COUNT_PERC_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdSpecialMetricsGetSingleJobRoCountPercentageServiceAdvisor
            .singleJobRoCountPercentageServiceAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1355) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET__SPECIALMETRICS_MULTI_LINE_RO_COUNT_PERC_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/SpecialMetrics',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET__SPECIALMETRICS_MULTI_LINE_RO_COUNT_PERC_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdSpecialMetricsGetMultiJobRoCountPercentageServiceAdvisor
            .multiJobRoCountPercentageServiceAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 930) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET__SPECIALMETRICS_PARTS_TO_LABOR_RATIO_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/SpecialMetrics',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET__SPECIALMETRICS_PARTS_TO_LABOR_RATIO_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdSpecialMetricsGetPartsToLaborRatioServiceAdvisor
            .partsToLaborRatioServiceAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (chartId == 935) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET__SPECIALMETRICS_LABOR_SOLD_HOURS_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/SpecialMetrics',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET__SPECIALMETRICS_LABOR_SOLD_HOURS_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdSpecialMetricsGetLaborSoldHoursByPaytypeServiceAdvisor
            .laborSoldHoursByPaytypeServiceAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (chartId == 936) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET__SPECIALMETRICS_PARTS_TO_TABOR_RATIO_BY_CATEGORY_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/SpecialMetrics',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET__SPECIALMETRICS_PARTS_TO_TABOR_RATIO_BY_CATEGORY_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdSpecialMetricsGetPartsToLaborRatioByCategoryServiceAdvisor
            .partsToLaborRatioByCategoryServiceAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (chartId == 1316) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET__SPECIALMETRICS_MPI_PENETRATION_PERC_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/SpecialMetrics',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET__SPECIALMETRICS_MPI_PENETRATION_PERC_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdSpecialMetricsGetMpiPenetrationPercentageServiceAdvisor
            .mpiPenetrationPercentageServiceAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1317) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET__SPECIALMETRICS_MENU_PENETRATION_PERC_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/SpecialMetrics',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET__SPECIALMETRICS_MENU_PENETRATION_PERC_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdSpecialMetricsGetMenuPenetrationPercentageServiceAdvisor
            .menuPenetrationPercentageServiceAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (
    chartId == 1239 ||
    chartId == 1359 ||
    chartId == 1360 ||
    chartId == 1361 ||
    chartId == 1362
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET__SPECIALMETRICS_SHOP_SUPPLIES_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/SpecialMetrics',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET__SPECIALMETRICS_SHOP_SUPPLIES_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .sstatelessDbdSpecialMetricsGetTotalShopsuppliesDetailsCombined
            .statelessCcPhysicalRwGetAllSchemaFunctionReturns
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 1354) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET__SPECIALMETRICS_MULTI_JOB_RO_COUNTS_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/SpecialMetrics',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET__SPECIALMETRICS_MULTI_JOB_RO_COUNTS_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdSpecialMetricsGetMultiJobRoCount
            .multijobRoCounts
        );
      })
      .catch(error => {
        return error;
      });
  }

  if (chartId == 948) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET__SPECIALMETRICS_SINGLE_JOB_RO_COUNTS_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/SpecialMetrics',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET__SPECIALMETRICS_SINGLE_JOB_RO_COUNTS_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdSpecialMetricsGetSingleJobRoCount
            .singlejobRoCounts
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (chartId == 938) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET__SPECIALMETRICS_RETURN_RATE_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/SpecialMetrics',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET__SPECIALMETRICS_RETURN_RATE_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data.statelessDbdSpecialMetricsGetReturnrateByServiceAdvisor
            .returnRateByServiceAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (
    chartId == 1243 ||
    chartId == 1244 ||
    chartId == 1245 ||
    chartId == 1246 ||
    chartId == 1247 ||
    chartId == 1248
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_LABOR_WORKMIX_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/LaborWorkMixAnalysis',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_LABOR_WORKMIX_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdLaborWorkmixsGetLbrWorkmixPercentageByOpcategoryByAdvisor
            .statelessDbdLaborWorkmixLbrWorkmixPercentageByOpcategoryByAdvisors
        );
      })
      .catch(error => {
        return error;
      });
  }
  if (
    chartId == 1253 ||
    chartId == 1254 ||
    chartId == 1255 ||
    chartId == 1256 ||
    chartId == 1257 ||
    chartId == 1258
  ) {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_PARTS_WORKMIX_DATA,
        variables: {
          advisor: advisors
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/PartsWorkMixAnalysis',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_PARTS_WORKMIX_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(
          result.data
            .statelessDbdPartsWorkmixsGetPartsWorkmixPercentageByOpcategoryByAdvisor
            .statelessCcPhysicalRwGetAllSchemaFunctionReturns
        );
      })
      .catch(error => {
        return error;
      });
  }
};

export const getAllSADetails = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_ALL_SERVICE_ADVISOR_DETAILS,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/ServiceAdvisors',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_ALL_SERVICE_ADVISOR_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getLastThirteenMonths = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_LAST_THIRTEEN_MONTHS,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/LaborWorkMixAnalysis',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_LAST_THIRTEEN_MONTHS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      let thirteenMonths = [];
      result.data.statelessCcAggregateGetLastThirteenMonths.nodes.map(val => {
        thirteenMonths.push(val.monthYear);
      });

      callback(thirteenMonths);
      // callback(result.data.dmsAggregateVwLastThirteenMonths.nodes);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDiscountROPercByServiceAdvisor = (
  month1,
  month2,
  ViewFor,
  callback
) => {
  apolloClientPostgres
    .query({
      query: GET_DISCOUNT_RO_PERC_BY_SERVICE_ADVISOR,
      variables: {
        mon1: month1,
        mon2: month2,
        view_for: ViewFor,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDiscountByServiceAdvisorOpcategory = (
  month1,
  month2,
  callback
) => {
  apolloClientPostgres
    .mutate({
      mutation: GET_DISCOUNT_BY_SERVICE_ADVISOR_OPCATEGORY,
      variables: {
        month1: month1,
        month2: month2,
        storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForMasterDiscountJobs = (monthYear, callback) => {
  apolloClient
    .query({
      query: GET_DRILL_DOWN_DATA_FOR_MASTER_DISCOUNT_JOBS,
      variables: { month_year: monthYear },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDrillDownDataForDiscountJobCount = (queryMonth, callback) => {
  apolloClientPostgres
    .query({
      query: GET_DRILL_DOWN_DATA_FOR_DISCOUNT_JOB_COUNT,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForDiscountedRo = callback => {
  apolloClient
    .query({
      query: GET_DRILL_DOWN_DATA_FOR_DISCOUNTED_JOBS_PER_RO,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getClientDetails = (client_id, callback) => {
  apolloClientPostgres
    .query({
      query: GET_CLIENT_DETAILS,
      variables: { client_id: client_id },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDrillDownDataForDiscountSummary = (
  queryMonth,
  serviceAdvisor,
  callback
) => {
  if (serviceAdvisor == 'All') {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DATA_FOR_DISCOUNT_SUMMARY,
        variables: {
          month_year: queryMonth,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  } else {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DATA_FOR_DISCOUNT_SUMMARY_WITH_SA,
        variables: {
          month_year: queryMonth,
          serviceadvisor: serviceAdvisor,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  }
};

export const getDrillDownDataForDiscountSummaryParts = (
  queryMonth,
  serviceAdvisor,
  callback
) => {
  if (serviceAdvisor == 'All') {
    apolloClientPostgres
      .mutate({
        mutation: GET_DRILL_DOWN_DATA_FOR_DISCOUNT_SUMMARY_PARTS,
        variables: {
          month_year: queryMonth,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  } else {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DATA_FOR_DISCOUNT_SUMMARY_WITH_SA_PARTS,
        variables: {
          month_year: queryMonth,
          serviceadvisor: serviceAdvisor,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  }
};

export const getDrillDownDiscountSummaryLabor = (
  queryMonth,
  discountId,
  serviceAdvisor,
  callback
) => {
  if (serviceAdvisor == 'All') {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DISCOUNT_SUMMARY_LABOR,
        variables: {
          month_year: queryMonth,
          disdiscountid: discountId,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  } else {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DISCOUNT_SUMMARY_LABOR_SA,
        variables: {
          month_year: queryMonth,
          disdiscountid: discountId,
          serviceadvisor: serviceAdvisor,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  }
};

export const getDrillDownDiscountSummaryParts = (
  queryMonth,
  discountId,
  serviceAdvisor,
  callback
) => {
  if (serviceAdvisor == 'All') {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DISCOUNT_SUMMARY_PARTS,
        variables: {
          month_year: queryMonth,
          disdiscountid: discountId,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  } else {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DISCOUNT_SUMMARY_PARTS_SA,
        variables: {
          month_year: queryMonth,
          disdiscountid: discountId,
          serviceadvisor: serviceAdvisor,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  }
};
export const getDbdDetails = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_DASHBOARD_DETAILS
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/ChartMaster',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DASHBOARD_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getViewDetails = callback => {
  const start = new Date();
  apolloClient
    .query({
      query: GET_VIEW_DETAILS
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/ChartMaster',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_VIEW_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForLaborDiscount = (queryMonth, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_DRILL_DOWN_DATA_LABOR_DISCOUNT,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Discounts',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DRILL_DOWN_DATA_LABOR_DISCOUNT',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForLaborPartsDiscount = (queryMonth, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_DRILL_DOWN_DATA_LABOR_PARTS_DISCOUNT,
      variables: {
        month_year: queryMonth,
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Discounts',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DRILL_DOWN_DATA_LABOR_PARTS_DISCOUNT',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDrillDownDataForPartsDiscount = (queryMonth, callback) => {
  apolloClientPostgres
    .query({
      query: GET_DRILL_DOWN_DATA_PARTS_DISCOUNT,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getOpcodeDetails = callback => {
  const userID = localStorage.getItem('userID');
  const start = new Date();
  apolloClientPostgres
    .query({
      query:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_OPCODE_DETAILS
          : GET_OPCODE_DETAILS_OLD,
      variables: {
        user_id: localStorage.getItem('userID')
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      //console.log('startttt==', start, new Date(), new Date() - start);
      const spanAttribute = {
        pageUrl: '/Opcodes',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_OPCODE_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getNonCPOpcodeDetails = callback => {
  const userID = localStorage.getItem('userID');
  const start = new Date();
  apolloClientPostgres
    .query({
      query:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_NONCPOPCODE_DETAILS
          : GET_OPCODE_DETAILS_OLD,
      variables: {
        user_id: localStorage.getItem('userID')
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      //console.log('startttt==', start, new Date(), new Date() - start);
      const spanAttribute = {
        pageUrl: '/NonCPOpcodes',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_NONCPOPCODE_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDiscountByRO = (queryMonth, callback) => {
  apolloClientPostgres
    .query({
      query: GET_DRILL_DOWN_DISCOUNT_BY_RO,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForReturnRateSA = (
  queryMonth,
  serviceAdvisor,
  callback
) => {
  apolloClientPostgres
    .query({
      query: GET_DRILL_DOWN_DATA_FOR_RETURN_RATE_SA,
      variables: { month_year: queryMonth, serviceadvisor: serviceAdvisor },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getWorkMixReportForTechEfficiency = (viewFor, callback) => {
  apolloClientPostgres
    .query({
      query:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_WORKMIX_REPORT_TECH_EFFICIENCY
          : GET_WORKMIX_REPORT_TECH_EFFICIENCY_OLD,
      variables: {
        view_for: viewFor,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getWorkMixReportTotalVolumeParts = (tblFor, viewFor, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_WORKMIX_REPORT_TOTAL_VOLUME_PARTS
          : GET_WORKMIX_REPORT_TOTAL_VOLUME_PARTS_OLD,
      variables: {
        table_for: tblFor,
        view_for: viewFor,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/WorkMixVolumeParts',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_WORKMIX_REPORT_TOTAL_VOLUME_PARTS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDrillDownDataForTechnician = (queryMonth, techNo, callback) => {
  if (techNo != '' && techNo.includes('All') === false) {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DATA_FOR_TECHNICIAN_BY_TECH_NO,
        variables: {
          month_year: queryMonth,
          lbrtechno: techNo,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  } else {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DATA_FOR_TECHNICIAN,
        variables: {
          month_year: queryMonth,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  }
};
export const getDrillDownDataForTechnicianWeekly = (
  queryMonth,
  techNo,
  callback
) => {
  if (techNo != '' && techNo.includes('All') === false) {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DATA_FOR_TECHNICIAN_BY_TECH_NO,
        variables: {
          month_year: queryMonth,
          lbrtechno: techNo,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  } else {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DATA_FOR_TECHNICIAN,
        variables: {
          month_year: queryMonth,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  }
};

export const removeAllChartsFromFavourite = (username, callback) => {
  apolloClientPostgresWrite
    .mutate({
      mutation: DELETE_ALL_FAVOURITE_CHARTS,
      variables: { username: username },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result.data.delete__physical_rw_tbl_chart_favourites);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getAllFavouriteCharts = (username, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_ALL_FAVOURITE_CHARTS,
      fetchPolicy: 'no-cache',
      variables: {
        username: username,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      }
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/MyFavorites',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_ALL_FAVOURITE_CHARTS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result.data.statefulCcPhysicalRwChartFavourites.nodes);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const addChartToFavourite = (chartID, username, callback) => {
  apolloClientPostgresWrite
    .mutate({
      mutation: ADD_CHART_TO_FAVOURITE,
      variables: {
        username: username,
        favourites: chartID,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result.data.statelessCcPhysicalRwAddChartToFavorites);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const removeChartFromFavourite = (chartID, username, callback) => {
  apolloClientPostgresWrite
    .mutate({
      mutation: REMOVE_CHART_FROM_FAVOURITE,
      variables: { username: username, favourites: chartID },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result.data.statelessCcPhysicalRwRemoveChartFromFavorites);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getAllPayTypeErrors = callback => {
  apolloClientPostgres
    .query({
      query: GET_PAY_TYPE_ERRORS,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      //callback(result.data.statefulCcPhysicalRwPayTypeMasters.nodes);
      callback(result.data.statelessCcPhysicalRwPayTypeCodeErrors.nodes);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getAllOpcodeErrors = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_OPCODE_ERRORS,

      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/OPcodes',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_OPCODE_ERRORS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(
        result.data.statelessCcPhysicalRwGetOpcodeCodeErrors
          .statelessCcPhysicalRwOpcodeCodeErrors
      );
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDrillDownCalculationDataSASummary = (
  queryMonth,
  serviceAdvisor,
  callback
) => {
  apolloClientPostgres
    .query({
      query: GET_DRILL_DOWN_CALCULATION_DATA_SA_SUMMARY,
      variables: {
        month_year: queryMonth,
        serviceadvisor: serviceAdvisor,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDrillDownForAverageHoursSoldPerTech = (
  queryMonth,
  serviceAdvisor,
  callback
) => {
  if (serviceAdvisor == 'All') {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_FOR_AVERAGE_HOURS_SOLD_PER_TECH,
        variables: {
          month_year: queryMonth,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  } else {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_FOR_AVERAGE_HOURS_SOLD_PER_TECH_SA,
        variables: {
          month_year: queryMonth,
          advisor: serviceAdvisor,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  }
};
export const getAverageHoursSoldPerTechData = (queryMonth, callback) => {
  apolloClient
    .query({
      query: GET_AVERAGE_HOURS_SOLD_PER_TECH_DATA,
      variables: { month_year: queryMonth },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getLaborPartsDiscountByMonth = (queryMonth, callback) => {
  queryMonth = getLast13Months();
  apolloClientPostgres
    .query({
      query: GET_LABOR_PARTS_DISCOUNT_BY_MONTH,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDiscountedSalePercentage = (queryMonth, callback) => {
  queryMonth = getLast13Months();
  apolloClientPostgres
    .query({
      query: GET_DISCOUNTED_SALE_PERECENTAGE,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDiscountsPerCPROs = (queryMonth, callback) => {
  queryMonth = getLast13Months();
  apolloClientPostgres
    .query({
      query: GET_DISCOUNTS_PER_TOTAL_CP_ROS,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDiscountsPerTotalDiscountedCPROs = (queryMonth, callback) => {
  queryMonth = getLast13Months();
  apolloClientPostgres
    .query({
      query: GET_DISCOUNTS_PER_TOTAL_DISCOUNTED_CP_ROS,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDiscountedTotalPerTotalDiscountedCPROs = (
  queryMonth,
  callback
) => {
  queryMonth = getLast13Months();
  apolloClientPostgres
    .query({
      query: GET_DISCOUNTED_CP_PER_TOTAL_DISCOUNTED_CP_ROS,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getTechnicianRevenue = (mon1, mon2, viewFor, callback) => {
  apolloClientPostgres
    .mutate({
      mutation: GET_TECHNICIAN_REVENUE_AND_HOURS,
      variables: {
        mon1: mon1,
        mon2: mon2,
        view_for: viewFor,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getMostFrequentTechnician = callback => {
  apolloClientPostgres
    .query({
      query: GET_MOST_FREQUENT_TECHNICIAN,
      variables: {
        store: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getStoreDetails = (store_id, callback) => {
  const start = new Date();
  makeApolloClientPostgres
    .query({
      query: GET_STORE_DETAILS,
      variables: { store_id: store_id },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Home',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_STORE_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getShopSuppliesDrilldown = (queryMonth, callback) => {
  apolloClientPostgres
    .query({
      query: GET_SHOP_SUPPLIES_DRILLDOWN,
      variables: { month_year: queryMonth },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getStoreNamesByStoreId = (store_ids, callback) => {
  apolloClientPostgres
    .query({
      query: GET_STORE_NAMES_BY_STORE_ID,
      variables: { storeId: store_ids },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      let store = [];
      result.data.statelessCcPhysicalRoGetStoreMasters.nodes.map((val, i) => {
        store.push(
          result.data.statelessCcPhysicalRoGetStoreMasters.nodes[i].storeName
        );
      });

      callback(store);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getAllStoreNames = (group_ids, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: ALL_STORE_NAMES,
      variables: { store_id: group_ids },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      let store = [];

      const spanAttribute = {
        pageUrl: '/Home',
        origin: '',
        event: 'Menu Load',
        is_from: 'ALL_STORE_NAMES',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result.data.statelessCcPhysicalRoGetStoreMasterDetails.nodes);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDrillDownDataForTotalCustomerPayRevenue = (
  queryMonth,
  paytype,
  callback
) => {
  apolloClientPostgres
    .query({
      query: GET_TOTAL_CUSTOMER_REVENUE_DETAILS_DRILL_DOWN,
      variables: {
        month_year: queryMonth,
        paytype: paytype
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getSearchByRODetails = (ronumberSelected, storeId, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_SEARCHBYRO_DETAILS
          : GET_SEARCHBYRO_DETAILS_OLD,
      variables: {
        ronumberSelected: ronumberSelected,
        storeId: storeId
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/SearchByRO',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_SEARCHBYRO_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getSearchByROXML = (ronumberSelected, storeId, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_SEARCHBYRO_XML,
      variables: {
        ronumberSelected: ronumberSelected,
        store_id: storeId
          ? storeId
          : JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/SearchByRO',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_SEARCHBYRO_XML',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getSearchByROJSON = (ronumberSelected, callback) => {
  apolloClientPostgres
    .query({
      query: GET_SEARCHBYRO_JSON,
      variables: {
        ronumberSelected: ronumberSelected,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDataForTotalPricingOpportunity = (queryMonth, callback) => {
  queryMonth = [queryMonth + '-01', queryMonth + '-02', queryMonth + '-03'];
  apolloClientPostgres
    .query({
      query: GET_DATA_FOR_TOTAL_PRICING_OPPORTUNITY,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getTrancheReporForLaborRateAnalysis = callback => {
  apolloClientPostgres
    .query({
      query: GET_TRANCHE_REPORT_FOR_LABOR_RATE_ANALYSIS,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getTrancheReportForIncludedOpcodes = callback => {
  apolloClientPostgres
    .query({
      query: GET_TRANCHE_REPORT_FOR_INCLUDED_OPCODES,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getTrancheReportForExcludedOpcodes = callback => {
  apolloClientPostgres
    .query({
      query: GET_TRANCHE_REPORT_FOR_EXCLUDED_OPCODES,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getElrWarrantyPaytypes = callback => {
  apolloClientPostgres
    .query({
      query: GET_ELR_WARRANTY_PAY_TYPES,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getTopFiveOpportunities = callback => {
  apolloClientPostgres
    .query({
      query: GET_TOP_FIVE_OPPORTUNITIES,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getSearchByROJSONFerrario = (ronumberSelected, callback) => {
  apolloClientPostgres
    .query({
      query: GET_SEARCHBYRO_JSON_FERRARIO,
      variables: {
        ronumberSelected: ronumberSelected,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getLatestClosedDate = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_LATEST_CLOSED_DATE,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      //  console.log('startttt==', start, new Date(), new Date() - start);
      const spanAttribute = {
        pageUrl: '/LaborItemization',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_LATEST_CLOSED_DATE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result.data.statelessCcPhysicalRwGetConfigurationValues.nodes);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getLatestOpenDate = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_LATEST_OPEN_DATE,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      // console.log('startttt==', start, new Date(), new Date() - start);
      const spanAttribute = {
        pageUrl: '/CPOverview',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_LATEST_OPEN_DATE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result.data.statelessCcPhysicalRwGetConfigurationValues.nodes);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getSearchByROXMLReynolds = (ronumberSelected, callback) => {
  apolloClientPostgres
    .query({
      query: GET_SEARCHBYRO_XML_RENOLDS,
      variables: {
        ronumberSelected: ronumberSelected,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getValidMakes = callback => {
  apolloClientPostgres
    .query({
      query: GET_VALID_MAKES,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getIncludedMakesDetails = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_INCLUDED_MAKES_DETAILS,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Makes',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_INCLUDED_MAKES_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getExcludedMakesDetails = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_EXCLUDED_MAKES_DETAILS,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Makes',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_EXCLUDED_MAKES_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDrillDownDataForPartsMarkupCharts = (
  queryMonth,
  payType,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_DRILL_DOWN_DATA_FOR_PARTS_MARKUP_DETAILS,
      variables: {
        month_year: queryMonth,
        payType: payType,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/AnalyzeData',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DRILL_DOWN_DATA_FOR_PARTS_MARKUP_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getQualifiedMapperOpcodes = callback => {
  apolloClientPostgres
    .query({
      query: GET_QUALIFIED_MAPPER_OPCODES,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getNonQualifiedMapperOpcodes = callback => {
  apolloClientPostgres
    .query({
      query: GET_NON_QUALIFIED_MAPPER_OPCODES,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getFixedopsVsMapperOpCategorization = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_FIXEDOPS_VS_MAPPER_OPCATEGORIZATION,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/FixedopsMapperOpcodes',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_FIXEDOPS_VS_MAPPER_OPCATEGORIZATION',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getMenuOpcodesDetails = callback => {
  apolloClientPostgres
    .query({
      query: GET_MENU_OPCODE_DETAILS,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      return error;
    });
};
export const getMpiOpcodesDetails = callback => {
  apolloClientPostgres
    .query({
      query: GET_MPI_OPCODE_DETAILS,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      return error;
    });
};
export const getSharedSequencesByMakeOverall = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_SHARED_SEQUENCES_BY_MAKE_OVERALL,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/SharedSequencesByMake',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_SHARED_SEQUENCES_BY_MAKE_OVERALL',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getSharedSequencesByMakeMonthly = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_SHARED_SEQUENCES_BY_MAKE_MONTHLY,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/SharedSequencesByMake',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_SHARED_SEQUENCES_BY_MAKE_MONTHLY',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getNewCarWarrantyOverall = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_NEW_CAR_WARRANTY_OVERALL,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/NewCarWarranty',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_NEW_CAR_WARRANTY_OVERALL',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getNewCarWarrantyMonthly = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_NEW_CAR_WARRANTY_MONTHLY,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/NewCarWarranty',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_NEW_CAR_WARRANTY_MONTHLY',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForAllRevenue = (queryMonth, callback) => {
  apolloClientPostgres
    .query({
      query: GET_DRILL_DOWN_DATA_FOR_ALL_REVENUE,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getNewCarWarrantySixMonths = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_NEW_CAR_WARRANTY_SIX_MONTHS,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/NewCarWarranty',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_NEW_CAR_WARRANTY_SIX_MONTHS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getRevenueSummaryWarrantyVolumesLabor = (
  queryMonth,
  optQueryMonth,

  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_REVENUE_SUMMARY_WARRANTY_VOLUMES_LABOR,
      variables: {
        currMonth: queryMonth,
        optMonth: optQueryMonth,
        storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/RevenueSummary',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_REVENUE_SUMMARY_WARRANTY_VOLUMES_LABOR',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getRevenueSummaryWarrantyVolumesParts = (
  queryMonth,
  optQueryMonth,

  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_REVENUE_SUMMARY_WARRANTY_VOLUMES_PARTS,
      variables: {
        currMonth: queryMonth,
        optMonth: optQueryMonth,
        storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/RevenueSummary',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_REVENUE_SUMMARY_WARRANTY_VOLUMES_PARTS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getMonthYears = callback => {
  const start = new Date();
  makeApolloClientPostgres
    .query({
      query: GET_MONTH_YEARS,
      variables: {},
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/RevenueSummary',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_MONTH_YEARS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownMonthYears = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_DRILLDOWN_MONTHYEARS,
      variables: {},
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/DailyDataImports',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DRILLDOWN_MONTHYEARS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getWarrantyReimbursementsGetAnalysisWarrantyVolumesDaterange = (
  startDate,
  endDate,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_WARRANTY_RATES_LABOR_DATERANGE,
      variables: {
        startdate: startDate,
        enddate: endDate,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/WarrantyRatesLabor',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_WARRANTY_RATES_LABOR_DATERANGE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForWarrantyRatesLabor = (queryMonth, callback) => {
  apolloClientPostgres
    .query({
      query: GET_WARRANTY_RATES_LABOR,
      variables: {
        month_year: queryMonth,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForWarrantyRatesParts = (
  startDate,
  endDate,
  queryMonth,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_WARRANTY_RATES_PARTS,
      variables: {
        startDate: startDate,
        endDate: endDate,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/WarrantyMarkupParts',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_WARRANTY_RATES_PARTS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getWarrantyReferenceLabor = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_WARRANTY_REFERENCE_LABOR,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/WarrantyReferenceLabor',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_WARRANTY_REFERENCE_LABOR',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getWarrantyReferenceParts = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_WARRANTY_REFERENCE_PARTS,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/WarrantyReferenceParts',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_WARRANTY_REFERENCE_PARTS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getLaborTotalOpportunity = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_LABOR_TOTAL_OPPORTUNITY,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/LaborGrossAndVolumeOpportunity',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_LABOR_TOTAL_OPPORTUNITY',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const mwRefreshOpportunity = (groupName, callback) => {
  apolloClientPostgres
    .query({
      query: OPPORTUNITY_MW_REFRESH,
      variables: {
        groupname: groupName,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getPartsTotalOpportunity = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_PARTS_TOTAL_OPPORTUNITY,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsGrossAndVolumeOpportunity',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_PARTS_TOTAL_OPPORTUNITY',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getELRTotalOpportunity = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_ELR_TOTAL_OPPORTUNITY,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/CPELROpportunity',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_ELR_TOTAL_OPPORTUNITY',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getAllDuplicateDashboards = (chartId, callback) => {
  apolloClientPostgres
    .query({
      query: GET_ALL_DUPLICATE_DASHBOARDS,
      variables: {
        chartId: chartId,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getChartMarkdown = (chartId, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_CHART_DETAILS,
      fetchPolicy: 'no-cache',
      variables: {
        chartId: chartId
      }
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/GraphDetailsView',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_CHART_DETAILS' + chartId,
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result.data.statelessCcPhysicalRwGetChartMasters.nodes);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getLaborOpportunityBaseline = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_LABOR_OPPORTUNITY_BASELINE,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        timeZone: timeZone
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/LaborGrossAndVolumeOpportunity',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_LABOR_OPPORTUNITY_BASELINE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getpartsOpportunityBaseline = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_PARTS_OPPORTUNITY_BASELINE,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        timeZone: timeZone
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsGrossAndVolumeOpportunity',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_PARTS_OPPORTUNITY_BASELINE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getElrOpportunityBaseline = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_ELR_OPPORTUNITY_BASELINE,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        timeZone: timeZone
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/CPELROpportunity',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_ELR_OPPORTUNITY_BASELINE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getKpiToggleOptions = callback => {
  apolloClientPostgres
    .query({
      query: GET_KPI_TOGGLE_OPTIONS,
      fetchPolicy: 'no-cache',
      addTypename: false
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDataForCWITotalcharts = (
  toggleOption,
  serviceAdvisor,
  technician,
  timeZoneVal,
  callback
) => {
  const start = new Date();
  serviceAdvisor == 'All' && technician == 'All'
    ? apolloClientPostgres
        .query({
          query: GET_DATA_FOR_CWITOTAL_CHARTS,
          variables: {
            toggleOption: toggleOption,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_CWITOTAL_CHARTS',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardCwitotal
              .statelessDbdKpiScorecardKpiScorecardCwitotals
          );
        })
        .catch(error => {
          return error;
        })
    : apolloClientPostgres
        .query({
          query: GET_DATA_FOR_CWITOTAL_ADVISOR_CHARTS,
          variables: {
            toggleOption: toggleOption,
            serviceAdvisor: serviceAdvisor == 'All' ? null : serviceAdvisor,
            technician: technician == 'All' ? null : technician,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardCwitotal
              .statelessDbdKpiScorecardKpiScorecardCwitotals
          );
        })
        .catch(error => {
          return error;
        });
};

export const getDataForKPIROSharechart = (
  toggleOption,
  serviceAdvisor,
  technician,
  timeZoneVal,
  callback
) => {
  const start = new Date();
  serviceAdvisor == 'All' && technician == 'All'
    ? apolloClientPostgres
        .query({
          query: GET_DATA_FOR_ROSHARE_CHART,
          variables: {
            toggleOption: toggleOption,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_ROSHARE_CHART',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardRoshare
              .statelessDbdKpiScorecardKpiScorecardRoshares
          );
        })
        .catch(error => {
          return error;
        })
    : apolloClientPostgres
        .query({
          query: GET_DATA_FOR_ROSHARE_ADVISOR_CHART,
          variables: {
            toggleOption: toggleOption,
            serviceAdvisor: serviceAdvisor == 'All' ? null : serviceAdvisor,
            technician: technician == 'All' ? null : technician,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardRoshare
              .statelessDbdKpiScorecardKpiScorecardRoshares
          );
        })
        .catch(error => {
          return error;
        });
};
export const getDataForKPILineROLtSixtyK = (
  toggleOption,
  serviceAdvisor,
  technician,
  timeZoneVal,
  callback
) => {
  const start = new Date();
  serviceAdvisor == 'All' && technician == 'All'
    ? apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_LINERO_LT_SIXTY_K,
          variables: {
            toggleOption: toggleOption,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_LINERO_LT_SIXTY_K',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardLineroLtSixtyk
              .statelessDbdKpiScorecardKpiScorecardLineros
          );
        })
        .catch(error => {
          return error;
        })
    : apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_LINERO_ADVISOR_LT_SIXTY_K,
          variables: {
            toggleOption: toggleOption,
            serviceAdvisor: serviceAdvisor == 'All' ? null : serviceAdvisor,
            technician: technician == 'All' ? null : technician,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardLineroLtSixtyk
              .statelessDbdKpiScorecardKpiScorecardLineros
          );
        })
        .catch(error => {
          return error;
        });
};
export const getDataForKPILineROGtSixtyK = (
  toggleOption,
  serviceAdvisor,
  technician,
  timeZoneVal,
  callback
) => {
  const start = new Date();
  serviceAdvisor == 'All' && technician == 'All'
    ? apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_LINERO_GT_SIXTY_K,
          variables: {
            toggleOption: toggleOption,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_LINERO_GT_SIXTY_K',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardLineroGtSixtyk
              .statelessDbdKpiScorecardKpiScorecardLineros
          );
        })
        .catch(error => {
          return error;
        })
    : apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_LINERO_ADVISOR_GT_SIXTY_K,
          variables: {
            toggleOption: toggleOption,
            serviceAdvisor: serviceAdvisor == 'All' ? null : serviceAdvisor,
            technician: technician == 'All' ? null : technician,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardLineroGtSixtyk
              .statelessDbdKpiScorecardKpiScorecardLineros
          );
        })
        .catch(error => {
          return error;
        });
};

export const getDataForKPIAvgAgeMiles = (
  toggleOption,
  serviceAdvisor,
  technician,
  timeZoneVal,
  callback
) => {
  const start = new Date();
  serviceAdvisor == 'All' && technician == 'All'
    ? apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_AVG_AGE_MILES,
          variables: {
            toggleOption: toggleOption,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_AVG_AGE_MILES',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardAvgAgeMiles
              .statelessDbdKpiScorecardKpiScorecardAvgAgeMiles
          );
        })
        .catch(error => {
          return error;
        })
    : apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_AVG_AGE_MILES_ADVISOR,
          variables: {
            toggleOption: toggleOption,
            serviceAdvisor: serviceAdvisor == 'All' ? null : serviceAdvisor,
            technician: technician == 'All' ? null : technician,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_AVG_AGE_MILES_ADVISOR',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardAvgAgeMiles
              .statelessDbdKpiScorecardKpiScorecardAvgAgeMiles
          );
        })
        .catch(error => {
          return error;
        });
};

export const getDataForKPIFlatRateHrs = (
  toggleOption,
  serviceAdvisor,
  technician,
  timeZoneVal,
  callback
) => {
  const start = new Date();
  serviceAdvisor == 'All' && technician == 'All'
    ? apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_FLAT_RATE_HRS,
          variables: {
            toggleOption: toggleOption,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_FLAT_RATE_HRS',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardFlatratehrs
              .statelessDbdKpiScorecardKpiScorecardFlatratehrs
          );
        })
        .catch(error => {
          return error;
        })
    : apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_FLAT_RATE_HRS_ADVISOR,
          variables: {
            toggleOption: toggleOption,
            serviceAdvisor: serviceAdvisor == 'All' ? null : serviceAdvisor,
            technician: technician == 'All' ? null : technician,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_FLAT_RATE_HRS_ADVISOR',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardFlatratehrs
              .statelessDbdKpiScorecardKpiScorecardFlatratehrs
          );
        })
        .catch(error => {
          return error;
        });
};

export const getDataForKPILaborGpRo = (
  toggleOption,
  serviceAdvisor,
  technician,
  timeZoneVal,
  callback
) => {
  const start = new Date();
  serviceAdvisor == 'All' && technician == 'All'
    ? apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_LABOR_GP_RO,
          variables: {
            toggleOption: toggleOption,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_LABOR_GP_RO',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardLaborGpRo
              .statelessDbdKpiScorecardKpiScorecardLaborGpRos
          );
        })
        .catch(error => {
          return error;
        })
    : apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_LABOR_GP_RO_ADVISOR,
          variables: {
            toggleOption: toggleOption,
            serviceAdvisor: serviceAdvisor == 'All' ? null : serviceAdvisor,
            technician: technician == 'All' ? null : technician,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_LABOR_GP_RO_ADVISOR',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardLaborGpRo
              .statelessDbdKpiScorecardKpiScorecardLaborGpRos
          );
        })
        .catch(error => {
          return error;
        });
};
export const getDataForKPIPartsGpRo = (
  toggleOption,
  serviceAdvisor,
  technician,
  timeZoneVal,
  callback
) => {
  const start = new Date();
  serviceAdvisor == 'All' && technician == 'All'
    ? apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_PARTS_GP_RO,
          variables: {
            toggleOption: toggleOption,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_PARTS_GP_RO',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardPartsGpRo
              .statelessDbdKpiScorecardKpiScorecardPartsGpRos
          );
        })
        .catch(error => {
          return error;
        })
    : apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_PARTS_GP_RO_ADVISOR,
          variables: {
            toggleOption: toggleOption,
            serviceAdvisor: serviceAdvisor == 'All' ? null : serviceAdvisor,
            technician: technician == 'All' ? null : technician,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_PARTS_GP_RO_ADVISOR',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardPartsGpRo
              .statelessDbdKpiScorecardKpiScorecardPartsGpRos
          );
        })
        .catch(error => {
          return error;
        });
};
export const getDataForKPITotalGpRo = (
  toggleOption,
  serviceAdvisor,
  technician,
  timeZoneVal,
  callback
) => {
  const start = new Date();
  serviceAdvisor == 'All' && technician == 'All'
    ? apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_TOTAL_GP_RO,
          variables: {
            toggleOption: toggleOption,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_TOTAL_GP_RO',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardTotalGpRo
              .statelessDbdKpiScorecardKpiScorecardTotGpRos
          );
        })
        .catch(error => {
          return error;
        })
    : apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_TOTAL_GP_RO_ADVISOR,
          variables: {
            toggleOption: toggleOption,
            serviceAdvisor: serviceAdvisor == 'All' ? null : serviceAdvisor,
            technician: technician == 'All' ? null : technician,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_TOTAL_GP_RO_ADVISOR',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardTotalGpRo
              .statelessDbdKpiScorecardKpiScorecardTotGpRos
          );
        })
        .catch(error => {
          return error;
        });
};
export const getDataForKPIWorkmix = (
  toggleOption,
  serviceAdvisor,
  technician,
  timeZoneVal,
  callback
) => {
  const start = new Date();
  serviceAdvisor == 'All' && technician == 'All'
    ? apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_WORK_MIX,
          variables: {
            toggleOption: toggleOption,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_WORK_MIX',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardWorkmix
              .statelessDbdKpiScorecardKpiScorecardWorkmixes
          );
        })
        .catch(error => {
          return error;
        })
    : apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_WORK_MIX_ADVISOR,
          variables: {
            toggleOption: toggleOption,
            serviceAdvisor: serviceAdvisor == 'All' ? null : serviceAdvisor,
            technician: technician == 'All' ? null : technician,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_WORK_MIX_ADVISOR',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardWorkmix
              .statelessDbdKpiScorecardKpiScorecardWorkmixes
          );
        })
        .catch(error => {
          return error;
        });
};

export const getDataForKPILaborGrid = (
  toggleOption,
  serviceAdvisor,
  timeZoneVal,
  payType,
  gridType,
  technician,
  callback
) => {
  const start = new Date();
  serviceAdvisor == 'All' && technician == 'All'
    ? apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_LABOR_GRID,
          variables: {
            toggleOption: toggleOption,
            timeZone: timeZoneVal ? timeZoneVal : timeZone,
            payType: payType,
            gridtype: gridType
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_LABOR_GRID',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardGriddata
              .statelessDbdKpiScorecardKpiScorecardGriddata
          );
        })
        .catch(error => {
          return error;
        })
    : apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_LABOR_GRID_ADVISOR,
          variables: {
            toggleOption: toggleOption,
            serviceAdvisor: serviceAdvisor == 'All' ? null : serviceAdvisor,
            technician: technician == 'All' ? null : technician,
            timeZone: timeZoneVal ? timeZoneVal : timeZone,
            payType: payType,
            gridtype: gridType
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_LABOR_GRID_ADVISOR',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardGriddata
              .statelessDbdKpiScorecardKpiScorecardGriddata
          );
        })
        .catch(error => {
          return error;
        });
};

export const getDataForKPILineRO = (
  toggleOption,
  serviceAdvisor,
  technician,
  timeZoneVal,
  callback
) => {
  const start = new Date();
  serviceAdvisor == 'All' && technician == 'All'
    ? apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_LINE_RO,
          variables: {
            toggleOption: toggleOption,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_LINE_RO',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardLinero
              .statelessDbdKpiScorecardKpiScorecardLineros
          );
        })
        .catch(error => {
          return error;
        })
    : apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_LINE_RO_ADVISOR,
          variables: {
            toggleOption: toggleOption,
            serviceAdvisor: serviceAdvisor == 'All' ? null : serviceAdvisor,
            technician: technician == 'All' ? null : technician,
            timeZone: timeZoneVal ? timeZoneVal : timeZone
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_LINE_RO_ADVISOR',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data.statelessDbdKpiScorecardGetKpiScorecardLinero
              .statelessDbdKpiScorecardKpiScorecardLineros
          );
        })
        .catch(error => {
          return error;
        });
};

export const getDataForKPIPartsGrid = (
  toggleOption,
  serviceAdvisor,
  timeZoneVal,
  payType,
  technician,
  callback
) => {
  const start = new Date();
  serviceAdvisor == 'All' && technician == 'All'
    ? apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_PARTS_GRID,
          variables: {
            toggleOption: toggleOption,
            timeZone: timeZoneVal ? timeZoneVal : timeZone,
            payType: payType
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_PARTS_GRID',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data
              .statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddata
              .statelessDbdKpiScorecardVwKpiScorecardPartsMatrixGriddata
          );
        })
        .catch(error => {
          return error;
        })
    : apolloClientPostgres
        .query({
          query: GET_DATA_FOR_KPI_PARTS_GRID_ADVISOR,
          variables: {
            toggleOption: toggleOption,
            serviceAdvisor: serviceAdvisor == 'All' ? null : serviceAdvisor,
            technician: technician == 'All' ? null : technician,
            timeZone: timeZoneVal ? timeZoneVal : timeZone,
            payType: payType
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/KpiReport',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_KPI_PARTS_GRID_ADVISOR',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(
            result.data
              .statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddata
              .statelessDbdKpiScorecardVwKpiScorecardPartsMatrixGriddata
          );
        })
        .catch(error => {
          return error;
        });
};

export const getDataForLaborMisses = (
  filterBy,
  filterType,
  timeZoneVal,
  payType,
  gridType,
  filterStart,
  filterEnd,
  callback
) => {
  const start = new Date();
  filterType == 'duration'
    ? apolloClientPostgres
        .query({
          query:
            localStorage.getItem('versionFlag') == 'TRUE'
              ? GET_DATA_FOR_LABOR_MISSES
              : GET_DATA_FOR_LABOR_MISSES_OLD,
          variables: {
            filterby: filterBy,
            timeZone: timeZoneVal ? timeZoneVal : timeZone,
            payType: payType,
            gridType: gridType != 'All' ? gridType : null,
            filterStart: filterStart,
            filterEnd: filterEnd
            ///store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/LaborMisses',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_LABOR_MISSES',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(result);
        })
        .catch(error => {
          return error;
        })
    : apolloClientPostgres
        .query({
          query: GET_DATA_FOR_LABOR_MISSES,
          variables: {
            monthyear: filterBy,
            payType: payType,
            timeZone: timeZoneVal ? timeZoneVal : timeZone,
            filterStart: filterStart,
            filterEnd: filterEnd
            ///store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/LaborMisses',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_LABOR_MISSES',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(result);
        })
        .catch(error => {
          return error;
        });
};

export const getDataForPartsMisses = (
  filterBy,
  filterType,
  timeZoneVal,
  payType,
  filterStart,
  filterEnd,
  callback
) => {
  const start = new Date();
  filterType == 'duration'
    ? apolloClientPostgres
        .query({
          query:
            localStorage.getItem('versionFlag') == 'TRUE'
              ? GET_DATA_FOR_PARTS_MISSES
              : GET_DATA_FOR_PARTS_MISSES_OLD,
          variables: {
            filterby: filterBy,
            timeZone: timeZoneVal ? timeZoneVal : timeZone,
            payType: payType,
            filterStart: filterStart,
            filterEnd: filterEnd
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/PartsMisses',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_DATA_FOR_PARTS_MISSES',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          callback(result);
        })
        .catch(error => {
          return error;
        })
    : apolloClientPostgres
        .query({
          query: GET_DATA_FOR_PARTS_MISSES,
          variables: {
            // monthyear: filterBy,
            payType: payType,
            timeZone: timeZoneVal ? timeZoneVal : timeZone,
            filterStart: filterStart,
            filterEnd: filterEnd
          },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          callback(result);
        })
        .catch(error => {
          return error;
        });
};

export const getGlossaryDetails = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_GLOSSARY_DETAILS,

      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Glossary',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_GLOSSARY_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getGridorMatrixDetails = (
  type,
  payType,
  partSource,
  storeId,
  gridFor,
  callback
) => {
  const start = new Date();
  if (type == 'matrix') {
    apolloClientPostgres
      .query({
        query:
          localStorage.getItem('versionFlag') == 'TRUE'
            ? GET_GRIDORMETRIX_DETAILS_PRTS
            : GET_GRIDORMETRIX_DETAILS_PRTS_OLD,
        variables: {
          gridormatrix: type,
          payType: payType,
          partSource: partSource,
          storeId: storeId,
          pGridorpartsfor: gridFor
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/LaborGridPricing',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_GRIDORMETRIX_DETAILS_PRTS' + type,
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(result);
      })
      .catch(error => {
        if (error.graphQLErrors) {
          error.graphQLErrors.map(({ message, locations, path }) =>
            console.log(
              `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
            )
          );
        }
        return error;
      });
  } else {
    apolloClientPostgres
      .query({
        query:
          localStorage.getItem('versionFlag') == 'TRUE'
            ? GET_GRIDORMETRIX_DETAILS
            : GET_GRIDORMETRIX_DETAILS_OLD,
        variables: {
          gridormatrix: type,
          payType: payType,
          storeId: storeId,
          pGridorpartsfor: gridFor
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/LaborGridPricing',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_GRIDORMETRIX_DETAILS' + type,
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(result);
      })
      .catch(error => {
        if (error.graphQLErrors) {
          error.graphQLErrors.map(({ message, locations, path }) =>
            console.log(
              `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
            )
          );
        }
        return error;
      });
  }
};
export const getMenuOpcodes = callback => {
  apolloClientPostgres
    .query({
      query: GET_MENU_OPCODES,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      if (
        result &&
        result.data.statelessCcPhysicalRwGetMenuOpcodes.nodes[0].length > 0
      ) {
        var menuOpcodesArr = result.data.statelessCcPhysicalRwGetMenuOpcodes.nodes[0]
          .replace(/[{}]/g, '')
          .split(',');
        callback(menuOpcodesArr);
      }
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getMPIOpcodes = callback => {
  apolloClientPostgres
    .query({
      query: GET_MPI_OPCODES,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      if (
        result &&
        result.data.statelessCcPhysicalRwGetMpiOpcodes.nodes[0].length > 0
      ) {
        var mpiOpcodesArr = result.data.statelessCcPhysicalRwGetMpiOpcodes.nodes[0]
          .replace(/[{}]/g, '')
          .split(',');
        callback(mpiOpcodesArr);
      }
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getTargetGridRate = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_TARGET_GRID_RATE,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/LaborMisses',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_TARGET_GRID_RATE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getKpiToggleOptionsWithTimeZone = (timeZoneVal, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_KPI_TOGGLE_OPTIONS_WITH_TIMEZONE,
      variables: {
        timezoneOffset: timeZoneVal ? timeZoneVal : timeZone
      },
      fetchPolicy: 'no-cache',
      addTypename: false
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/KpiReport',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_KPI_TOGGLE_OPTIONS_WITH_TIMEZONE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDataForLaborMissesWithRO = (
  filterBy,
  timeZoneVal,
  roNumber,
  closeDate,
  payType,
  gridType,
  filterStart,
  filterEnd,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_DATA_FOR_LABOR_MISSES_WITH_RO
          : GET_DATA_FOR_LABOR_MISSES_WITH_RO_OLD,
      variables: {
        filterby: filterBy,
        timeZone: timeZoneVal ? timeZoneVal : timeZone,
        roNumber: roNumber,
        closeDate: closeDate,
        payType: payType,
        gridType: gridType,
        filterStart: filterStart ? filterStart : null,
        filterEnd: filterEnd ? filterEnd : null
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/SearchByRO',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DATA_FOR_LABOR_MISSES_WITH_RO',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};

export const getDataForPartsMissesWithRO = (
  filterBy,
  timeZoneVal,
  roNumber,
  closedDate,
  payType,
  filterStart,
  filterEnd,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_DATA_FOR_PARTS_MISSES_WITH_RO
          : GET_DATA_FOR_PARTS_MISSES_WITH_RO_OLD,
      variables: {
        filterby: filterBy,
        timeZone: timeZoneVal ? timeZoneVal : timeZone,
        roNumber: roNumber,
        closedDate: closedDate,
        payType: payType,
        filterStart: filterStart ? filterStart : null,
        filterEnd: filterEnd ? filterEnd : null
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/SearchByRO',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DATA_FOR_PARTS_MISSES_WITH_RO',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};
export const getMenuOpcodesList = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_MENU_OPCODES_LIST,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/MenuOpcode',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_MENU_OPCODES_LIST',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getMPIOpcodesList = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_MPI_OPCODES_LIST,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/MPIOpcode',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_MPI_OPCODES_LIST',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
      console.log('result', result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getAdvisorName = (serviceadvisor, callback) => {
  apolloClientPostgres
    .query({
      query: GET_ADVISOR_NAME,
      variables: {
        serviceadvisor: serviceadvisor
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      return error;
    });
};

export const getKPIScorecardGoal = (timeZone, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_KPI_SCORE_CARD_GOAL,
      variables: {
        calltype: 'store',
        reporttype: 'advisor_individual_report',
        serviceadvisor: 'store_goal',
        timezoneOffset: timeZone
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/ScoreCardGoalSetting',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_KPI_SCORE_CARD_GOAL',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      console.log('result', result);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};

export const getKPIScorecardGoalAdvisor = (advisor, timeZone, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_KPI_SCORE_CARD_GOAL,
      variables: {
        calltype: 'advisor',
        reporttype: 'advisor_individual_report',
        timezoneOffset: timeZone,
        serviceadvisor: advisor
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/ScoreCardGoalSetting',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_KPI_SCORE_CARD_GOAL',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};

export const getKPIScorecardGoalTechnician = (
  technician,
  timeZone,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_KPI_SCORE_CARD_GOAL,
      variables: {
        calltype: 'technician',
        reporttype: 'tech_comparative_report',
        timezoneOffset: timeZone,
        serviceadvisor: technician
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/ScoreCardGoalSetting',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_KPI_SCORE_CARD_GOAL',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};

export const getClientId = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_CLIENT_ID,

      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Home',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_CLIENT_ID',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};

export const checkInternalToggleExistance = (partyid, shortkey, callback) => {
  apolloClientPostgres
    .query({
      query: CHECK_INTERNAL_TOGGLE_EXISTANCE,
      variables: {
        partyid: partyid,
        shortkey: shortkey
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      return error;
    });
};
export const getSearchByROJSONData = (ronumberSelected, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_SEARCHBYRO_JSON_DATA,
      variables: {
        ronumberSelected: ronumberSelected,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      console.log('result', result);
      const spanAttribute = {
        pageUrl: '/SearchByRO',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_SEARCHBYRO_JSON_DATA',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};

export const getOrSetKPIScorecardEmail = (getSet, recipientlist, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_SET_KPI_SCORE_CARD_SETTINGS_EMAIL,
      variables: {
        getorset: getSet,
        recipientlist: recipientlist
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/ScoreCardGoalSetting',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_SET_KPI_SCORE_CARD_SETTINGS_EMAIL',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};

export const getUpdateStoreSettings = (
  getorset,
  pGoal,
  settingtype,
  timeZone,
  userid,
  storeNickName,
  dmsFee,
  fopcFee,
  callback
) => {
  const start = new Date();
  let client =
    getorset == 'get' ? apolloClientPostgres : apolloClientPostgresWrite;
  client
    .mutate({
      mutation: GET_SET_STORE_SETTINGS,
      variables: {
        getorset: getorset,
        pGoal: pGoal,
        settingtype: settingtype,
        timezoneValue: timeZone,
        userid: userid,
        storeNickName: storeNickName,
        dmsFee: dmsFee != '' ? dmsFee : 0,
        fopcFee: fopcFee != '' ? fopcFee : 0
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/StoreSettings',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_SET_STORE_SETTINGS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};

export const getMatrixDetail = (
  type,
  partSrc,
  payType,
  createdDate,
  partsFor,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_MATRIX_DETAIL,
      variables: {
        gridormatrix: type,
        storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        payType: payType,
        partSrc: partSrc,
        createddate: createdDate,
        pGridorpartsfor: partsFor
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsMatrix',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_MATRIX_DETAIL' + type,
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getMatrixType = (
  type,
  payType,
  partSrc,
  gridorpartsfor,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_MATRIX_TYPE,
      variables: {
        gridormatrix: type,
        storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        payType: payType,
        partSrc: partSrc,
        gridorpartsfor: gridorpartsfor
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsMatrix',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_MATRIX_TYPE' + type,
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getPartsGridList = (
  type,
  payType,
  partSrc,
  // createdDate,
  pGridorpartsfor,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_PARTS_LIST,
      variables: {
        gridormatrix: type,
        storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        payType: payType,
        partSrc: partSrc,
        // createdDate: createdDate,
        pGridorpartsfor: pGridorpartsfor
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsMatrix',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_MATRIX_TYPE' + type,
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getLaborGridDetails = (
  type,
  payType,
  partSrc,
  // createdDate,
  pGridorpartsfor,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_PARTS_DETAILS,
      variables: {
        gridormatrix: type,
        storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        payType: payType,
        partSrc: partSrc,
        // createdDate: createdDate,
        pGridorpartsfor: pGridorpartsfor
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsMatrix',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_MATRIX_TYPE' + type,
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getMatrixPartSource = (type, payType, storeId, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_MATRIX_PART_SOURCE
          : GET_MATRIX_PART_SOURCE_OLD,
      variables: {
        gridormatrix: type,
        payType: payType,
        storeId: storeId
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsMatrixPricing',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_MATRIX_PART_SOURCE' + type,
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      let resultArr = [];
      result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes.map(
        val => {
          resultArr.push(val.partSource);
        }
      );
      callback(resultArr);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getNotificationsUserLoginHistoryStatus = (
  enddate,
  startdate,
  realmid,
  timezoneOffset,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_NOTIFICATIONS_USER_LOGIN_HISTORY_STATUS,
      variables: {
        enddate: enddate,
        startdate: startdate,
        realmid: realmid,
        timezoneOffset: timezoneOffset,
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/UserLoginHistory',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_NOTIFICATIONS_USER_LOGIN_HISTORY_STATUS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const GetDailyDataloadStatus = (
  fromdate,
  statusDate,
  timezoneOffset,
  callback
) => {
  apolloClientPostgres
    .query({
      query: GET_DAILY_DATA_LOAD_STATUS,
      variables: {
        fromdate: fromdate,
        statusdate: statusDate,
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        timezoneOffset: timezoneOffset
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getGridorMatrixPayTypeDetails = (type, storeId, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_GRIDORMETRIX_PAYTYPE_DETAILS
          : GET_GRIDORMETRIX_PAYTYPE_DETAILS_OLD,
      variables: {
        gridormatrix: type,
        storeId: storeId ? storeId : ''
        // : JSON.parse(localStorage.getItem('selectedStoreId'))
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/KpiReport',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_GRIDORMETRIX_PAYTYPE_DETAILS' + type,
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      let resultArr = [];
      result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes.map(
        val => {
          resultArr.push(val.dipsGridorpartsfor);
        }
      );
      callback(resultArr);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getFleetCustomerNames = (store_id, callback) => {
  apolloClientPostgres
    .query({
      query: GET_FLEET_CUSTOMER_NAMES,
      variables: { store_id: store_id ? store_id : storeId },
      //variables: {},
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      console.log('ccc==111', result);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getGridorMatrixPeriods = (
  type,
  payType,
  partSource,
  storeId,
  gridFor,
  callback
) => {
  const start = new Date();
  if (type != 'matrix') {
    apolloClientPostgres
      .query({
        query:
          localStorage.getItem('versionFlag') == 'TRUE'
            ? GET_GRIDORMETRIX_PERIODS
            : GET_GRIDORMETRIX_PERIODS_OLD,
        variables: {
          gridormatrix: type,
          payType: payType,
          storeId: storeId,
          pGridorpartsfor: gridFor
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/LaborGridPricing',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_GRIDORMETRIX_PERIODS' + type,
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        let resultArr = [];
        result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes.map(
          val => {
            resultArr.push(Number(val.gridOrder));
          }
        );
        callback(resultArr);
      })
      .catch(error => {
        if (error.graphQLErrors) {
          error.graphQLErrors.map(({ message, locations, path }) =>
            console.log(
              `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
            )
          );
        }
        return error;
      });
  } else {
    apolloClientPostgres
      .query({
        query:
          localStorage.getItem('versionFlag') == 'TRUE'
            ? GET_GRIDORMETRIX_PERIODS_PRTSOURCE
            : GET_GRIDORMETRIX_PERIODS_PRTSOURCE_OLD,
        variables: {
          gridormatrix: type,
          payType: payType,
          partSource: partSource,
          storeId: storeId
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/LaborGridPricing',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_GRIDORMETRIX_PERIODS' + type,
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        let resultArr = [];
        result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes.map(
          val => {
            resultArr.push(Number(val.gridOrder));
          }
        );
        callback(resultArr);
      })
      .catch(error => {
        if (error.graphQLErrors) {
          error.graphQLErrors.map(({ message, locations, path }) =>
            console.log(
              `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
            )
          );
        }
        return error;
      });
  }
};

export const getOpcodeDetailedViewMonthYears = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_OPCODE_DETAILED_VIEW_MONTH_YEARS,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/LaborWorkMixAnalysis',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_OPCODE_DETAILED_VIEW_MONTH_YEARS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      let monthYears = [];
      result.data.statelessCcDrilldownGetDrillDownTotalRevenueDetailsMonthYears.nodes.map(
        val => {
          monthYears.push(val.monthYear);
        }
      );
      callback(monthYears);
    })
    .catch(error => {
      return error;
    });
};

export const GetDailyUpdateStatus = (
  startdate,
  enddate,
  optionName,
  timezoneOffset,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_DAILY_UPDATE_STATUS,
      variables: {
        optionName: optionName,
        startdate: startdate,
        enddate: enddate,
        timezoneOffset: timezoneOffset,
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/EditHistory',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DAILY_UPDATE_STATUS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const GetDailyUpdateStatusAll = (
  startdate,
  enddate,
  optionName,
  timezoneOffset,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_DAILY_UPDATE_STATUS_ALL,
      variables: {
        optionName: optionName,
        startdate: startdate,
        enddate: enddate,
        timezoneOffset: timezoneOffset,
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/EditHistoryAll',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DAILY_UPDATE_STATUS_ALL',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getUniqueOpcodes = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_UNIQUE_OPCODES,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/OPcodes',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_UNIQUE_OPCODES',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getLaborOpportunityBaselineAdvisor = (advisor, callback) => {
  apolloClientPostgres
    .query({
      query: GET_LABOR_OPPORTUNITY_BASELINE_ADVISOR,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        timeZone: timeZone,
        advisor: advisor
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getLaborTotalOpportunityAdvisor = (advisor, callback) => {
  apolloClientPostgres
    .query({
      query: GET_LABOR_TOTAL_OPPORTUNITY_ADVISOR,
      variables: {
        advisor: advisor,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getpartsOpportunityBaselineAdvisor = (advisor, callback) => {
  apolloClientPostgres
    .query({
      query: GET_PARTS_OPPORTUNITY_BASELINE_ADVISOR,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        timeZone: timeZone,
        advisor: advisor
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getPartsTotalOpportunityAdvisor = (advisor, callback) => {
  apolloClientPostgres
    .query({
      query: GET_PARTS_TOTAL_OPPORTUNITY_ADVISOR,
      variables: {
        advisor: advisor,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getElrOpportunityBaselineAdvisor = (advisor, callback) => {
  apolloClientPostgres
    .query({
      query: GET_ELR_OPPORTUNITY_BASELINE_ADVISOR,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        timeZone: timeZone,
        advisor: advisor
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getElrOpportunityAdvisor = (advisor, callback) => {
  apolloClientPostgres
    .query({
      query: GET_ELR_OPPORTUNITY_ADVISOR,
      variables: {
        advisor: advisor,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getFleetListItems = (fleetName, fleettype, store_id, callback) => {
  apolloClientPostgres
    .query({
      query: GET_FLEET_LIST,
      variables: {
        fleetname: fleetName,
        fleettype: fleettype,
        store_id: store_id ? store_id : storeId
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const insertFleetNameToList = (
  fleetName,
  process,
  pType,
  store_id,
  callback
) => {
  apolloClientPostgresWrite
    .mutate({
      mutation: ADD_FLEET_TO_LIST,
      variables: {
        fleetname: fleetName,
        userid: localStorage.getItem('userID'),
        pType: pType,
        process: process,
        store_id: store_id ? store_id : storeId
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getFleetMasterData = (pType, store_id, callback) => {
  apolloClientPostgres
    .mutate({
      mutation: ADD_FLEET_TO_LIST,
      variables: {
        fleetname: null,
        userid: localStorage.getItem('userID'),
        pType: pType,
        process: 'view',
        store_id: store_id ? store_id : storeId
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getFleetPayTypeNames = (pType, store_id, callback) => {
  apolloClientPostgres
    .mutate({
      mutation: ADD_FLEET_TO_LIST,
      variables: {
        fleetname: null,
        userid: localStorage.getItem('userID'),
        pType: pType,
        process: 'view',
        store_id: store_id ? store_id : storeId
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getROItems = (monthValue, advisors, callback) => {
  apolloClientPostgres
    .mutate({
      mutation: GET_RO_LIST,
      variables: {
        month: monthValue,
        advisor: advisors
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const insertFleetDetails = (
  fleet_rate_grid,
  fleet_rate_parts,
  fleet_exists_labor_grid,
  fleet_exists_parts_matrix,
  callback
) => {
  const userID = localStorage.getItem('userID');
  apolloClientPostgresWrite
    .mutate({
      mutation: INSERT_FLEET_DETAILS,
      variables: {
        user_id: localStorage.getItem('userID'),
        fleet_rate_grid: fleet_rate_grid,
        fleet_rate_parts: fleet_rate_parts,
        fleet_exists_labor_grid: fleet_exists_labor_grid,
        fleet_exists_parts_matrix: fleet_exists_parts_matrix
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getServiceAdvisorData = (serviceAdvisor, callback) => {
  if (serviceAdvisor != 'All') {
    apolloClientPostgres
      .query({
        query: GET_SERVICE_ADVISOR_DATA_SA,
        variables: {
          serviceadvisor: serviceAdvisor,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(
          result.data
            .statelessDbdPeopleMetricsServiceAdvisorGetRevenueTrendCharts.nodes
        );
      })
      .catch(error => {
        return error;
      });
  } else {
    apolloClientPostgres
      .query({
        query: GET_SERVICE_ADVISOR_DATA,
        variables: {
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(
          result.data
            .statelessDbdPeopleMetricsServiceAdvisorGetDrillDownServiceAdvisorRevenueReportAll
            .nodes
        );
      })
      .catch(error => {
        if (error.graphQLErrors) {
          error.graphQLErrors.map(({ message, locations, path }) =>
            console.log(
              `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
            )
          );
        }
        return error;
      });
  }
};
export const getFixedRateDetails = (
  action,
  laborOrParts,
  storeId,
  callback
) => {
  const start = new Date();
  const userID = localStorage.getItem('userID');
  apolloClientPostgres
    .mutate({
      mutation:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_FIXED_RATE_DETAILS
          : GET_FIXED_RATE_DETAILS_OLD,
      variables: {
        userid: localStorage.getItem('userID'),
        pAction: action,
        pLaborOrParts: laborOrParts,
        pOpcode: null,
        pPaytype: null,
        fixedratevalue: null,
        fixedratedate: null,
        storeId: storeId
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/FixedRates',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_FIXED_RATE_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getScatterPlotReportData = (type, advisor, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_SCATTER_PLOT_REPORT_DATA,
      variables: {
        type: type,
        advisor: advisor == 'All' ? null : advisor,
        timeZone: timeZone
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/JobCountGrid',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_SCATTER_PLOT_REPORT_DATA',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForTotalRevenueDateRange = (
  startDate,
  endDate,
  callback
) => {
  const start = new Date();
  const userID = localStorage.getItem('userID');
  apolloClientPostgres
    .mutate({
      mutation: GET_TOTAL_REVENUE_DETAILS_DRILL_DOWN_DATE_RANGE,
      variables: {
        startdate: startDate,
        enddate: endDate,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/AnalyzeData',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_TOTAL_REVENUE_DETAILS_DRILL_DOWN_DATE_RANGE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
// export const getDrillDownDataForTotalRevenueDateRange = (
//   startDate,
//   endDate,
//   callback
// ) => {
//   apolloClientPostgres
//     .query({
//       query: GET_TOTAL_REVENUE_DETAILS_DRILL_DOWN_DATE_RANGE,
//       variables: {
//         startdate: startDate,
//         enddate: endDate,
//         store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
//       },
//       fetchPolicy: 'no-cache'
//     })
//     .then(result => {
//       callback(result);
//       console.log('enter=12');
//     })
//     .catch(error => {
//       console.log('enter=1');
//       if (error.graphQLErrors) {
//         error.graphQLErrors.map(({ message, locations, path }) =>
//           console.log(
//             `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
//           )
//         );
//       }
//       return error;
//     });
// };

export const getAxcessaReportSummary = (advisor, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_AXCESSA_REPORT_SUMMARY,
      variables: {
        serviceAdvisor: advisor == 'All' ? null : advisor
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/TrendReport',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_AXCESSA_REPORT_SUMMARY',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getPayTypeMasterCodes = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_PAYTYPE_LIST,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getKpiReport2Data = (
  $advisor,
  $dayfilter,
  $filterby,
  $timezoneOffset,
  $pReportType,
  callback
) => {
  const userID = localStorage.getItem('userID');
  apolloClientPostgres
    .mutate({
      mutation: GET_KPI_REPORT_2_DATA,
      variables: {
        advisor: $advisor,
        dayfilter: $dayfilter,
        filterby: $filterby,
        timezoneOffset: $timezoneOffset,
        pReportType: $pReportType,
        userid: localStorage.getItem('userID')
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const get_SpecialMetric_1357 = (advisors, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_SPECIALMETRICS_1357,
      variables: {
        advisor: advisors
        // month_year: queryMonth
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/SpecialMetrics',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_SPECIALMETRICS_1357',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })

    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getLaunchDate = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_LAUNCH_DATE,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Home',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_LAUNCH_DATE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};

export const getPaytypeFixedRateDetails = (
  action,
  laborOrParts,
  storeId,
  callback
) => {
  const userID = localStorage.getItem('userID');
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_PAYTYPE_FIXED_RATE_DETAILS
          : GET_PAYTYPE_FIXED_RATE_DETAILS_OLD,
      variables: {
        pAction: action,
        pFixedratedate: null,
        pLaborFixedratevalue: null,
        pLaborOrParts: laborOrParts,
        pPartsFixedratevalue: null,
        pPaytype: null,

        userid: localStorage.getItem('userID'),
        storeId: storeId
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/FixedRates',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_PAYTYPE_FIXED_RATE_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getUsersList = (realm, callback) => {
  apolloClientPostgres
    .query({
      query: GET_USERS_LIST,
      variables: {
        realm: realm
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const updateEnableStatus = (username, status, callback) => {
  apolloClientPostgres
    .mutate({
      mutation: UPDATE_USER_ENABLE_STATUS,
      variables: {
        username: username,
        status: status
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getRealmsRoles = (realm, callback) => {
  apolloClientPostgres
    .query({
      query: GET_REALMS_ROLES,
      variables: {
        realm: realm
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getGroups = (realm, callback) => {
  apolloClientPostgres
    .query({
      query: GET_GROUPS,
      variables: {
        realm: realm
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getUsersAssignedRoles = (userName, callback) => {
  apolloClientPostgres
    .query({
      query: GET_USER_ASSIGNED_ROLES,
      variables: {
        userName: userName
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getUsersAssignedGrps = (userName, callback) => {
  apolloClientPostgres
    .query({
      query: GET_USER_ASSIGNED_GRPS,
      variables: {
        userName: userName
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getUserCredentialsStatus = (userName, callback) => {
  apolloClientPostgres
    .query({
      query: GET_USER_CREDENTIALS,
      variables: {
        userName: userName
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getCustomerHistoryData = (enddate, startdate, callback) => {
  apolloClientPostgres
    .mutate({
      mutation: GET_CUSTOMERHISTORY_DATA,
      variables: {
        enddate: enddate,
        startdate: startdate,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getStoreAdvisorDetails = callback => {
  apolloClientPostgres
    .query({
      query: GET_STORE_ADVISOR_DETAILS,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const gettechMetrics1352 = (techNo, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_TECHNICIAN_ESTIMATEDTECHNICIAN_SOLDHOURS_WEEKLY,
      variables: {
        techno: techNo,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/TechnicianPerformance',
        origin: '',
        event: 'Menu Load',
        is_from: ' GET_TECHNICIAN_ESTIMATEDTECHNICIAN_SOLDHOURS_WEEKLY',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getPayTypeWithoutStore = callback => {
  apolloClientPostgres
    .mutate({
      mutation: GET_PAYTYPE_WITHOUT_STORE,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getStoreAdvisorDetailsCDK = callback => {
  apolloClientPostgres
    .query({
      query: GET_STORE_ADVISOR_DETAILS_CDK,
      variables: {
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getKpiScoreCardDataStatus = (toggle, callback) => {
  apolloClientPostgres
    .query({
      query: GET_KPI_SCORE_CARD_DATA_STATUS,
      variables: {
        toggle: toggle,
        timeZone: timeZone
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDataForAdviorAndTechLaborMisses = (
  filterBy,
  filterType,
  timeZoneVal,
  payType,
  gridType,
  serviceAdvisor,
  technician,
  filterStart,
  filterEnd,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_DATA_FOR_ADVISOR_AND_TECH_LABOR_MISSES
          : GET_DATA_FOR_ADVISOR_AND_TECH_LABOR_MISSES_OLD,
      variables: {
        filterby: filterBy,
        timeZone: timeZoneVal ? timeZoneVal : timeZone,
        payType: payType,
        gridType: gridType,
        serviceAdvisor:
          serviceAdvisor == undefined || serviceAdvisor == 'All'
            ? null
            : serviceAdvisor,
        technician:
          technician == undefined || technician == 'All' ? null : technician,
        filterStart: filterStart,
        filterEnd: filterEnd
        ///store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/LaborMisses',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DATA_FOR_ADVISOR_AND_TECH_LABOR_MISSES',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};

export const getDataForAdvisorAndTechPartsMisses = (
  filterBy,
  filterType,
  timeZoneVal,
  payType,
  serviceAdvisor,
  technician,
  filterStart,
  filterEnd,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_DATA_FOR_ADVISOR_AND_TECH_PARTS_MISSES
          : GET_DATA_FOR_ADVISOR_AND_TECH_PARTS_MISSES_OLD,
      variables: {
        filterby: filterBy,
        timeZone: timeZoneVal ? timeZoneVal : timeZone,
        payType: payType,
        serviceAdvisor:
          serviceAdvisor == undefined || serviceAdvisor == 'All'
            ? null
            : serviceAdvisor,
        technician:
          technician == undefined || technician == 'All' ? null : technician,
        filterStart: filterStart,
        filterEnd: filterEnd
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsMisses',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DATA_FOR_ADVISOR_AND_TECH_PARTS_MISSES',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};

export const gettechMetricsTechJob = (techNo, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET__TECHNICIAN_TECH_JOB,
      variables: {
        techno: techNo,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/TechnicianPerformance',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET__TECHNICIAN_TECH_JOB',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const gettechMetricsSoldhoursWeekly = (techNo, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_TECHNICIAN_ESTIMATEDTECH_PRODUCTIVITY_WEEKLY,
      variables: {
        techno: techNo,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/TechnicianPerformance',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_TECHNICIAN_ESTIMATEDTECH_PRODUCTIVITY_WEEKLY',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getTechnicianDrilldownTechHoursWeekly = (
  paytype,
  techNo,
  weekdate,
  callback
) => {
  apolloClientPostgres
    .mutate({
      mutation: GET_TECHNICIAN_DRILLDOWN_TECH_HOURS,
      variables: {
        paytype: paytype,
        techno: techNo,
        weekdate: weekdate,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/TechnicianPerformance',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_TECHNICIAN_DRILLDOWN_TECH_HOURS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const gettechMetricsSingleJobROCount = (techNo, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_TECHNICIAN_SINGLE_JOB_RO_COUNT,
      variables: {
        techno: techNo,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/TechnicianPerformance',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_TECHNICIAN_SINGLE_JOB_RO_COUNT',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDatForTechMetricsSingleJob = (
  queryMonth,
  techNo,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_DRILL_DOWN_DATA_FOR_TECH_METRICS_SINGLE_JOB,
      variables: {
        month_year: queryMonth,
        techno: techNo
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/AnalyzeData',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DRILL_DOWN_DATA_FOR_SINGLE_JOB',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillDownDataForReports = (
  paytypeData,
  techNo,
  queryMonth,
  callback
) => {
  if (techNo != '') {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DATA_FOR_TECH_REPORT,
        variables: {
          paytypeData: paytypeData,
          lbrtechno: techNo,
          month_year: queryMonth,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  } else {
    apolloClientPostgres
      .query({
        query: GET_DRILL_DOWN_DATA_FOR_TECH_REPORT_ALL,
        variables: {
          paytypeData: paytypeData,
          store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        callback(result);
      })
      .catch(error => {
        return error;
      });
  }
};

export const getMpiValue = callback => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_MPI_VALUE,
      variables: {},
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Mpi',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_MPI_VALUE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const insertMpiValue = (frhValue, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: INSERT_MPI_VALUE,
      variables: {
        frhValue: frhValue,
        username: localStorage.getItem('userID')
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Mpi',
        origin: '',
        event: 'Menu Load',
        is_from: 'INSERT_MPI_VALUE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getPartsMatrix = (pCallType, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_PARTS_MATRIX,
      variables: {
        pCallType: pCallType,
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsMatrix',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_PARTS_MATRIX',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getPartsMatrixDetails = (
  pCallType,
  pMatrixType,
  pCreatedDate,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_PARTS_MATRIX_DETAILS,
      variables: {
        pCallType: pCallType,
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        pMatrixType: pMatrixType,
        pCreatedDate: pCreatedDate
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsMatrix',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_PARTS_MATRIX_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
// export const getDrillDownDataForReports = (
//   paytypeData,
//   techNo,
//   queryMonth,
//   callback
// ) => {
//   if (techNo != '') {
//     apolloClientPostgres
//       .query({
//         query: GET_DRILL_DOWN_DATA_FOR_TECH_REPORT,
//         variables: {
//           paytypeData: paytypeData,
//           lbrtechno: techNo,
//           month_year: queryMonth,
//           store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
//         },
//         fetchPolicy: 'no-cache'
//       })
//       .then(result => {
//         callback(result);
//       })
//       .catch(error => {
//         return error;
//       });
//   }
// };
export const getKpiComparativeReport = (
  startDate,
  endDate,
  techAdvisor,
  dayfilter,
  advisor,
  pReportType,
  timezoneOffset,
  tech,
  filterby,
  payType,
  gridtype,
  selectedRecalc,
  recalcAdvisor,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_KPI_COMPARATIVE_REPORT,
      variables: {
        argstartdate: startDate,
        argenddate: endDate,
        techAdvisor: techAdvisor,
        dayfilter: dayfilter,
        advisor: advisor,
        pReportType: pReportType,
        timezoneOffset: timezoneOffset,
        tech: tech,
        filterby: filterby,
        payType: payType,
        gridtype: gridtype,
        selectedRecalc: selectedRecalc,
        recalcAdvisor: recalcAdvisor
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/KPIReportComparative',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_KPI_COMPARATIVE_REPORT',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };

      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};
export const getKpiComparativeStoreReport = (
  startDate,
  endDate,
  techAdvisor,
  dayfilter,
  advisor,
  pReportType,
  timezoneOffset,
  tech,
  filterby,
  payType,
  gridtype,
  avgRecalcStore,
  avgRecalc,
  recalcStore,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_KPI_COMPARATIVE_STORE_REPORT,
      variables: {
        argstartdate: startDate,
        argenddate: endDate,

        dayfilter: dayfilter,

        pReportType: pReportType,
        timezoneOffset: timezoneOffset,

        filterby: filterby,
        payType: payType,
        gridtype: gridtype,
        avgRecalcStore: avgRecalcStore,
        avgRecalc: avgRecalc,
        recalcStore: recalcStore
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/KPIReportStoreComparative',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_KPI_COMPARATIVE_STORE_REPORT',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };

      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};

export const getKPIScoredCardsValues = (
  startDate,
  endDate,
  dayfilter,
  advisor,
  tech,
  pReportType,
  timezoneOffset,
  techAdvisor,
  filterby,
  payType,
  gridtype,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_KPI_SCORE_CARD_VALUES,
      variables: {
        argstartdate: startDate,
        argenddate: endDate,
        techAdvisor: techAdvisor,
        dayfilter: dayfilter,
        advisor: advisor,
        pReportType: pReportType,
        timezoneOffset: timezoneOffset,
        tech: tech,
        filterby: filterby,
        payType: payType,
        gridtype: gridtype
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/2.4.0/Home',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_KPI_SCORE_CARD_VALUES',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getHomeKpis = (
  startDate,
  endDate,
  advisor,
  tech,
  dayfilter,
  pReportType,
  timezoneOffset,
  techAdvisor,
  filterby,
  payType,
  gridtype,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_HOME_KPIS,
      variables: {
        argstartdate: startDate,
        argenddate: endDate,
        advisor: advisor,
        tech: tech,
        dayfilter: dayfilter,
        pReportType: pReportType,
        timezoneOffset: timezoneOffset,
        techAdvisor: techAdvisor,
        filterby: filterby,
        payType: payType,
        gridtype: gridtype
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/KpiGraphics',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_HOME_KPIS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getVersionFlags = callback => {
  apolloClientPostgres
    .mutate({
      mutation: GET_VERSION_FLAGS,
      variables: {},
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      return error;
    });
};

export const getMenuModalData = callback => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_MENU_MODEL_DATA,
      variables: {},
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/MenuModalMapping',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_MENU_MODEL_DATA',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};

export const crudMenuModels = (pVal, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: CRUD_MENU_MODELS,
      variables: {
        pVal: pVal,
        userid: localStorage.getItem('userID')
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/MenuModalMapping',
        origin: '',
        event: 'Menu Load',
        is_from: 'CRUD_MENU_MODELS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};
export const getEditHistoryToggleOptionsWithTimeZone = (
  timeZoneVal,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_EDIT_HISTORY_TOGGLE_OPTIONS_WITH_TIMEZONE,
      variables: {
        timezoneOffset: timeZoneVal ? timeZoneVal : timeZone
      },
      fetchPolicy: 'no-cache',
      addTypename: false
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/KpiReport',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_EDIT_HISTORY_TOGGLE_OPTIONS_WITH_TIMEZONE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getVersionFlagBeforeLogin = callback => {
  apolloClientPostgresAnonymous
    .mutate({
      mutation: GET_VERSION_FLAG_BEFORE_LOGIN,
      variables: {},
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      return error;
    });
};
export const getLaborMissesModels = callback => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_LABOR_MISSES_MODELS,
      variables: { pUserId: localStorage.getItem('userID') },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Modal',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_LABOR_MISSES_MODELS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const insertMakeDetails = (pVal, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: INSERT_MAKE_DETAILS,
      variables: {
        pVal: pVal,
        userid: localStorage.getItem('userID')
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Modal',
        origin: '',
        event: 'Menu Load',
        is_from: 'INSERT_MAKE_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getLaborGridTypes = (
  pProcess,
  oldGridtype,
  newGridtype,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_LABOR_MISSES_GRID_TYPES,
      variables: {
        pProcess: pProcess,
        oldGridtype: oldGridtype,
        newGridtype: newGridtype,
        userid: localStorage.getItem('userID')
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Modal',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_LABOR_MISSES_GRID_TYPES',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getTechProductivityAllRos = (techNo, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_TECH_PRODUCTIVITY_ALL_ROS,
      variables: {
        techno: techNo,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/TechnicianPerformance',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_TECH_PRODUCTIVITY_ALL_ROS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getLastThreeYears = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_LAST_THREE_YEARS,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/LaborWorkMixAnalysis',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_LAST_THREE_YEARS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      let threeYears = [];
      result.data.statelessCcAggregateGetLastThreeYears.nodes.map(val => {
        threeYears.push(val.monthYear);
      });

      callback(threeYears);
      // callback(result.data.dmsAggregateVwLastThirteenMonths.nodes);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const partsMatrixFileUpload = (
  base64Data,
  inFileName,
  fleetName,
  inInstallationDate,
  inPrtsource,
  inMatrixOrFleet,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: PARTS_MATRIX_FILE_UPLOAD,
      variables: {
        base64Data: base64Data,
        inFileName: inFileName,
        inMatrixType: fleetName,
        inInstallationDate: inInstallationDate,
        inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        inTenantId: localStorage.getItem('realm'),
        inPrtsource: inPrtsource,
        inCreatedUser: localStorage.getItem('userID'),
        inMatrixOrFleet: inMatrixOrFleet
        // inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      console.log('result====', result);
      const spanAttribute = {
        pageUrl: '/FleetAccounts',
        origin: '',
        event: 'Menu Load',
        is_from: 'PARTS_MATRIX_FILE_UPLOAD',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const gridTypeFileUpload = (
  base64Data,
  inFileName,
  inGridType,
  inInstallationDate,
  inGridOrFleet,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GRID_TYPE_FILE_UPLOAD,
      variables: {
        base64Data: base64Data,
        inFileName: inFileName,
        inGridTypes: inGridType,
        inInstallationDate: inInstallationDate,
        inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        inTenantId: localStorage.getItem('realm'),
        inCreatedUser: localStorage.getItem('userID'),
        inGridOrFleet: inGridOrFleet
        // inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      console.log('result====', result);
      const spanAttribute = {
        pageUrl: '/FleetAccounts',
        origin: '',
        event: 'Menu Load',
        is_from: 'GRID_TYPE_FILE_UPLOAD',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getMatrixDetailNew = (
  type,
  partSrc,
  payType,
  createdDate,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_MATRIX_DETAIL_NEW,
      variables: {
        gridormatrix: type,
        storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        payType: payType,
        partSrc: partSrc,
        createddate: createdDate
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsMatrix',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_MATRIX_DETAIL_NEW' + type,
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getMatrixTypeNew = (type, payType, partSrc, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_MATRIX_TYPE_NEW,
      variables: {
        gridormatrix: type,
        storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        payType: payType,
        partSrc: partSrc
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsMatrix',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_MATRIX_TYPE_NEW' + type,
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const insertFleetDetailsNew = (
  fleet_rate_grid,
  fleet_rate_parts,
  fleet_exists_labor_grid,
  fleet_exists_parts_matrix,
  callback
) => {
  const userID = localStorage.getItem('userID');
  apolloClientPostgresWrite
    .mutate({
      mutation: INSERT_FLEET_DETAILS_NEW,
      variables: {
        user_id: localStorage.getItem('userID'),
        fleet_rate_grid: fleet_rate_grid,
        fleet_rate_parts: fleet_rate_parts,
        fleet_exists_labor_grid: fleet_exists_labor_grid,
        fleet_exists_parts_matrix: fleet_exists_parts_matrix
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getPartsGridListNew = (
  type,
  payType,
  partSrc,
  // createdDate,
  pGridorpartsfor,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_PARTS_LIST_NEW,
      variables: {
        gridormatrix: type,
        storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        payType: payType,
        partSrc: partSrc,
        // createdDate: createdDate,
        pGridorpartsfor: pGridorpartsfor
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsMatrix',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_PARTS_LIST_NEW' + type,
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const partsMatrixFileUploadNew = (input, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: PARTS_MATRIX_FILE_UPLOAD_NEW,
      variables: {
        base64Data: input.base64Data,
        inFileName: input.inFileName,
        inMatrixType: input.inMatrixType,
        inInstallationDate: input.inInstallationDate,
        inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        inTenantId: localStorage.getItem('realm'),
        inPrtsource: input.inPrtsource,
        inCreatedUser: localStorage.getItem('userID'),
        inMatrixOrFleet: input.pMatrixOrFleet
        // inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      console.log('result====', result);
      const spanAttribute = {
        pageUrl: '/FleetAccounts',
        origin: '',
        event: 'Menu Load',
        is_from: 'PARTS_MATRIX_FILE_UPLOAD_NEW',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const gridTypeFileUploadNew = (
  base64Data,
  inFileName,
  inGridType,
  inInstallationDate,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GRID_TYPE_FILE_UPLOAD_NEW,
      variables: {
        base64Data: base64Data,
        inFileName: inFileName,
        inGridType: inGridType,
        inInstallationDate: inInstallationDate,
        inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        inTenantId: localStorage.getItem('realm'),
        inCreatedUser: localStorage.getItem('userID')
        // inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      console.log('result====', result);
      const spanAttribute = {
        pageUrl: '/FleetAccounts',
        origin: '',
        event: 'Menu Load',
        is_from: 'GRID_TYPE_FILE_UPLOAD_NEW',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const partsMatrixTypeNew = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: PARTS_MATRIX_TYPE_NEW,
      fetchPolicy: 'no-cache',
      variables: {
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      }
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/FleetAccounts',
        origin: '',
        event: 'Menu Load',
        is_from: 'PARTS_MATRIX_TYPE_NEW',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getPartsSourceNew = (pCallType, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_PARTS_SOURCE_NEW,
      fetchPolicy: 'no-cache',
      variables: {
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        pCallType: pCallType
      }
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/FleetAccounts',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_PARTS_SOURCE_NEW',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getAllGridForDetails = (type, storeId, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_GRIDORMETRIX_PAYTYPE_DETAILS,
      variables: {
        gridormatrix: type,
        storeId: storeId ? storeId : ''
        // : JSON.parse(localStorage.getItem('selectedStoreId'))
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/KpiReport',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_GRIDORMETRIX_PAYTYPE_DETAILS' + type,
        value: new Date() - start
      };
      traceSpan('Menu Load', spanAttribute);
      let resultArr = [];
      result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes.map(
        val => {
          resultArr.push(val);
        }
      );
      callback(resultArr);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getGridorMatrixPayTypesByGridType = (
  type,
  gridFor,
  storeId,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_GRIDORMETRIX_PAYTYPE_DETAILS_BY_GRID_TYPE,
      variables: {
        gridormatrix: type,
        pGridorpartsfor: gridFor,
        storeId: storeId ? storeId : ''
        // : JSON.parse(localStorage.getItem('selectedStoreId'))
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/KpiReport',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_GRIDORMETRIX_PAYTYPE_DETAILS_BY_GRID_TYPE' + type,
        value: new Date() - start
      };
      traceSpan('Menu Load', spanAttribute);
      let resultArr = [];
      result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes.map(
        val => {
          resultArr.push(val.gridormatrixtype);
        }
      );
      callback(resultArr);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getStoreNickName = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_STORE_NICKNAME,
      fetchPolicy: 'no-cache',
      variables: {
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      }
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/StoreSettings',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_STORE_NICKNAME',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getGridorMatrixMissesDetails = (type, storeId, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_GRIDORMETRIX_MISSES_DETAILS
          : GET_GRIDORMETRIX_PAYTYPE_DETAILS_OLD,
      variables: {
        gridormatrix: type,
        storeId: storeId ? storeId : ''
        // : JSON.parse(localStorage.getItem('selectedStoreId'))
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/KpiReport',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_GRIDORMETRIX_PAYTYPE_DETAILS' + type,
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      let resultArr = [];
      result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes.map(
        val => {
          resultArr.push(val.dipsGridorpartsfor);
        }
      );
      callback(resultArr);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getGridorMatrixPayTypeDetailsReport = (
  type,
  storeId,
  gridorpartsfor,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? GET_GRIDORMETRIX_PAYTYPE_DETAILS_REPORT
          : GET_GRIDORMETRIX_PAYTYPE_DETAILS_OLD,
      variables: {
        gridormatrix: type,
        storeId: storeId ? storeId : '',
        gridorpartsfor: gridorpartsfor
        // : JSON.parse(localStorage.getItem('selectedStoreId'))
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/KpiReport',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_GRIDORMETRIX_PAYTYPE_DETAILS' + type,
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      let resultArr = [];
      result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes.map(
        val => {
          resultArr.push(val.dipsGridorpartsfor);
        }
      );
      callback(resultArr);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getTechDrillDownAllRos = (
  queryMonth,
  techNo,
  payType,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_DRILLDOWN_TECH_RO_COUNT,
      variables: {
        argpaytype: payType,
        argtechno: techNo,
        argweekdate: queryMonth
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      console.log('Hasura------------>', result);
      const spanAttribute = {
        pageUrl: '/TechnicianPerformance',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_DRILLDOWN_TECH_RO_COUNT',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDataForOneLineRoDrilldown = (
  startDate,
  endDate,
  isMileageBelow,
  tech,
  advisor,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_DATA_FOR_ONE_LINE_RO_DRILLDOWN,
      variables: {
        startDate: startDate,
        endDate: endDate,
        isMileageBelow: isMileageBelow,
        tech: tech == 'All' ? null : tech,
        advisor: advisor == 'All' ? null : advisor
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getDataForMPIStatsDrilldown = (
  startDate,
  endDate,
  tech,
  advisor,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_MPI_STATS_DRILLDOWN_REPORT,
      variables: {
        startDate: startDate,
        endDate: endDate,
        tech: tech == 'All' ? null : tech,
        advisor: advisor == 'All' ? null : advisor,
        storeId: JSON.parse(localStorage.getItem('selectedStoreId')) || []
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const insertKPIReportName_1 = (
  reportName,
  filterStart,
  filterEnd,
  iKpiReportType,
  iKpiIds,
  iStoreId,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: INSERT_KPI_REPORT_NAME,
      variables: {
        iReportName: reportName,
        iKpiReportType: iKpiReportType,
        iKpiIds: iKpiIds,
        iStartDate: filterStart,
        iEndDate: filterEnd,
        iStoreId: iStoreId,
        iUserId: localStorage.getItem('userID')
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/KPIReportComparative',
        origin: '',
        event: 'Menu Load',
        is_from: 'INSERT_KPI_REPORT_NAME',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const insertKPIReportName = (
  process,
  reportName,
  filterStart,
  filterEnd,
  iKpiReportType,
  iKpiIds,
  iStoreId,
  userRole,
  visibilityStatus,
  filterBy,
  pSelectedGoals,
  pHiddenKpiSection,
  pHiddenKpiNumb,
  pKpiSortOrder,
  kpiSortedArray,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: INSERT_KPI_REPORT_NAME,
      variables: {
        pProcess: process,
        pReportName: reportName,
        pKpiReportType: iKpiReportType,
        pKpiIds: iKpiIds,
        pStoreId: iStoreId,
        pUserid: localStorage.getItem('userID'),
        pUserRole: userRole,
        pVisibility: visibilityStatus,
        pFilterBy: filterBy,
        pSelectedGoals: pSelectedGoals,
        pHiddenKpiSection: pHiddenKpiSection,
        pHiddenKpiNumb: pHiddenKpiNumb,
        pKpiSortOrder: pKpiSortOrder,
        pHeaderSortList: kpiSortedArray
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/KPIReportComparative',
        origin: '',
        event: 'Menu Load',
        is_from: 'INSERT_KPI_REPORT_NAME',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getKpiSavedReports = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_KPI_SAVED_REPORTS,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/SavedKpiReport',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_KPI_SAVED_REPORTS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);

      callback(result);
      // callback(result.data.dmsAggregateVwLastThirteenMonths.nodes);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getKpiReports = (reportName, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_KPI_REPORT,
      fetchPolicy: 'no-cache',
      variables: {
        iReportName: reportName
      }
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/SavedKpiReport',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_KPI_REPORT',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getEmail = (
  pNewMailId,
  pOldMailId,
  pProcess,
  pSource,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_EMAIL,
      fetchPolicy: 'no-cache',
      variables: {
        pNewMailId: pNewMailId,
        pOldMailId: pOldMailId,
        pProcess: pProcess,
        pSource: pSource,
        pStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        pUserid: localStorage.getItem('userID')
      }
    })
    .then(result => {
      console.log('result=email=', result);
      const spanAttribute = {
        pageUrl: '/Email',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_EMAIL',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);

      callback(result);
      // callback(result.data.dmsAggregateVwLastThirteenMonths.nodes);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getKpiSavedReportDetails = (
  pStoreId,
  pUserRole,
  pUserid,
  timezoneOffset,
  callback
) => {
  console.log(
    'getKpiSavedReportDetails',
    pStoreId,
    pUserRole,
    pUserid,
    timezoneOffset
  );
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_SAVED_REPORT_DETAILS,
      fetchPolicy: 'no-cache',
      variables: {
        pStoreId: pStoreId,
        pUserRole: pUserRole,
        pUserid: pUserid,
        timezoneOffset: timezoneOffset
      }
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/SavedKpiReport',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_SAVED_REPORT_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);

      callback(result);
      // callback(result.data.dmsAggregateVwLastThirteenMonths.nodes);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const deleteKPIReport = (
  process,
  reportName,
  iKpiReportType,
  iStoreId,
  userRole,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: DELETE_KPI_REPORT,
      variables: {
        pProcess: process,
        pReportName: reportName,
        pKpiReportType: iKpiReportType,
        pStoreId: iStoreId,
        pUserid: localStorage.getItem('userID'),
        pUserRole: userRole
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/KPIReportComparative',
        origin: '',
        event: 'Menu Load',
        is_from: 'DELETE_KPI_REPORT',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const sendSavedReportEmails = (
  process,
  storeId,
  reportName,
  reportType,
  mailStatus,
  mailFrequency,
  scheduledOn,
  recipientId,
  cc,
  bcc,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: SEND_SAVED_REPORT_EMAILS,
      variables: {
        pProcess: process,
        pStoreId: storeId,
        pReportName: reportName,
        pKpiReportType: reportType,
        pMailStatus: mailStatus,
        pMailFrequency: mailFrequency,
        pScheduledOn: scheduledOn,
        pRecipientId: recipientId,
        pCcId: cc,
        pBccId: bcc,
        pUserid: localStorage.getItem('userID')
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/ReportSaved',
        origin: '',
        event: 'Menu Load',
        is_from: 'SEND_SAVED_REPORT_EMAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getRecipientEmails = (
  process,
  storeId,
  reportName,
  reportType,
  mailStatus,
  mailFrequency,
  scheduledOn,
  recipientId,
  ccId,
  bccId,
  pOldRecipientId,
  callback
) => {
  console.log(
    'getRecipientEmails234567',
    process,
    storeId,
    reportName,
    reportType,
    mailStatus,
    mailFrequency,
    scheduledOn,
    recipientId,
    ccId,
    bccId,
    pOldRecipientId
  );
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_RECIPIENT_EMAILS,
      variables: {
        pProcess: process,
        pStoreId: storeId,
        pReportName: reportName,
        pKpiReportType: reportType,
        pMailStatus: mailStatus,
        pMailFrequency: mailFrequency,
        pScheduledOn: scheduledOn,
        pRecipientId: recipientId,
        pCcId: ccId,
        pBccId: bccId,
        pOldRecipientId: pOldRecipientId,
        pUserid: localStorage.getItem('userID')
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/ReportSaved',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_RECIPIENT_EMAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const fetchKpiScorecardPartsMatrix = (input, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_PARTSMATRIX_DETAILS,
      fetchPolicy: 'no-cache',
      variables: {
        pCallType: input.pCallType,
        pStore: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      }
    })
    .then(result => {
      callback(
        result.data.statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix.nodes
      );
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const GetGridTypeOptions = callback => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_GRID_TYPE_OPTIONS,
      fetchPolicy: 'no-cache',
      variables: {
        storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      }
    })
    .then(result => {
      callback(
        result.data.statelessCcPhysicalRwPartsMatrixTypeMasterDetails.nodes
      );
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const fetchKpiScorecardPartsMatrixDetails = (input, callback) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_MATRIX_DETAILS,
      fetchPolicy: 'no-cache',
      variables: {
        pCallType: input.pCallType,
        pStore: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        pMatrixType: input.pMatrixType,
        pCreatedDate: input.pCreatedDate,
        pPrtsource: input.pPrtsource
      }
    })
    .then(result => {
      const response = result.data.statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix.edges.map(
        edge => edge.node
      );
      callback(response);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const InsertOrUpdatePartsMatrix = (
  pCall,
  pStore,
  pNewPrtsource,
  pOldPrtsource,
  pMatrixType,
  pCreatedDate,
  pStoreInstallDate,
  pMatrix,
  pMatrixOrFleet,
  callback
) => {
  console.log('pMatrixType>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>', pMatrixType);
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: INSERT_OR_UPDATE_PARTSMATRIX,
      variables: {
        pCall: pCall,
        pStore: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        pNewPrtsource: pNewPrtsource,
        pOldPrtsource: pOldPrtsource,
        pMatrixType: pMatrixType,
        pCreatedDate: pCreatedDate,
        pStoreInstallDate: pStoreInstallDate,
        pMatrix: pMatrix ? JSON.stringify(pMatrix) : null,
        pUserId: localStorage.getItem('userID'),
        pMatrixOrFleet: pMatrixOrFleet
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(
        result.data
          .statelessDbdKpiScorecardInsertOrUpdateKpiScorecardPartsMatrix.results
      );
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const GetLaborGridList = (input, callback) => {
  const query = GET_LABOR_GRID_LIST(input.callType);
  apolloClientPostgres
    .query({
      query,
      fetchPolicy: 'no-cache',
      variables: {
        pCallType: input.callType,
        pStore: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        pGridType: input.gridType ? input.gridType : null,
        pCreatedDate: input.createdDate ? input.createdDate : null,
        pGridFor: input.gridFor ? input.gridFor : null
      }
    })
    .then(result => {
      const nodes =
        result.data.statelessDbdKpiScorecardBzoGetKpiScorecardLaborGrid.nodes;
      callback(nodes);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const createMatrixName = (input, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: CREATE_MATRIX_NAME,
      variables: {
        inCreatedUser: localStorage.getItem('userID'),
        inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        inTenantId: localStorage.getItem('realm'),
        inActivity: input.inActivity,
        inOldMatrixType: input.inOldMatrixType,
        inNewMatrixType: input.inNewMatrixType
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(
        result.data.statelessCcPhysicalRwInsertPartsMatrixTypeMasterDetails
          .results
      );
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const createOrUpdateGridName = (input, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: INSERT_OR_UPDATE_GRID_NAME,
      variables: {
        inActivity: input.inActivity,
        inCreatedUser: localStorage.getItem('userID'),
        inOldGridName: input.inOldGridName,
        inNewGridName: input.inNewGridName,
        inIsDefaultGridName: input.inIsDefaultGridName,
        inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        inTenantId: localStorage.getItem('realm')
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(
        result.data
          .statelessCcPhysicalRwInsertOrUpdateLaborGridTypeMasterDetails.results
      );
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const GetOpcodesList = callback => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: FETCH_OPCODE_LIST,
      variables: {
        inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result.data.statelessCcPhysicalRwGetRoOpcodesList.results);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const GetGridTypes = callback => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: FETCH_GRID_TYPES,
      variables: {
        storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(
        result.data.statelessCcPhysicalRwLaborGridTypeMasterDetails.nodes
      );
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const CreateGridWithDoorRate = (input, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: CREATE_GRID_WITH_DOOR_RATE,
      variables: {
        inCreatedDate: input.inCreatedDate,
        inGridTypes: input.inGridType,
        inDoorRate: input.inDoorRate,
        inGridFor: input.inGridFor,
        inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const data =
        result.data.statelessCcPhysicalRwInsertGriddataDtlMultiple.json;
      callback(JSON.parse(data));
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const InsertUpdateLaborGrid = (input, callback) => {
  const start = new Date();
  let newGridType =
    input.pCall === 'insert'
      ? { pNewGridTypes: input.pNewGridType }
      : { pNewGridType: input.pNewGridType };
  apolloClientPostgres
    .mutate({
      mutation:
        input.pCall === 'insert'
          ? INSERT_OR_UPDATE_LABOR_GRID
          : UPDATE_OR_DELETE_LABOR_GRID,
      variables: {
        ...newGridType,
        pStore: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        pOldGridType: input.pCall === 'insert' ? '' : input.pOldGridType,
        // pNewGridTypes: input.pNewGridType,
        pCreatedDate: input.createdDate,
        pStoreInstallDate: input.storeInstallDate,
        pGrid: input.gridData ? JSON.stringify(input.gridData) : input.gridData,
        pUserId: localStorage.getItem('userID'),
        pCall: input.pCall,
        pLaborMissType: input.pLaborMissType,
        pGridFor: input.pGridFor,
        pConfirmFlag: input.pConfirmFlag,
        pIsDefault: input.pIsDefault
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      if (input.pCall === 'insert') {
        const data =
          result.data
            .statelessDbdKpiScorecardInsertOrUpdateKpiScorecardLaborGridMultiple
            .json;
        callback(JSON.parse(data));
      } else {
        callback(
          result.data
            .statelessDbdKpiScorecardInsertOrUpdateKpiScorecardLaborGrid.results
        );
      }
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getRoHideRulesList = (inType, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_RO_HIDE_RULES_LIST,
      variables: {
        inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        inType: inType
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_RO_HIDE_RULES_LIST',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};
export const getRoHideRules = callback => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_RO_HIDE_RULES,
      variables: {
        inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_RO_HIDE_RULES',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};
export const editRoHideRules = (input, callback) => {
  console.log(
    'insert===',
    input.argId,
    input.inActive,
    input.inType,
    'input===',
    input
  );

  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: EDIT_RO_HIDE_RULES_LIST,
      variables: {
        argId: input.argId,
        inActive: input.inActive,
        inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        inUserid: localStorage.getItem('userID'),

        argRoleType: input.argRoleType,
        argActionName: input.argActionName,
        argActionValue: input.argActionValue,
        argExcepActionName: input.argExcepActionName,
        argExcepActionValue: input.argExcepActionValue,
        argExcepDateName: input.argExcepDateName,
        argExcepDateValueFrom: input.argExcepDateValueFrom,
        argExcepDateValueTo: input.argExcepDateValueTo,
        inType: input.inType,
        inTypeView: input.inTypeView
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/',
        origin: '',
        event: 'Menu Load',
        is_from: 'EDIT_RO_HIDE_RULES_LIST',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};
export const viewRoHide = (input, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: VIEW_RO_HIDE,
      variables: {
        argId: input.argId,
        inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        inUserid: localStorage.getItem('userID'),
        inType: input.inType
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/',
        origin: '',
        event: 'Menu Load',
        is_from: 'VIEW_RO_HIDE',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};
export const deleteRoHideRules = (selectedId, inType, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: DELETE_RO_HIDE_RULES,
      variables: {
        argId: selectedId,
        inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        inUserid: localStorage.getItem('userID'),
        inType: inType
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/',
        origin: '',
        event: 'Menu Load',
        is_from: 'DELETE_RO_HIDE_RULES',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};
// export const insertRoHideRules = (input, callback) => {
//   console.log(
//     'insert===',
//     input.fieldName,
//     input.status,
//     input.operatorValue,
//     input.operator,
//     'input===',
//     input
//   );
//   const start = new Date();
//   apolloClientPostgres
//     .mutate({
//       mutation: INSERT_RO_HIDE_RULES,
//       variables: {
//         inFeildName: input.fieldName,
//         inActive: input.status,
//         inOperandValue: input.operatorValue,
//         inOperator: input.operator,
//         inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
//         inUserid: localStorage.getItem('userID'),
//         inType: null
//       },
//       fetchPolicy: 'no-cache'
//     })
//     .then(result => {
//       const spanAttribute = {
//         pageUrl: '/',
//         origin: '',
//         event: 'Menu Load',
//         is_from: 'INSERT_RO_HIDE_RULES',
//         value: new Date() - start,
//         provenance: localStorage.getItem('provenance')
//       };
//       traceSpan('Menu Load', spanAttribute);
//       callback(result);
//     })
//     .catch(error => {
//       return error;
//     });
// };
export const insertRoHideRules = (input, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: INSERT_RO_HIDE_RULES,
      variables: {
        inFeildName: input.fieldName,
        inActive: input.status,
        inOperandValue: input.operatorValue,
        inOperator: input.operator,
        inStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        inUserid: localStorage.getItem('userID'),
        inType: null
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/',
        origin: '',
        event: 'Menu Load',
        is_from: 'INSERT_RO_HIDE_RULES',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};
export const getRevenueSummaryRevenueByPaytype = (
  queryMonth,
  optQueryMonth,
  department,
  serviceAdvisor,
  callback
) => {
  const start = new Date();
  if (serviceAdvisor != 'All') {
    apolloClientPostgres
      .query({
        query: GET_DETAIL_SUMMARY_DATA,
        variables: {
          currMonth: queryMonth,
          optMonth: optQueryMonth,
          department: department,
          serviceAdvisor: serviceAdvisor
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/RevenueSummary',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_DETAIL_SUMMARY_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(result);
      })
      .catch(error => {
        if (error.graphQLErrors) {
          error.graphQLErrors.map(({ message, locations, path }) =>
            console.log(
              `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
            )
          );
        }
        return error;
      });
  } else {
    apolloClientPostgres
      .query({
        query: GET_DETAIL_SUMMARY_DATA,
        variables: {
          currMonth: queryMonth,
          optMonth: optQueryMonth,
          department: department
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/RevenueSummary',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_DETAIL_SUMMARY_DATA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(result);
      })
      .catch(error => {
        if (error.graphQLErrors) {
          error.graphQLErrors.map(({ message, locations, path }) =>
            console.log(
              `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
            )
          );
        }
        return error;
      });
  }
};
export const getDrillDownDataForRevenueSummary = (
  queryMonth,
  department,
  payType,
  startdate,
  enddate,
  serviceAdvisor,
  callback
) => {
  if (serviceAdvisor != 'All') {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_REVENUE_DETAILS_DRILL_DOWN_SA,
        variables: {
          department: department,
          enddate: enddate,
          monthyear: queryMonth,
          startdate: startdate
          // payType: payType,
          // serviceAdvisor: serviceAdvisor
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/AnalyzeData',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_REVENUE_DETAILS_DRILL_DOWN_SA',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(result);
      })
      .catch(error => {
        if (error.graphQLErrors) {
          error.graphQLErrors.map(({ message, locations, path }) =>
            console.log(
              `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
            )
          );
        }
        return error;
      });
  } else {
    const start = new Date();
    apolloClientPostgres
      .mutate({
        mutation: GET_REVENUE_DETAILS_DRILL_DOWN,
        variables: {
          department: department,
          enddate: enddate,
          monthyear: queryMonth,
          startdate: startdate
        },
        fetchPolicy: 'no-cache'
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/AnalyzeData',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_REVENUE_DETAILS_DRILL_DOWN',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        callback(result);
      })
      .catch(error => {
        if (error.graphQLErrors) {
          error.graphQLErrors.map(({ message, locations, path }) =>
            console.log(
              `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
            )
          );
        }
        return error;
      });
  }
};
export const getRevenueSummaryWarrantyVolumesLaborNew = (
  queryMonth,
  optQueryMonth,

  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_REVENUE_SUMMARY_WARRANTY_VOLUMES_LABOR_NEW,
      variables: {
        currMonth: queryMonth,
        optMonth: optQueryMonth,
        storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/RevenueSummary',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_REVENUE_SUMMARY_WARRANTY_VOLUMES_LABOR_NEW',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getRevenueSummaryWarrantyVolumesPartsNew = (
  queryMonth,
  optQueryMonth,

  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_REVENUE_SUMMARY_WARRANTY_VOLUMES_PARTS_NEW,
      variables: {
        currMonth: queryMonth,
        optMonth: optQueryMonth,
        storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/RevenueSummary',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_REVENUE_SUMMARY_WARRANTY_VOLUMES_PARTS_NEW',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getRoHideColumns = callback => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_RO_HIDE_COLUMNS,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_RO_HIDE_COLUMNS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};
export const getRoHideOprators = callback => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_RO_HIDE_OPERATOR,
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_RO_HIDE_OPERATOR',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      return error;
    });
};

export const getMonthlyClientReportCardDetails = (
  measuredMTH,
  priorMTH,
  storeIds,
  callback
) => {
  apolloClientPostgres
    .mutate({
      mutation: GET_MONTHLY_CLIENT_REPORT_CARD_DETAILS,
      variables: {
        measuredMon: measuredMTH,
        priorMon: priorMTH,
        storeids: storeIds
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.error(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const getThreeMonthClientReportCardDetails = (
  startDate,
  endDate,
  lastmonth,
  storeIds,
  callback
) => {
  apolloClientPostgres
    .mutate({
      mutation: GET_THREE_MONTH_CLIENT_REPORT_CARD_DETAILS,
      variables: {
        userselStartDate: startDate,
        userselEndDate: endDate,
        lastmonth: lastmonth,
        storeIds: storeIds
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.error(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getOneMonthReportMonths = (storeid, callback) => {
  const start = new Date();
  makeApolloClientPostgres
    .query({
      query: GET_ONEMONTH_REPORT_MONTHS,
      variables: {
        storeId: storeid
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/oneMonthReport',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_ONEMONTH_REPORT_MONTHS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const enableMfaStatus = (username, realmName, status, callback) => {
  apolloClientPostgres
    .mutate({
      mutation:
        status == true ? ENABLE_USER_MFA_STATUS : DISABLE_USER_MFA_STATUS,
      variables: {
        username: username,
        realmName: realmName
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const insertKPIMonthCardReportName = (
  pProcess,
  pStoreId,
  pReportName,
  pToggleDuration,
  pStartdate,
  pEnddate,
  pPrevMonthyear,
  pCurrentMonthyear,
  pUserid,
  pKpiReportType,
  pKpiIds,
  pVisibility,
  pUserRole,
  pWorkmixView,
  pStoreSelected,
  pRoiView,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: INSERT_KPI_MONTH_CARD_REPORT_NAME,
      variables: {
        pProcess: pProcess,
        pStoreId: pStoreId,
        pReportName: pReportName,
        pToggleDuration: pToggleDuration,
        pStartdate: pStartdate,
        pEnddate: pEnddate,
        pPrevMonthyear: pPrevMonthyear,
        pCurrentMonthyear: pCurrentMonthyear,
        pUserid: pUserid,
        pKpiReportType: pKpiReportType,
        pKpiIds: pKpiIds,
        pVisibility: pVisibility,
        pUserRole: pUserRole,
        pWorkmixView: pWorkmixView,
        pStoreSelected: pStoreSelected,
        pRoiView: pRoiView
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/OneMonthReport',
        origin: '',
        event: 'Menu Load',
        is_from: 'INSERT_KPI_MONTH_CARD_REPORT_NAME',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getKpiSavedReportCardDetails = (
  pStoreId,
  pUserRole,
  pUserid,
  timezoneOffset,
  callback
) => {
  console.log(
    'getKpiSavedReportDetails',
    pStoreId,
    pUserRole,
    pUserid,
    timezoneOffset
  );
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_SAVED_REPORT_CARD_DETAILS,
      fetchPolicy: 'no-cache',
      variables: {
        pStoreId: pStoreId,
        pUserRole: pUserRole,
        pUserid: pUserid,
        timezoneOffset: timezoneOffset
      }
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/SavedKpiReport',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_SAVED_REPORT_CARD_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);

      callback(result);
      // callback(result.data.dmsAggregateVwLastThirteenMonths.nodes);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getStoreRetialFlags = callback => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_STORE_RETAIL_FLAGS,
      fetchPolicy: 'no-cache',
      variables: {
        pStoreId: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        inPayType: 'C'
      }
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/Home',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_STORE_RETAIL_FLAGS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);

      callback(result);
      // callback(result.data.dmsAggregateVwLastThirteenMonths.nodes);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const insertMpiStatsReportName = (
  pProcess,
  pStoreId,
  pReportName,
  pUserid,
  pReportType,
  pVisibility,
  pUserRole,
  pSortColumns,
  pFilterColumns,
  pTech,
  pAdvisor,
  pDisplayColumns,
  pDisplayOrder,
  pFilterBy,
  callback
) => {
  console.log('filetebyyyy', pFilterBy);
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: INSERT_MPI_STATS_REPORT_NAME,
      variables: {
        pProcess: pProcess,
        pStoreId: pStoreId,
        pReportName: pReportName,
        pUserid: pUserid,
        pReportType: pReportType,
        pVisibility: pVisibility,
        pUserRole: pUserRole,
        pSortColumns: pSortColumns,
        pFilterColumns: pFilterColumns,
        pTech: pTech,
        pAdvisor: pAdvisor,
        pDisplayColumns: pDisplayColumns,
        pDisplayOrder: pDisplayOrder,
        pFilterBy: pFilterBy
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/MPIStats',
        origin: '',
        event: 'Menu Load',
        is_from: 'INSERT_MPI_STATS_REPORT_NAME',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const insertLaborMissesSavededReport = (
  pProcess,
  pStoreId,
  pReportName,
  pFilterBy,
  pUserid,
  pReportType,
  pVisibility,
  pUserRole,
  pSortColumns,
  pFilterColumns,
  pTech,
  pDisplayColumns,
  pGridType,
  pJobType,
  pPaytype,
  pDisplayOrder,
  pAdvisor,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: INSERT_LABOR_MISSES_REPORT,
      variables: {
        pProcess: pProcess,
        pStoreId: pStoreId,
        pReportName: pReportName,
        pFilterBy: pFilterBy,
        pUserid: pUserid,
        pReportType: pReportType,
        pVisibility: pVisibility,
        pUserRole: pUserRole,
        pSortColumns: pSortColumns,
        pFilterColumns: pFilterColumns,
        pTech: pTech,
        pDisplayColumns: pDisplayColumns,
        pGridType: pGridType,
        pJobType: pJobType,
        pPaytype: pPaytype,
        pDisplayOrder: pDisplayOrder,
        pAdvisor: pAdvisor
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/LaborMisses',
        origin: '',
        event: 'Menu Load',
        is_from: 'INSERT_LABOR_MISSES_REPORT',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const insertPartsMissesSavededReport = (
  pProcess,
  pStoreId,
  pReportName,
  pFilterBy,
  pUserid,
  pReportType,
  pVisibility,
  pUserRole,
  pSortColumns,
  pFilterColumns,
  pTech,
  pDisplayColumns,
  pGridType,
  pJobType,
  pPaytype,
  pDisplayOrder,
  pAdvisor,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: INSERT_PARTS_MISSES_REPORT,
      variables: {
        pProcess: pProcess,
        pStoreId: pStoreId,
        pReportName: pReportName,
        pFilterBy: pFilterBy,
        pUserid: pUserid,
        pReportType: pReportType,
        pVisibility: pVisibility,
        pUserRole: pUserRole,
        pSortColumns: pSortColumns,
        pFilterColumns: pFilterColumns,
        pTech: pTech,
        pDisplayColumns: pDisplayColumns,
        pGridType: pGridType,
        pJobType: pJobType,
        pPaytype: pPaytype,
        pDisplayOrder: pDisplayOrder,
        pAdvisor: pAdvisor
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/LaborMisses',
        origin: '',
        event: 'Menu Load',
        is_from: 'INSERT_PARTS_MISSES_REPORT',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};

export const insertKpiOnelineSavedReport = (
  pProcess,
  pStoreId,
  pReportName,
  pFilterBy,
  pReportType,
  pAdvisor,
  pTech,
  pUserid,
  pUserRole,
  pVisibility,
  pDisplayColumns,
  pSortColumns,
  pFilterColumns,
  pDisplayOrder,
  pIsMileageBelow,
  pOnelineType,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: INSERT_KPI_ONELINE_SAVED_REPORT,
      variables: {
        pProcess: pProcess,
        pStoreId: pStoreId,
        pReportName: pReportName,
        pFilterBy: pFilterBy,
        pReportType: pReportType,
        pAdvisor: pAdvisor,
        pTech: pTech,
        pUserid: pUserid,
        pUserRole: pUserRole,
        pVisibility: pVisibility,
        pDisplayColumns: pDisplayColumns,
        pSortColumns: pSortColumns,
        pFilterColumns: pFilterColumns,
        pDisplayOrder: pDisplayOrder,
        pIsMileageBelow: pIsMileageBelow,
        pOnelineType: pOnelineType
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/KpiOneline', //ToDO: Need to set it dynamically
        origin: '',
        event: 'Menu Load',
        is_from: 'INSERT_KPI_ONELINE_SAVED_REPORT', //ToDO: Need to set it dynamically
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDataForLaborOverviewCharts = (advisors, chartIds, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_CP_LABOR_OVERVIEW_DATA,
      variables: {
        advisor: advisors,
        chartIds: chartIds
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/CPLaborOverview',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_CP_LABOR_OVERVIEW_DATA',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(
        result.data.statelessDbdCpLaborCpLaborOverview
          .statelessDbdCpLaborRevenueByYears
      );
    })
    .catch(error => {
      return error;
    });
};
export const getDataForPartsOverviewCharts = (advisors, chartIds, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_CP_PARTS_OVERVIEW_DATA,
      variables: {
        advisor: advisors,
        chartIds: chartIds
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/CPLaborOverview',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_CP_PARTS_OVERVIEW_DATA',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(
        result.data.statelessDbdCpPartsCpPartsOverview
          .statelessDbdCpPartsRevenueByYears
      );
    })
    .catch(error => {
      return error;
    });
};
export const getDataForSummaryOverviewCharts = (advisors, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_CP_SUMMARY_OVERVIEW_DATA,
      variables: {
        advisor: advisors,
        chartIds: 'All'
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/CPLaborOverview',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_CP_SUMMARY_OVERVIEW_DATA',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(
        result.data.statelessDbdCpOverviewCpSummaryOverview
          .statelessDbdCpOverviewRevenueByYears
      );
    })
    .catch(error => {
      return error;
    });
};
export const getDrillGraphDataForMonthWiseComparisonLaborAll = (
  parameters,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_COMPARISON_DATA_MONTHWISE_LABOR_ALL,
      variables: {
        mon1: parameters[0],
        mon2: parameters[1],
        chartIds: parameters[2]
        // store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/LaborWorkMixAnalysis',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_COMPARISON_DATA_MONTHWISE_LABOR_ALL',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getServiceEfficiencyByMonthsAll = (
  mon1,
  mon2,
  chartIds,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_SERVICE_EFFICIENCY_BY_MONTHS_ALL,
      variables: {
        mon1: mon1,
        mon2: mon2,
        chartIds: chartIds,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/ServiceAdvisorPerformance',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_SERVICE_EFFICIENCY_BY_MONTHS_ALL',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDrillGraphDataForMonthWiseComparisonPartsAll = (
  parameters,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .query({
      query: GET_COMPARISON_DATA_MONTHWISE_PARTS_ALL,
      variables: {
        mon1: parameters[0],
        mon2: parameters[1],
        chartIds: parameters[2]
        // store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/PartsWorkMixAnalysis',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_COMPARISON_DATA_MONTHWISE_PARTS_ALL',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getServiceAdvisorEfficiencyByOpCategoryAll = (
  mon,
  chartIds,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_SERVICE_ADVISOR_EFFICIENCY_BY_OPCATEGORY_ALL,
      variables: {
        mon: mon,
        chartIds: chartIds,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/ServiceAdvisorPerformance',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_SERVICE_ADVISOR_EFFICIENCY_BY_OPCATEGORY_ALL',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getTechnicianRevenueAll = (mon1, mon2, chartIds, callback) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_TECHNICIAN_REVENUE_AND_HOURS_ALL,
      variables: {
        mon1: mon1,
        mon2: mon2,
        chartIds: chartIds,
        store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/TechnicianPerformance',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_TECHNICIAN_REVENUE_AND_HOURS_ALL',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(result);
    })
    .catch(error => {
      if (error.graphQLErrors) {
        error.graphQLErrors.map(({ message, locations, path }) =>
          console.log(
            `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
          )
        );
      }
      return error;
    });
};
export const getDataForSpecialMetricsCharts = (
  advisors,
  chartIds,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_SPECIAL_METRICS_CHART_DATA,
      variables: {
        advisor: advisors,
        chartIds: chartIds
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/SpecialMetrics',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_SPECIAL_METRICS_CHART_DATA',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(
        result.data.statelessDbdSpecialMetricsGetJobRoCountSinglelineMultiline
          .singlejobRoCounts
      );
    })
    .catch(error => {
      return error;
    });
};
export const getDataForSpecialMetricsChartsSummary = (
  advisors,
  chartIds,
  callback
) => {
  const start = new Date();
  apolloClientPostgres
    .mutate({
      mutation: GET_SPECIAL_METRICS_SUMMARY_DATA,
      variables: {
        advisor: advisors,
        chartIds: chartIds
      },
      fetchPolicy: 'no-cache'
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/SpecialMetrics',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_SPECIAL_METRICS_SUMMARY_DATA',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      callback(
        result.data.statelessDbdSpecialMetricsGetServiceAdvisorOperationSummary
          .laborSoldHoursByPaytypeServiceAdvisors
      );
    })
    .catch(error => {
      return error;
    });
};
