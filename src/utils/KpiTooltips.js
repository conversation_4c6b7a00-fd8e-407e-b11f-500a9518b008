import React, { memo, useState } from 'react';
import {
  Typography
} from '@material-ui/core';

export default function getTooltipConentKPI(chartId, classes, gridTitle) {
    if(gridTitle == 'Int') {
        gridTitle = 'Internal';
    }
    switch(chartId) {
      case 1341: 
        return (
        <React.Fragment>
            <Typography className={classes.tooltipContent}>
                <span>For the period selected and/or Service Advisor selected from the filtering options:</span><br/>
                <span className={classes.valTooltip}>
                <ul class="tooltipUL"> 
                    <li class="parentListKpi">
                        <span>Labor Sales $ Per RO</span>&nbsp;
                        <span className={classes.dotSeparationTooltip}>&bull;</span>&nbsp;
                        <span>GP $ Per RO</span>&nbsp;
                        <span className={classes.dotSeparationTooltip}>&bull;</span>&nbsp;
                        <span>GP % Per RO</span><br/>
                    </li>
                    <li class="parentListKpi">
                        <span>Total Labor Sales $</span>&nbsp;
                        <span className={classes.dotSeparationTooltip}>&bull;</span>&nbsp;
                        <span>Total Labor GP $</span>
                    </li>
                </ul>
                </span>
            </Typography>
        </React.Fragment>
       ) 
    case 1342: 
        return (
        <React.Fragment>
            <Typography className={classes.tooltipContent}>
                <span>For the period selected and/or Service Advisor selected from the filtering options:</span><br/>
                <span className={classes.valTooltip}>
                <ul class="tooltipUL"> 
                    <li class="parentListKpi">
                        <span>Parts Sales $ Per RO</span>&nbsp;
                        <span className={classes.dotSeparationTooltip}>&bull;</span>&nbsp;
                        <span>GP $ Per RO</span>&nbsp;
                        <span className={classes.dotSeparationTooltip}>&bull;</span>&nbsp;
                        <span>GP % Per RO</span><br/>
                    </li>
                    <li class="parentListKpi">
                        <span>Total Parts Sales $</span>&nbsp;
                        <span className={classes.dotSeparationTooltip}>&bull;</span>&nbsp;
                        <span>Total Parts GP $</span>
                    </li>
                </ul>
                </span>
            </Typography>
        </React.Fragment>
       ) 
       case 1343: 
        return (
        <React.Fragment>
            <Typography className={classes.tooltipContent}>
                <span>For the period selected and/or Service Advisor selected from the filtering options:</span><br/>
                <span className={classes.valTooltip}>
                <ul class="tooltipUL"> 
                    <li class="parentListKpi">
                        <span>Labor & Parts Sales $ Per RO</span>&nbsp;
                        <span className={classes.dotSeparationTooltip}>&bull;</span>&nbsp;
                        <span> Labor & Parts GP $ Per RO</span><br/>
                    </li>
                    <li class="parentListKpi">
                        <span>Total Labor & Parts Sales $</span>&nbsp;
                        <span className={classes.dotSeparationTooltip}>&bull;</span>&nbsp;
                        <span>Total Labor & Parts GP $</span>
                    </li>
                </ul>
                </span>
            </Typography>
        </React.Fragment>
       ) 
       case 1339: 
        return (
        <React.Fragment>
            <Typography className={classes.tooltipContent}>
                <span>For the period selected and/or Service Advisor selected from the filtering options:</span><br/>
                <span className={classes.valTooltip}>
                <ul class="tooltipUL"> 
                    <li class="parentListKpi">
                        <span>All Sold Hours (CP</span>
                        <span className={classes.dotSeparationTooltip}>&bull;</span>                         
                        <span>Wty</span>
                        <span className={classes.dotSeparationTooltip}>&bull;</span>
                        <span>Int)</span>&nbsp;
                        <span className={classes.dotSeparationTooltip}>&bull;</span>&nbsp;
                        <span>Avg. Hrs Per Day</span>&nbsp;
                        <span className={classes.dotSeparationTooltip}>&bull;</span>&nbsp;
                        <span>CP Hrs Per RO</span>
                    </li>
                </ul>
                </span>
            </Typography>
        </React.Fragment>
      ) 
      case 1337:
        return (
        <React.Fragment>
            <Typography className={classes.tooltipContent}>
                <span>For the period selected and/or Service Advisor selected from the filtering options:</span><br/>
                <span className={classes.valTooltip}>
                <ul class="tooltipUL"> 
                    <li class="parentListKpi">
                        <span>The count of CP 1-line ROs under 60k miles, then the % of total CP ROs under 60k miles.</span>
                    </li>
                    <li class="parentListKpi">
                        <span>Average labor sales $, average parts sales $, and then the total of the two.</span><br/>
                    </li>
                    <li class="parentListKpi">
                        <span>Same as above, but for multi-line CP ROs.</span>
                    </li>
                </ul>
                </span>
            </Typography>
        </React.Fragment>
        )
        case 1346: 
        return (
            <React.Fragment>
                <Typography className={classes.tooltipContent}>
                    <span>For the period selected and/or Service Advisor selected from the filtering options:</span><br/>
                    <span className={classes.valTooltip}>
                    <ul class="tooltipUL"> 
                        <li class="parentListKpi">
                            <span>How many times your {gridTitle} repair pricing policy should have been used</span>&nbsp;
                            <span className={classes.dotSeparationTooltip}>&bull;</span>&nbsp;                         
                            <span>How many were missed</span>&nbsp;
                            <span className={classes.dotSeparationTooltip}>&bull;</span>&nbsp;
                            <span>% of non-compliance.</span>
                        </li>
                    </ul>
                    </span>
                </Typography>
            </React.Fragment>
        ) 
        case 1353: 
        return (
            <React.Fragment>
                <Typography className={classes.tooltipContent}>
                    <span>For the period selected and/or Service Advisor selected from the filtering options:</span><br/>
                    <span className={classes.valTooltip}>
                    <ul class="tooltipUL"> 
                        <li class="parentListKpi">
                            <span>How many times your {gridTitle} Parts matrix should have been used</span>&nbsp;
                            <span className={classes.dotSeparationTooltip}>&bull;</span>&nbsp;                         
                            <span>How many were missed</span>&nbsp;
                            <span className={classes.dotSeparationTooltip}>&bull;</span>&nbsp;
                            <span>% of non-compliance.</span>
                        </li>
                    </ul>
                    </span>
                </Typography>
            </React.Fragment>
        ) 
        case 1338:
        return (
        <React.Fragment>
            <Typography className={classes.tooltipContent}>
                <span>For the period selected and/or Service Advisor selected from the filtering options:</span><br/>
                <span className={classes.valTooltip}>
                <ul class="tooltipUL"> 
                    <li class="parentListKpi">
                        <span>The count of CP 1-line ROs over 60k miles, then the % of total CP ROs over 60k miles.</span>
                    </li>
                    <li class="parentListKpi">
                        <span>Average labor sales $, average parts sales $, and then the total of the two.</span><br/>
                    </li>
                    <li class="parentListKpi">
                        <span>Same as above, but for multi-line CP ROs.</span>
                    </li>
                </ul>
                </span>
            </Typography>
        </React.Fragment>
        )
        case 1336:
        return (
        <React.Fragment>
            <Typography className={classes.tooltipContentBlock3}>
                <span>For the period selected and/or Service Advisor selected from the filtering options:</span><br/>
                <span className={classes.valTooltip}>
                <ul class="tooltipUL"> 
                    <li class="parentListKpi">
                        <span>All ROs per day average (for all pay types) and the % of the total share of all ROs.</span>
                        <li class="childListKpi">
                            <span>Example:  If you have 3 Advisors and they all wrote the exact same amount of ROs, 
                                but you have only selected 1 Advisor, their share would be 33% (rounded). This gives 
                                you a real look at the count and % this share this person is writing (how many real 
                                vehicles they are touching, because each RO is only counted once).
                            </span>
                        </li>
                        <li class="childListKpi">
                            <span>Example:  If you have selected “Total Shop” (all Advisors) the share % will always = 100%.</span>
                        </li>
                    </li>
                </ul>
                </span>
            </Typography>
        </React.Fragment>
        )
        case 1335:
        return (
        <React.Fragment>
            <Typography className={classes.tooltipContentBlock3}>
                <span>For the period selected and/or Service Advisor selected from the filtering options:</span><br/>
                <span className={classes.valTooltip}>
                <ul class="tooltipUL"> 
                    <li class="parentListKpi">
                        <span>Each RO is counted once only and prioritized by highest value of Customer Pay work first, 
                            then Warranty, then Internal, and then the total of the three.  We use “Unique” because we 
                            only count each RO once, producing a true count.
                        </span>
                        <li class="childListKpi">
                            <span>Example:  If an RO has a CP line it’s counted as a CP RO, regardless of any other work 
                                (Wty or Int). 
                            </span>
                        </li>
                        <li class="childListKpi">
                            <span>Example:  If an RO has a Wty line (no CP lines), it’s counted as a Wty RO 
                                (but it may have Int lines).
                            </span>
                        </li>
                        <li class="childListKpi">
                            <span>Example:  If an RO has Int only (no CP or Wty) it’s counted as an Int RO.</span>
                        </li>
                    </li>
                </ul>
                </span>
            </Typography>
        </React.Fragment>
        )
        case 1344:
        return (
        <React.Fragment>
            <Typography className={classes.tooltipContent}>
                <span>For the period selected and/or Service Advisor selected from the filtering options:</span><br/>
                <span className={classes.valTooltip}>
                <ul class="tooltipUL"> 
                    <li class="parentListKpi">
                        <span>% of Comp., Maint and Repair work sold.</span>
                    </li>
                    <li class="parentListKpi">
                        <span>Sales $ for Comp., Maint., and Repair work sold.</span>
                    </li>
                </ul>
                </span>
            </Typography>
        </React.Fragment>
        )
        case 1340:
        return (
        <React.Fragment>
            <Typography className={classes.tooltipContent}>
                <span>For the period selected and/or Service Advisor selected from the filtering options:</span><br/>
                <span className={classes.valTooltip}>
                <ul class="tooltipUL"> 
                    <li class="parentListKpi">
                        <span>Cp and Wty vehicles average age (rounded to the nearest ½ year) with their avg. miles.</span>
                    </li>
                </ul>
                </span>
            </Typography>
        </React.Fragment>
        )
        case 1351:
        return (
        <React.Fragment>
            <Typography className={classes.tooltipContent}>
                <span>For the period selected and/or Service Advisor selected from the filtering options:</span><br/>
                <span className={classes.valTooltip}>
                <ul class="tooltipUL"> 
                    <li class="parentListKpi">
                        <span>The count of CP 1-line ROs for all miles, then the % of total CP ROs for all miles.</span>
                    </li>
                    <li class="parentListKpi">
                        <span>Average labor sales $, average parts sales $, and then the total of the two.</span><br/>
                    </li>
                    <li class="parentListKpi">
                        <span>Same as above, but for multi-line CP ROs.</span>
                    </li>
                </ul>
                </span>
            </Typography>
        </React.Fragment>
        )
    }
  }
