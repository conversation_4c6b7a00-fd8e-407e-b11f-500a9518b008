import moment from 'moment';
import { GET_ALL_CHART_DETAILS } from 'src/graphql/queries';
import ApolloClient from 'apollo-boost';
import makeApolloClient from './apolloRootClientPostgres';
import { tr } from 'react-dom-factories';
import { Chart } from 'chart.js';
import { getYear } from 'date-fns';
import { chart } from 'highcharts';
import { TRUE } from 'sass';
import { traceSpan } from 'src/utils/OTTTracing';
var lodash = require('lodash');

function getYearLegend(value, realm) {
  realm = realm ? realm : localStorage.getItem('realm');
  var closedDate = localStorage.getItem('closedDate').split('-')[0];

  var year;
  switch (value.trim()) {
    case 'present':
      year = closedDate;
      break;
    case 'past':
      year = closedDate - 1;
      break;
    case 'previous':
      year = closedDate - 2;
      break;
    default:
      year = closedDate;
      break;
  }
  return year;
}
export function getVerificationDashboardBaseURL() {
  return process.env.REACT_APP_VERIFICATION_ENDPOINT;
}

// 0-4 for Technician  & 5-9 for Service Advisor
function getAgGridChartTitle(chartId) {
  var title;
  switch (chartId) {
    case 0:
      title = 'Revenue vs Technician';
      break;
    case 1:
      title = 'Gross Profit vs Technician';

      break;
    case 2:
      title = 'Labor Sold Hours /RO  vs Technician';
      break;
    case 3:
      title = 'Job Counts vs Technician';

      break;
    case 4:
      title = 'Gross Profit % vs Technician';

      break;
    case 5:
      title = 'Revenue vs Service Advisor';
      break;
    case 6:
      title = 'Gross Profit vs Service Advisor ';
      break;
    case 7:
      title = 'Labor Sold Hours /RO vs Service Advisor';
      break;
    case 8:
      title = 'Job Counts vs Service Advisor';
      break;
    case 9:
      title = 'Gross Profit % vs Service Advisor';
      break;

    default:
      title = '';
  }

  return title;
}

function getAgGridChartColumnData(chartId) {
  var columData;
  switch (chartId) {
    case 0:
      columData = ['lbrsale', 'prtssale', 'personname'];
      break;
    case 1:
      columData = ['laborprofit', 'partsprofit', 'personname'];
      break;
    case 2:
      columData = ['lbrsoldhours', 'personname'];

      break;
    case 3:
      columData = ['jobcount', 'personname'];
      break;
    case 4:
      columData = ['laborprofitpercent', 'partsprofitpercent', 'personname'];
      break;
    case 5:
      columData = ['lbrsale', 'prtssale', 'personname'];
      break;
    case 6:
      columData = ['laborprofit', 'partsprofit', 'personname'];
      break;
    case 7:
      columData = ['lbrsoldhours', 'personname'];

      break;
    case 8:
      columData = ['jobcount', 'personname'];
      break;
    case 9:
      columData = ['laborprofitpercent', 'partsprofitpercent', 'personname'];
      break;

    default:
      columData = [];
  }

  return columData;
}

export function getAgGridChartOptions(chartId, chartContainer) {
  var params = {
    cellRange: {
      columns: getAgGridChartColumnData(chartId)
    },
    chartType: 'groupedColumn',
    chartPalette: 'bright',
    chartContainer: chartContainer,
    processChartOptions: function(params) {
      var opts = params.options;
      opts.title.enabled = true;
      opts.title.text = getAgGridChartTitle(chartId);
      opts.seriesDefaults.fill.colors = getColorScheme(1);
      opts.seriesDefaults.stroke.colors = getColorScheme(1);
      opts.legend = {
        position: 'bottom',
        align: 'center'
        // labels: {
        //   boxWidth: 12
        // }
      };

      return opts;
    }
  };
  return params;
}
export function getDataGridConfigurationKPI(gridKey) {
  // if(gridKey == 0 ){
  //   gridKey = {
  //     x: getXValue(gridKey),
  //     y: getYValue(gridKey),
  //     w: 4,
  //     h: 2,
  //     minW: 3,
  //     minH: 1,
  //     // isResizable: true,
  //     preventCollision: true
  //   };
  // } else {
  //   gridKey = {
  //     x: getXValueSpecialMetrics(gridKey),
  //     y: getYValue(gridKey),
  //     w: 6,
  //     h: 2,
  //     minW: 5,
  //     minH: 1,
  //     isResizable: true
  //   };
  // }
  gridKey = {
    x: getXValueSpecialMetrics(gridKey),
    y: getYValue(gridKey),
    w: 6,
    h: 2,
    minW: 5,
    minH: 1,
    isResizable: true
  };
  return gridKey;
  // return (gridKey = {
  //   x: getXValue(gridKey),
  //   y: getYValue(gridKey),
  //   w: 4,
  //   h: 2,
  //   minW: 3,
  //   minH: 1,
  //   // isResizable: true,
  //   preventCollision: true
  // });
}
export function getDataGridConfiguration(gridKey) {
  return (gridKey = {
    x: getXValue(gridKey),
    y: getYValue(gridKey),
    w: 6,
    h: 7,
    minW: 5,
    minH: 7,
    // isResizable: true,
    preventCollision: true
  });
}
export function getDataGridConfigurationFav(gridKey) {
  return (gridKey = {
    x: getXValue(gridKey),
    y: getYValue(gridKey),
    w: 6,
    h: 5,
    minW: 5,
    minH: 5,
    // isResizable: true,
    preventCollision: true
  });
}
export function getDataGridConfigurationFavOneRow(gridKey) {
  return (gridKey = {
    x: getXValue(gridKey),
    y: getYValue(gridKey),
    w: 12,
    h: 10,
    minW: 12,
    minH: 10,
    // isResizable: true,
    preventCollision: true
  });
}
export function getDataGridConfigurationCommon(gridKey) {
  return (gridKey = {
    x: getXValue(gridKey),
    y: getYValue(gridKey),
    w: 4,
    h: 7,
    minW: 3,
    minH: 5,
    //  isResizable: true,
    preventCollision: true
  });
}
export function getDataGridConfigurationTwoRow(gridKey) {
  return (gridKey = {
    x: getXValueTwoRow(gridKey),
    y: getYValue(gridKey),
    w: 6,
    h: 7,
    minW: 3,
    minH: 5,
    // isResizable: true,
    preventCollision: true
  });
}
export function getDataGridConfigurationSpecialMetrics(gridKey) {
  return (gridKey = {
    x: getXValueSpecialMetrics(gridKey),
    y: getYValue(gridKey),
    w: 6,
    h: 10,
    minW: 5,
    minH: 9,
    isResizable: true
  });
}
export function getDataGridConfigTotalOpportunity(gridKey) {
  return (gridKey = {
    x: 3,
    y: 0,
    w: 6,
    h: 8,
    minW: 6,
    minH: 8,
    isResizable: true
  });
}
function getXValue(value) {
  if (value % 2 == 0) return 0;
  if (value % 2 == 1) return 6;
  //if (value % 3 == 2) return 8;
}
// function getXValue(value) {
//   if (value % 3 == 0) return 0;
//   if (value % 3 == 1) return 4;
//   if (value % 3 == 2) return 8;
// }
function getXValueTwoRow(value) {
  if (value % 2 == 0) return 0;
  if (value % 2 == 1) return 6;
}
function getXValueSpecialMetrics(value) {
  if (value % 2 == 0) return 0;
  if (value % 2 == 1) return 6;
  // if (value % 3 == 2) return 8;
}
function getYValue(value) {
  return parseInt(value / 3) * 7;
}

export function getDataGridConfigurationForDetails(gridKey, parentId, chartId) {
  return (gridKey = {
    x: getXValueForDetails(gridKey),
    y: getYValueForDetails(gridKey, chartId),
    w:
      chartId == '1345' ||
      parentId == '1345' ||
      chartId == '1352' ||
      parentId == '1352' ||
      chartId == '1363' ||
      parentId == '1363'
        ? 12
        : 6,

    h: 7,
    minW: 5,
    minH: 7,
    isResizable: true,
    preventCollision: true,
    isBounded: true
  });
}

function getXValueForDetails(value) {
  if (value % 2 == 0) return 0;
  if (value % 2 == 1) return 6;
}

function getYValueForDetails(value) {
  return parseInt(value / 2) * 6;
}

export function getColorScheme(chartType, chartId) {
  let COLORS_SERIES;
  switch (chartType) {
    case 0:
      COLORS_SERIES = ['#109618', '#DC3912', '#3366CC'];
      break;
    case 1:
      COLORS_SERIES = ['#3366CC', '#DC3912', '#109618'];
      break;
    case 2:
      COLORS_SERIES = ['#109618', '#DC3912', '#3366CC'];
      break;
    case 3:
      COLORS_SERIES = ['#109618', '#DC3912', '#3366CC'];
      break;
    case 8: //oppor
      COLORS_SERIES = ['#ffcc00', 'rgb(248, 102, 36)', '#3366CC'];

      break;
    case 9:
      COLORS_SERIES = [
        '#DC3912',
        '#5c5c8a',
        '#993399',
        '#e67300',
        '#3366CC',
        '#cc0044'
      ];
      break;

    case 10:
      COLORS_SERIES = ['#DD4477', '#66AA00', '#109618', '#22AA99'];
      break;
    case 13:
      //  COLORS_SERIES = ['#0099C6', '#FF9900', '#DD4477'];
      COLORS_SERIES = [
        'rgb(102, 46, 155)',
        'rgb(248, 102, 36)',
        'rgb(249, 200, 14)',
        '#009933'
      ];
      break;
      break;
    case 16:
      COLORS_SERIES = ['#994499', '#FF9900', '#DD4477'];
      break;
    case 20:
      chartId == 1239
        ? (COLORS_SERIES = COLORS_SERIES = ['#DC3912', '#3366CC', '#993399'])
        : (COLORS_SERIES = ['#FF9900', '#109618', '#3366CC', '#DD4477']);
      break;
    case 21:
      COLORS_SERIES = [
        'rgb(102, 46, 155)',
        'rgb(248, 102, 36)',
        'rgb(249, 200, 14)'
      ];
      break;
    case 22:
      COLORS_SERIES = ['#3366CC', '#109618', '#FF9900'];
      break;
    default:
      COLORS_SERIES = ['#109618', '#DC3912', '#3366CC'];
      break;
  }
  return COLORS_SERIES;
}
export function getLayoutConfiguration(key, viewId) {
  let ls = {};
  if (global.localStorage) {
    try {
      ls = JSON.parse(global.localStorage.getItem(viewId)) || {};
    } catch (e) {
      /*Ignore*/
    }
  }
  return ls[key];
}

/**
 *
 * @param {*} resultSet cube js resultSet data
 * @param {*} chartType
 *  1 for Chart without 3 year data
 *  2 & 3 for charts with year as legend
 */
export function getDataforLineChart(
  resultSet,
  chartType,
  specialColor,
  chartId
) {
  const data = {
    labels: resultSet.length > 0 && resultSet[0].labels,
    datasets:
      resultSet.length > 0 &&
      resultSet[0].datasets &&
      resultSet[0].datasets.map((dataset, index) => ({
        label: dataset.label,
        data: dataset.data,
        borderColor:
          chartId == 1316
            ? '#dc3912'
            : chartId == 1317
            ? '#993399'
            : chartId == 921
            ? getColorScheme(13, chartId)[index]
            : chartId == 923 || chartId == 1354 || chartId == 1355
            ? getColorScheme(11)[index]
            : specialColor == undefined
            ? getColorScheme(chartType, chartId)[index]
            : specialColor,
        backgroundColor:
          chartId == 1316
            ? '#dc3912'
            : chartId == 1317
            ? '#993399'
            : chartId == 921
            ? getColorScheme(13, chartId)[index]
            : chartId == 923 || chartId == 1354 || chartId == 1355
            ? getColorScheme(11)[index]
            : specialColor == undefined
            ? getColorScheme(chartType, chartId)[index]
            : specialColor,
        fill: true,
        type: getChartRenderType(chartType),
        lineTension: 0,
        borderWidth: getBorderWidth(chartType, index),
        pointRadius: 1.5
      }))
  };

  return data;
}

function getLegend3yrChart() {
  var closedDate = localStorage.getItem('closedDate').split('-')[0];
  var legend = [
    {
      label: closedDate,
      backgroundColor: '#109618'
    },
    {
      label: closedDate - 1,
      backgroundColor: '#DC3912'
    },
    {
      label: closedDate - 2,
      backgroundColor: '#3366CC'
    }
  ];
  return legend;
}
export function getDbdName(chartId) {
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(item => item.chartId == chartId);
  const dashboard = filteredResult.length > 0 && filteredResult[0].dbdName;
  return dashboard;
}
function getBorderWidth(chartType, index) {
  let borderWidth = 1.5;
  if (chartType == 21 && index == 1) {
    borderWidth = 2;
  } else if (chartType == 21 && index == 2) {
    borderWidth = 2.5;
  }
  return borderWidth;
}
function getChartLabel(chartId) {
  switch (chartId) {
    case 942:
      return 'Revenue';
    case 939:
      return 'Gross Profit';
    case 920:
      return 'Labor Sold Hours';
    case 1111:
    case 1164:
    case 1165:
      return 'Total Discount';
    case 1113:
      return 'RO';
    case 1123:
      return 'Discounted RO %';
    case 1115:
      return 'Total CP Sale';
    case 1232:
      return 'Discounted Sale';
    case 948:
      return 'CP 1-Line RO Count';
    case 1354:
      return 'CP Multi-Line RO Count';
    case 1234:
    case 1233:
    case 1236:
    case 1237:
      return ['Labor Discount'];
    case 1235:
      return ['Labor Sale %'];
    case 1239:
      return 'Shop Supplies';
    case 1101:
      return 'Add On Job Count';
    case 1102:
      return 'Percentage';
    case 1104:
      return 'Add On RO Count';
  }
}
export function getYAxisLabelforPostgraphileCharts(chartId, resultSet) {
  if (resultSet.length > 0) {
    return resultSet[0].labels && resultSet[0].labels.map(val => val);
  }
}
export function getYAxisLabelforHasuraCharts(chartId, resultSet) {
  if (
    chartId == '940' ||
    chartId == '946' ||
    // chartId == '1072' ||
    // chartId == '967' ||
    // chartId == '1097' ||
    chartId == '937' ||
    chartId == '988' ||
    chartId == '987' ||
    chartId == '986' ||
    chartId == '1066' ||
    chartId == '1128' ||
    chartId == '1129' ||
    chartId == '1130' ||
    chartId == '1131' ||
    chartId == '1132' ||
    chartId == '949' ||
    chartId == '1076' ||
    chartId == '1077' ||
    chartId == '1078' ||
    chartId == '1079' ||
    chartId == '1083' ||
    chartId == '953' ||
    chartId == '1012' ||
    chartId == '1015' ||
    chartId == '1014' ||
    chartId == '1013' ||
    chartId == '1084' ||
    chartId == '1085' ||
    chartId == '1086' ||
    chartId == '1087' ||
    chartId == '1088' ||
    chartId == '1089' ||
    chartId == '923' ||
    chartId == '1355' ||
    chartId == '930' ||
    chartId == '935' ||
    chartId == '936' ||
    chartId == '938' ||
    chartId == '1176' ||
    chartId == '1177' ||
    chartId == '1178' ||
    chartId == '1179' ||
    chartId == '1180' ||
    chartId == '1181' ||
    chartId == '1182' ||
    chartId == '1183' ||
    chartId == '1184' ||
    chartId == '1185' ||
    chartId == '1186' ||
    chartId == '1187' ||
    chartId == '1188' ||
    chartId == '1189' ||
    chartId == '1197' ||
    chartId == '1198' ||
    chartId == '1199' ||
    chartId == '1200' ||
    chartId == '1201' ||
    chartId == '1202' ||
    chartId == '1203' ||
    chartId == '1319' ||
    chartId == '1320' ||
    chartId == '1321' ||
    chartId == '1322' ||
    chartId == '1323' ||
    chartId == '1324' ||
    chartId == '1325' ||
    chartId == '1211' ||
    chartId == '1212' ||
    chartId == '1213' ||
    chartId == '1214' ||
    chartId == '1215' ||
    chartId == '1216' ||
    chartId == '1217' ||
    chartId == '1218' ||
    chartId == '1219' ||
    chartId == '1220' ||
    chartId == '1221' ||
    chartId == '1222' ||
    chartId == '1223' ||
    chartId == '1224' ||
    chartId == '931' ||
    chartId == '926' ||
    chartId == '921'
  ) {
    return resultSet.map(val =>
      new Date(val.rodate).toISOString().replace('Z', '')
    );
  }
  // if (
  //   (chartId == '1067' ||
  //     chartId == '1068' ||
  //     chartId == '941' ||
  //     chartId == '1069' ||
  //     chartId == '1070' ||
  //     chartId == '1071' ||
  //     chartId == '1072 ' ||
  //     chartId == '967' ||
  //     chartId == '1097') &&
  //   resultSet.length > 0
  // ) {
  //   return resultSet[0].labels.map(val => val);
  // }
  if (chartId == '1357') {
    return resultSet.map(val =>
      new Date(val.roDate).toISOString().replace('Z', '')
    );
  }
  if (
    chartId == '942' ||
    chartId == '1073' ||
    chartId == '1127' ||
    chartId == '1098' ||
    chartId == '1356' ||
    chartId == '966' ||
    chartId == '916' ||
    chartId == '1091' ||
    chartId == '1092' ||
    chartId == '1092' ||
    chartId == '1094' ||
    chartId == '955' ||
    chartId == '1044' ||
    chartId == '1318' ||
    chartId == '1225' ||
    chartId == '1226' ||
    chartId == '1227' ||
    chartId == '1228' ||
    chartId == '1229' ||
    chartId == '1230' ||
    chartId == '1231' ||
    chartId == '1238' ||
    chartId == '1334'
  ) {
    return resultSet.map(val =>
      new Date(val.closeddate).toISOString().replace('Z', '')
    );
  }
  if (chartId == '1174' || chartId == '1175') {
    return resultSet.map(val =>
      new Date(val.month_year).toISOString().replace('Z', '')
    );
  }
  if (chartId == '1316' || chartId == '1317') {
    return resultSet.map(val =>
      new Date(val.monthYear).toISOString().replace('Z', '')
    );
  }
}

export function getDataforLineChartFromHasura(
  resultSet,
  chartType,
  specialColor,
  chartId
) {
  const data = {
    labels: getYAxisLabelforHasuraCharts(chartId, resultSet),
    datasets: getHasuraChartMeasures(chartId).map((measures, index) => ({
      label: getChartLegendsforHasuraCharts(chartId)[index],
      data: resultSet.map(r => r[measures]),
      borderColor:
        specialColor == undefined
          ? getColorScheme(chartType, chartId)[index]
          : specialColor,
      fill: false,
      type: getChartRenderType(chartType),
      lineTension: 0,
      borderWidth: 1.5,
      pointRadius: 1.5
    }))
  };

  if (
    chartType == 8 ||
    chartType == 9 ||
    chartType == 10 ||
    chartType == 11 ||
    chartType == 12 ||
    chartType == 13 ||
    chartType == 16 ||
    chartType == 17 ||
    chartType == 20
  ) {
    data.datasets.map(function(s, index) {
      s.backgroundColor =
        specialColor == undefined
          ? getColorScheme(chartType, chartId)[index]
          : chartId == 923 || chartId == 1355
          ? getColorScheme(11)[index]
          : specialColor;
    });
  }

  return data;
}

export function getDataforLineChartFromPostgraphile(
  resultSet,
  chartType,
  specialColor,
  chartId,
  opt
) {
  var closedDate =
    localStorage.getItem('closedDate') &&
    localStorage.getItem('closedDate').split('-')[0];
  var yearArr = [closedDate, closedDate - 1, closedDate - 2];
  var dashboardName = getDbdName(chartId);
  const data = {
    labels: resultSet.length > 0 && resultSet[0].labels,
    datasets:
      resultSet.length > 0 &&
      resultSet[0].datasets &&
      resultSet[0].datasets.map((dataset, index) => ({
        label: dataset.label.includes('Gross Profit Percentage')
          ? dataset.label.replace('Gross Profit Percentage', 'Gross Profit %')
          : dashboardName === 'CP Labor Overview' ||
            dashboardName === 'CP Parts Overview'
          ? yearArr[index]
          : dataset.label,
        data: dataset.data,
        borderColor:
          specialColor == undefined
            ? getColorScheme(chartType, chartId)[index]
            : specialColor,
        fill: false,
        type: getChartRenderType(chartType),
        lineTension: 0,
        borderWidth: 1.5,
        pointRadius: 1.5
      }))
  };

  if (
    chartType == 8 ||
    chartType == 9 ||
    chartType == 10 ||
    chartType == 11 ||
    chartType == 12 ||
    chartType == 13 ||
    chartType == 16 ||
    chartType == 17 ||
    chartType == 20
  ) {
    data.datasets.map(function(s, index) {
      s.backgroundColor =
        specialColor == undefined
          ? getColorScheme(chartType, chartId)[index]
          : chartId == 923 || chartId == 1355
          ? getColorScheme(11)[index]
          : specialColor;
    });
  }

  return data;
}
// export function getDataforLineChartFromHasura(
//   resultSet,
//   chartType,
//   specialColor,
//   chartId
// ) {
//   const data = {
//     labels: getYAxisLabelforHasuraCharts(chartId, resultSet),
//     datasets: getHasuraChartMeasures(chartId).map((measures, index) => ({
//       label: getChartLegendsforHasuraCharts(chartId)[index],
//       data: resultSet.map(r => r[measures]),
//       borderColor:
//         specialColor == undefined
//           ? getColorScheme(chartType)[index]
//           : specialColor,
//       fill: false,
//       type: getChartRenderType(chartType),
//       lineTension: 0,
//       borderWidth: 1.5,
//       pointRadius: 1.5
//     }))
//   };

//   if (
//     chartType == 8 ||
//     chartType == 9 ||
//     chartType == 10 ||
//     chartType == 11 ||
//     chartType == 12 ||
//     chartType == 13 ||
//     chartType == 16 ||
//     chartType == 17 ||
//     chartType == 20
//   ) {
//     data.datasets.map(function(s, index) {
//       s.backgroundColor =
//         specialColor == undefined
//           ? getColorScheme(chartType)[index]
//           : chartId == 923 || chartId == 1355
//           ? getColorScheme(11)[index]
//           : specialColor;
//     });
//   }

//   return data;
// }
export function getChartLegendsforPostgraphileCharts(data, chartId) {
  let legendArr = [];
  if (data) {
    data.map(item => {
      if (Number(item.chartId) == chartId) {
        legendArr = [item.label];
      }
    });
  }
  return legendArr;
}
export function getChartLegendsforHasuraCharts(chartId) {
  if (chartId == '940') {
    return [
      'Labor Gross Profit %',
      'Parts Gross Profit %',
      'Combined Gross Profit %'
    ];
  }
  if (chartId == '1068') {
    return ['Combined Revenue'];
  }
  if (chartId == '941') {
    return ['Labor Revenue'];
  }
  if (chartId == '1067') {
    return ['Parts Revenue'];
  }
  if (chartId == '946') {
    return ['Effective Labor Rate'];
  }
  if (chartId == '1072') {
    return ['Combined Gross Profit Percentage'];
  }
  if (chartId == '967') {
    return ['Labor Gross Profit Percentage'];
  }
  if (chartId == '1097') {
    return ['Parts Gross Profit Percentage'];
  }
  if (chartId == '937') {
    return ['Effective Labor Rate'];
  }
  if (chartId == '988') {
    return ['Effective Labor Rate'];
  }
  if (chartId == '987') {
    return ['Effective Labor Rate'];
  }
  if (chartId == '986') {
    return ['Effective Labor Rate'];
  }
  if (chartId == '1066') {
    return ['Effective Labor Rate'];
  }
  if (
    chartId == '1176' ||
    chartId == '1177' ||
    chartId == '1178' ||
    chartId == '1179' ||
    chartId == '1180' ||
    chartId == '1181' ||
    chartId == '1182' ||
    chartId == '1183' ||
    chartId == '1184' ||
    chartId == '1185' ||
    chartId == '1186' ||
    chartId == '1187' ||
    chartId == '1188' ||
    chartId == '1189'
  ) {
    return ['Effective Labor Rate'];
  }
  if (
    chartId == '1197' ||
    chartId == '1198' ||
    chartId == '1199' ||
    chartId == '1200' ||
    chartId == '1201' ||
    chartId == '1202' ||
    chartId == '1203'
  ) {
    return ['Labor Hours Per RO'];
  }
  if (
    chartId == '1211' ||
    chartId == '1212' ||
    chartId == '1213' ||
    chartId == '1214' ||
    chartId == '1215' ||
    chartId == '1216' ||
    chartId == '1217'
  ) {
    return ['Average Labor Sales Per RO'];
  }
  if (
    chartId == '1218' ||
    chartId == '1219' ||
    chartId == '1220' ||
    chartId == '1221' ||
    chartId == '1222' ||
    chartId == '1223' ||
    chartId == '1224'
  ) {
    return ['Parts Revenue Per RO'];
  }
  if (
    chartId == '1225' ||
    chartId == '1226' ||
    chartId == '1227' ||
    chartId == '1228' ||
    chartId == '1229' ||
    chartId == '1230' ||
    chartId == '1231' ||
    chartId == '1238'
  ) {
    return ['Parts Markup'];
  }
  if (
    chartId == '1073' ||
    chartId == '1127' ||
    chartId == '1098' ||
    chartId == '1356' ||
    chartId == '966' ||
    chartId == '916' ||
    chartId == '953' ||
    chartId == '955' ||
    chartId == '1044' ||
    chartId == '1318' ||
    chartId == '1334'
  ) {
    var closedDate = localStorage.getItem('closedDate').split('-')[0];
    // return [closedDate - 2, closedDate - 1, closedDate];
    return [closedDate, closedDate - 1, closedDate - 2];
  }
  if (chartId == '1128') {
    return ['Effective labor rate'];
  }
  if (chartId == '1129') {
    return ['Effective labor rate'];
  }
  if (chartId == '1130') {
    return ['Effective labor rate'];
  }
  if (chartId == '1131') {
    return ['Effective labor rate'];
  }
  if (chartId == '1132') {
    return ['Effective labor rate'];
  }
  if (chartId == '949') {
    return ['Labor Hour Per RO'];
  }
  if (chartId == '1076') {
    return ['Repair'];
  }
  if (chartId == '1077') {
    return ['Maintenance'];
  }
  if (chartId == '1078') {
    return ['Competitive'];
  }
  if (chartId == '1083') {
    return ['Competitive', 'Repair', 'Maintenance', 'Shop Supplies'];
  }
  if (chartId == '1079') {
    return ['Competitive', 'Repair', 'Maintenance'];
  }

  if (chartId == '1012') {
    return ['Parts Revenue per RO'];
  }
  if (chartId == '1015') {
    return ['Parts Revenue per RO'];
  }
  if (chartId == '1014') {
    return ['Parts Revenue per RO'];
  }
  if (chartId == '1013') {
    return ['Parts Revenue per RO'];
  }
  if (chartId == '1091') {
    return ['Parts Markup'];
  }
  if (chartId == '1092') {
    return ['Parts Markup'];
  }
  if (chartId == '1093') {
    return ['Parts Markup'];
  }
  if (chartId == '1094') {
    return ['Parts Markup'];
  }
  if (chartId == '1084') {
    return ['Labor Sale Per RO'];
  }
  if (chartId == '1085') {
    return ['Labor Sale Per RO'];
  }
  if (chartId == '1086') {
    return ['Labor Sale Per RO'];
  }
  if (chartId == '1087') {
    return ['Labor Sale Per RO'];
  }
  if (chartId == '1088') {
    return ['Labor Sale Per RO'];
  }
  if (chartId == '923') {
    return ['Mileage Under 60K', 'Mileage Over 60K', 'Total Shop'];
  }
  if (chartId == '1355') {
    return ['Mileage Under 60K', 'Mileage Over 60K', 'Total Shop'];
  }
  if (chartId == '930') {
    return [' Parts to Labor Ratio'];
  }
  if (chartId == '935') {
    return [
      'Customer Pay',
      'Extended Service',
      'Internal',
      'Maintenance',
      'Warranty',
      'Factory Service Contract'
    ];
  }
  if (chartId == '1357') {
    return [
      'Customer Pay',
      'Extended Service',
      'Internal',
      'Maintenance',
      'Warranty',
      'Factory Service Contract'
    ];
  }
  if (chartId == '936') {
    return ['Competitive', 'Maintenance', 'Repair'];
  }
  if (chartId == '938') {
    return ['12 months Return Rate', '6 Months Return Rate'];
  }
  if (chartId == '1174') {
    return ['Average Hours Sold Per Technician'];
  }
  if (chartId == '1175') {
    return ['Average Sales Per Technician'];
  }
  if (chartId == '1316') {
    return ['MPI Penetration Percentage'];
  }
  if (chartId == '1317') {
    return ['Menu Penetration Percentage'];
  }
  if (
    chartId == '1319' ||
    chartId == '1320' ||
    chartId == '1321' ||
    chartId == '1322' ||
    chartId == '1323' ||
    chartId == '1324' ||
    chartId == '1325'
  ) {
    return ['Parts Hours Per RO'];
  }
  if (chartId == '931' || chartId == '926') {
    return ['Hours Per RO', 'Gross Profit%', 'Joint Effect'];
  }
  if (chartId == '921') {
    return ['Competitive', 'Maintenance', 'Repair', 'Total'];
  }
  if (chartId == '1359') {
    return ['Combined'];
  }
  if (chartId == '1360') {
    return ['Customer Pay'];
  }
  if (chartId == '1361') {
    return ['Warranty'];
  }
  if (chartId == '1362') {
    return ['Internal'];
  }
}

export function isAdvisorenabledCharts(query) {
  var charts = [
    'drillDown',
    '942',
    '940',
    '939',
    '920',
    '946',
    '925',
    '960',
    '944',
    '1073',
    '1133',
    '1127',
    '1044',
    '1318',
    '1098',
    '1356',
    '1138',
    '918',
    '956',
    '955',
    '1049',
    '952',
    '966',
    '953',
    '1143',
    '916',
    '936',
    '1357',
    '930',
    '935',
    '938',
    '923',
    '948',
    '1354',
    '1355',
    '1174',
    '1175',
    '1326',
    '1239',
    '1240',
    '1241',
    '1242',
    '1334',
    '1316',
    '1317',
    '1090',
    '1096',
    '931',
    '926',
    '921',
    '1238',
    '1276',
    '1277',
    '1278',
    '1279',
    '1280',
    '1281',
    '1282',
    '1283',
    '1284',
    '1285',
    '1286',
    '1315',
    '1359',
    '1360',
    '1361',
    '1362'
  ];
  if (query) {
    let chartId = query.split('?chartId=').pop();
    return charts.includes(chartId);
  } else return false;
}
export function getPostgraphileChartMeasures(chartId) {
  return [chartId];
}

export function getHasuraChartMeasures(chartId) {
  if (chartId == '940') {
    return [
      'laborgrossprofitpercentage',
      'partsgrossprofitpercentage',
      'combinedgrossprofitpercentage'
    ];
  }
  if (chartId == '1068' || chartId == '1072 ') {
    return ['Labor Gross Profit'];
  }
  if (chartId == '941' || chartId == '967') {
    return ['Parts Gross Profit'];
  }
  if (chartId == '1067' || chartId == '1097') {
    return ['Combined Gross Profit'];
  }
  if (chartId == '942') {
    return ['laborRevenue', 'partsRevenue', 'combined'];
  }
  if (chartId == '946') {
    return ['effectiveLbrRate'];
  }
  if (chartId == '1072') {
    return ['combinedgrossprofitpercentage'];
  }
  if (chartId == '967') {
    return ['laborgrossprofitpercentage'];
  }
  if (chartId == '1097') {
    return ['partsgrossprofitpercentage'];
  }
  if (chartId == '937') {
    return ['elrCombined'];
  }
  if (chartId == '988') {
    return ['elrRepair'];
  }
  if (chartId == '987') {
    return ['elrMaintenance'];
  }
  if (chartId == '986') {
    return ['elrRepairCompetitive'];
  }
  if (chartId == '1066') {
    return ['elrCompetitive'];
  }
  if (chartId == '1073') {
    // return ['lgpyearbeforeprevious', 'lgppreviousyear', 'lgpcurrentyear'];
    return ['lgpcurrentyear', 'lgppreviousyear', 'lgpyearbeforeprevious'];
  }
  if (chartId == '1127') {
    return [
      'effectiveLbrRateCurrentyear',
      'effectiveLbrRatePreviousyear',
      'effectiveLbrRatePastyear'
    ];
  }
  if (chartId == '1098') {
    return ['repairCurrentyear', 'repairPreviousyear', 'repairPastyear'];
  }
  if (chartId == '1356') {
    return [
      'competitivemaintenanceCurrentyear',
      'competitivemaintenancePreviousyear',
      'competitivemaintenancePastyear'
    ];
  }
  if (chartId == '966') {
    return [
      'partsgrossprofitpercentageCurrentyear',
      'partsgrossprofitpercentagePreviousyear',
      'partsgrossprofitpercentagePastyear'
    ];
  }
  if (chartId == '916') {
    return [
      'partsMarkupCurrentyear',
      'partsMarkupPreviousyear',
      'partsMarkupPastyear'
    ];
  }
  if (chartId == '1128') {
    return ['elrCombined'];
  }
  if (chartId == '1129') {
    return ['elrRepair'];
  }
  if (chartId == '1130') {
    return ['elrMaintenance'];
  }
  if (chartId == '1131') {
    return ['elrCompetitive'];
  }
  if (chartId == '1132') {
    return ['elrRepairCompetitive'];
  }
  if (chartId == '949') {
    return ['hoursPerRepairOrder'];
  }
  if (chartId == '1076') {
    return ['hoursPerRepairOrderRepair'];
  }
  if (chartId == '1077') {
    return ['hoursPerRepairOrderMaintenance'];
  }
  if (chartId == '1078') {
    return ['hoursPerRepairOrderCompet'];
  }
  if (chartId == '1083') {
    return [
      'ro_countcompetitive',
      'ro_countrepair',
      'ro_countmaintenance',
      'ro_countshopsupplies'
    ];
  }
  if (chartId == '1079') {
    return [
      'hoursPerRepairOrderCompet',
      'hoursPerRepairOrderRepair',
      'hoursPerRepairOrderMaintenance'
    ];
  }
  if (chartId == '953') {
    return [
      'partsRevenuePerRoCurrentyear',
      'partsRevenuePerRoPreviousyear',
      'partsRevenuePerRoPastyear'
    ];
  }
  if (chartId == '1012') {
    return ['parts_revenue_per_ro'];
  }
  if (chartId == '1015') {
    return ['parts_revenue_per_ro_repair'];
  }
  if (chartId == '1014') {
    return ['parts_revenue_per_ro_maintenance'];
  }
  if (chartId == '1013') {
    return ['parts_revenue_per_ro_competitive'];
  }
  if (chartId == '1091') {
    return ['parts_markup_all'];
  }
  if (chartId == '1092') {
    return ['parts_markup_repair'];
  }
  if (chartId == '1093') {
    return ['parts_markup_maintenance'];
  }
  if (chartId == '1094') {
    return ['parts_markup_competitive'];
  }
  if (chartId == '955') {
    return [
      'salePerRepairOrderCurrentyear',
      'salePerRepairOrderPreviousyear',
      'salePerRepairOrderPastyear'
    ];
  }
  if (chartId == '1084') {
    return ['averageratebycategory'];
  }
  if (chartId == '1085') {
    return ['repair'];
  }
  if (chartId == '1086') {
    return ['maintenance'];
  }
  if (chartId == '1087') {
    return ['competitive'];
  }
  if (chartId == '1088') {
    return ['shopsupplies'];
  }
  if (chartId == '1044') {
    return [
      'hoursPerRepairOrderCurrentyear',
      'hoursPerRepairOrderPreviousyear',
      'hoursPerRepairOrderPastyear'
    ];
  }
  if (chartId == '1318') {
    return [
      'hoursPerRepairOrderCurrentyear',
      'hoursPerRepairOrderPreviousyear',
      'hoursPerRepairOrderPastyear'
    ];
  }
  if (chartId == '923') {
    return ['mileageBlw60KPerc', 'mileageAbv60KPerc', 'percentage'];
  }
  if (chartId == '1355') {
    return ['mileageBlw60KPerc', 'mileageAbv60KPerc', 'percentage'];
  }
  if (chartId == '930') {
    return ['partstolaborratio'];
  }
  if (chartId == '935') {
    return [
      'lbrsldhrscustomerpay',
      'lbrsldhrsextended',
      'lbrsldhrsinternal',
      'lbrsldhrsmaintenance',
      'lbrsldhrswarranty',
      'lbrsldhrfactoryservicecontract'
    ];
  }
  if (chartId == '1357') {
    return [
      'avgWorkingDaysCustomerpay',
      'avgWorkingDaysExtended',
      'avgWorkingDaysInternal',
      'avgWorkingDaysMaintenance',
      'avgWorkingDaysWarranty',
      'avgWorkingDaysFactorysc'
    ];
  }
  if (chartId == '936') {
    return ['rtiocompetitive', 'rtiomaintenance', 'rtiorepair'];
  }
  if (chartId == '938') {
    return ['twelveMonthReturnrate', 'sixMonthReturnrate'];
  }
  if (chartId == '1175') {
    return ['averagesalepertech'];
  }
  if (chartId == '1174') {
    return ['averagehrspertech'];
  }
  if (chartId == '1176') {
    return ['elrCombined'];
  }
  if (chartId == '1177') {
    return ['elrCustomerpay'];
  }
  if (chartId == '1178') {
    return ['elrWarranty'];
  }
  if (chartId == '1179') {
    return ['elrInternal'];
  }
  if (chartId == '1180') {
    return ['elrMaintenanceplan'];
  }
  if (chartId == '1181') {
    return ['elrExtendedservice'];
  }
  if (chartId == '1182') {
    return ['elrFactoryservice'];
  }
  if (chartId == '1183') {
    return ['elrCombined'];
  }
  if (chartId == '1184') {
    return ['elrCustomerpay'];
  }
  if (chartId == '1185') {
    return ['elrWarranty'];
  }
  if (chartId == '1186') {
    return ['elrInternal'];
  }
  if (chartId == '1187') {
    return ['elrMaintenanceplan'];
  }
  if (chartId == '1188') {
    return ['elrExtendedservice'];
  }
  if (chartId == '1189') {
    return ['elrFactoryservice'];
  }
  if (chartId == '1197') {
    return ['hoursPerRepairOrder'];
  }
  if (chartId == '1198') {
    return ['hoursPerRepairOrderCustomerpay'];
  }
  if (chartId == '1199') {
    return ['hoursPerRepairOrderWarranty'];
  }
  if (chartId == '1200') {
    return ['hoursPerRepairOrderInternal'];
  }
  if (chartId == '1201') {
    return ['hoursPerRepairOrderMaintenance'];
  }
  if (chartId == '1202') {
    return ['hoursPerRepairOrderExtendedservice'];
  }
  if (chartId == '1203') {
    return ['hoursPerRepairOrderFactoryservice'];
  }
  if (chartId == '1211') {
    return ['averagesalebypaytypes'];
  }
  if (chartId == '1212') {
    return ['customerpay'];
  }
  if (chartId == '1213') {
    return ['warranty'];
  }
  if (chartId == '1214') {
    return ['internal'];
  }
  if (chartId == '1215') {
    return ['maintenance'];
  }
  if (chartId == '1216') {
    return ['extendedservice'];
  }
  if (chartId == '1217') {
    return ['factoryservice'];
  }

  if (chartId == '1218') {
    return ['partsRevenuePerRoCombined'];
  }
  if (chartId == '1219') {
    return ['partsRevenuePerRoCustomerpay'];
  }
  if (chartId == '1220') {
    return ['partsRevenuePerRoWarranty'];
  }
  if (chartId == '1221') {
    return ['partsRevenuePerRoInternal'];
  }
  if (chartId == '1222') {
    return ['partsRevenuePerRoMaintenance'];
  }
  if (chartId == '1223') {
    return ['partsRevenuePerRoExtendedwarranty'];
  }
  if (chartId == '1224') {
    return ['partsRevenuePerRoFactoryservice'];
  }
  if (chartId == '1225') {
    return ['partsMarkupAll'];
  }
  if (chartId == '1226' || chartId == '1238') {
    return ['partsMarkupCustomerpay'];
  }
  if (chartId == '1227') {
    return ['partsMarkupWarranty'];
  }
  if (chartId == '1228') {
    return ['partsMarkupInternal'];
  }
  if (chartId == '1229') {
    return ['partsMarkupMaintenance'];
  }
  if (chartId == '1230') {
    return ['partsMarkupExtendedservice'];
  }
  if (chartId == '1231') {
    return ['partsMarkupFactoryservice'];
  }
  if (chartId == '1316') {
    return ['mpipenetrationperc'];
  }
  if (chartId == '1317') {
    return ['menupenetrationperc'];
  }

  if (chartId == '1319') {
    return ['hoursPerRepairOrder'];
  }
  if (chartId == '1320') {
    return ['hoursPerRepairOrderCustomerpay'];
  }
  if (chartId == '1321') {
    return ['hoursPerRepairOrderWarranty'];
  }
  if (chartId == '1322') {
    return ['hoursPerRepairOrderInternal'];
  }
  if (chartId == '1323') {
    return ['hoursPerRepairOrderMaintenance'];
  }
  if (chartId == '1324') {
    return ['hoursPerRepairOrderExtendedservice'];
  }
  if (chartId == '1325') {
    return ['hoursPerRepairOrderFactoryservice'];
  }
  if (chartId == '1334') {
    return ['markupCurrentyear', 'markupPreviousyear', 'markupPastyear'];
  }
  if (chartId == '931') {
    return [
      'lbrvolumeOpportunity',
      'lbrgrossOpportunity',
      'lbrjointOpportunity'
    ];
  }
  if (chartId == '926') {
    return [
      'prtvolumeOpportunity',
      'prtgrossOpportunity',
      'prtjointOpportunity'
    ];
  }
  if (chartId == '921') {
    return [
      'competitiveOpportunity',
      'maintenanceOpportunity',
      'repairOpportunity',
      'total'
    ];
  }
  if (chartId == '1359') {
    return ['Combined'];
  }
  if (chartId == '1360') {
    return ['Customer Pay'];
  }
  if (chartId == '1361') {
    return ['Warranty'];
  }
  if (chartId == '1362') {
    return ['Internal'];
  }
}

function getChartRenderType(chartType) {
  if (
    chartType == 8 ||
    chartType == 9 ||
    chartType == 10 ||
    chartType == 11 ||
    chartType == 12 ||
    chartType == 13 ||
    chartType == 16 ||
    chartType == 17 ||
    chartType == 20
  ) {
    return 'bar';
  } else return 'line';
}

function getDataLabel(value, chartType, realm) {
  // if (chartType == 2 || chartType == 3 || chartType == 21) {
  //   return getYearLegend(value.split('  ')[1], realm);
  // } else return value.split('  ')[1];
  if (chartType == 2 || chartType == 3 || chartType == 21) {
    return getYearLegend(value, realm);
  } else return value;
}
const dateFormatter = item => moment(item).format('MMM');
const dateFormatterWithYearLabel = item =>
  moment(item).format('MMM') + "'" + moment(item).format('YY');
const dateFormatterWithYear = item => moment(item).format('MMM YY');

function strikeThroughLegendItem(chart, datasetIndex) {
  if (chart && chart.data.datasets.length > 0) {
    var legend = chart.legend;
    var legendItem = legend.legendItems[datasetIndex];
    var dataset = chart.data.datasets[datasetIndex];
    dataset.hidden = true;
    legendItem.hidden = true;
    chart.update();
  }
}

/**
 *
 * @param {*} chartType
 * 1 for CPOverview charts
 * 2 for CPPartsOverview charts
 * @param {*} yAxisRanges
 * [minvalue,stepsize] for charts
 */
export function getChartConfiguration(
  chartType,
  yAxisRanges,
  isStacked,
  xAxisRanges,
  resultSet,
  chartId,
  isServiceAdvisorFilter,
  realm,
  dashboard,
  Zoomed,
  isFrom,
  chartData,
  type
) {
  let storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
  realm = realm ? realm : localStorage.getItem('realm');
  const popup = localStorage.getItem('popup');
  let multipleItemArr = [];
  let singleItemArr = [];
  let resultentItemArr = [];
  let hovering = false;
  let tooltip = document.getElementById('tooltip_' + chartId);
  let pathName = window.location.pathname;

  const options = {
    maintainAspectRatio:
      (chartId == 966 ||
        chartId == 953 ||
        chartId == 916 ||
        chartId == 1318 ||
        chartId == 1334 ||
        chartId == 955 ||
        chartId == 1073 ||
        chartId == 955) &&
      localStorage.getItem('popup') != 'true'
        ? false
        : chartType == 18 || chartType == 19 || chartId == 931 || chartId == 926
        ? localStorage.getItem('popup') == 'true'
          ? false
          : false
        : type == 'popup' &&
          localStorage.getItem('popup') == 'true' &&
          chartId != 921
        ? true
        : false,
    tooltips: {
      titleFontColor: '#003d6b',
      bodyFontColor: '#003d6b',
      borderColor: '#003d6b',
      borderWidth: 1,
      backgroundColor: '#ddeaf4',
      mode:
        (chartType == 18 || chartType == 19) && Zoomed == true
          ? 'point'
          : 'index',
      axis: 'y',
      yPadding: chartId == 931 ? 1 : 1,
      // yAlign: chartType == 8 ?( chartId ==931 ? 'bottom' : 'center') : 'top',
      // yAlign: chartType == 8 ? 'center' : 'top',
      position: chartType == 8 ? 'nearest' : 'nearest',
      usePointStyle: true,

      //  position: 'nearest',
      callbacks: {
        label: function(tooltipItem, data) {
          let itemCount = 0;

          if ((chartType == 18 || chartType == 19) && Zoomed == true) {
            data = lodash.uniqWith(
              data.datasets[tooltipItem.datasetIndex].data,
              lodash.isEqual
            );

            data.map(item => {
              if (
                item.y == tooltipItem.yLabel &&
                item.x == tooltipItem.xLabel
              ) {
                itemCount = item.count;
              }
            });
            if (itemCount > 1) {
              resultentItemArr = [];
              multipleItemArr = multipleItemArr.concat(
                getTootipLabel(
                  chartType,
                  tooltipItem,
                  data,
                  chartId,
                  '',
                  resultSet
                )
              );
              if (itemCount == multipleItemArr.length) {
                resultentItemArr.push(
                  lodash.uniqWith(multipleItemArr, lodash.isEqual)
                );
                multipleItemArr = [];
                return resultentItemArr;
              }
            } else {
              singleItemArr = [];
              singleItemArr.push(
                getTootipLabel(
                  chartType,
                  tooltipItem,
                  data,
                  chartId,
                  '',
                  resultSet
                )
              );
              return singleItemArr;
            }
          } else {
            return getTootipLabel(
              chartType,
              tooltipItem,
              data,
              chartId,
              '',
              resultSet
            );
          }
        },
        title: function(tooltipItem, data) {
          if (
            chartType == 1 ||
            dashboard == 'Special Metrics' ||
            dashboard == 'Discounts' ||
            dashboard == 'AddOns' ||
            chartType == 8
          ) {
            return dateFormatterWithYear(tooltipItem[0].label);
          } else if (chartType == 18 || chartType == 19) {
            return getTooltipTitle(tooltipItem, data, chartType);
          }
          if (
            (chartType == 5 ||
              (chartType == 6 &&
                chartId != 1044 &&
                chartId != 1318 &&
                chartId != 918 &&
                chartId != 956 &&
                chartId != 955 &&
                chartId != 952 &&
                chartId != 916 &&
                chartId != 1334) ||
              (chartType == 2 &&
                chartId != 1133 &&
                chartId != 1138 &&
                chartId != 1143 &&
                chartId != 1326)) &&
            isThreeYearChart(chartId) == false
          ) {
            return dateFormatterWithYear(tooltipItem[0].label);
          }
          // return dateFormatterWithYear(tooltipItem[0].label);
          return dateFormatter(tooltipItem[0].label);
        }
      }
    },

    legend: {
      position: 'bottom',
      align: 'center',
      labels: {
        boxWidth: 12,
        usePointStyle: false
      }
      //Tooltip for no data

      /* 
      onHover: function(event, legendItem) {
        if (hovering) {
          return;
        }
        hovering = true;
        chartData &&
          chartData.datasets.map((item, index) => {
            if (index == legendItem.datasetIndex) {
              if (item.data.every(lodash.isNull)) {
                tooltip = document.getElementById('tooltip_' + chartId);

                tooltip.innerHTML =
                  '<span id="warringMsg" style="font-size: 16px"> * </span> <span style="margin-left:3px; font-size: 11px"> No data available for ' +
                  legendItem.text +
                  '</span>';
                // tooltip.style.left = event.x - 300 + 'px';
                tooltip.style.fontFamily = 'Roboto';
                tooltip.style.color = 'red';
                //tooltip.style.position = 'absolute';
                tooltip.style.marginTop = '-5px';
                tooltip.style.display = 'flex';
                tooltip.style.justifyContent = 'left';
              }
            }
          });
      },
      onLeave: function() {
        hovering = false;
        if (tooltip) {
          tooltip.innerHTML = '';
          tooltip.style.display = 'none';
        }
        if (
          this.chart &&
          this.chart.legend &&
          this.chart.legend.options.onClick &&
          chartData &&
          chartData.datasets
        ) {
          chartData.datasets.map((item, index) => {
            if (item.data.every(lodash.isNull)) {
              setTimeout(() => {
                if (this.chart) {
                  strikeThroughLegendItem(this.chart, index);
                }
              }, 100);
            }
          });
        }
      }*/
    },
    plugins: {
      datalabels: { display: false }
    },
    layout: {
      padding: {
        left: 0,
        right: 0,
        top: chartId == 931 || chartId == 926 ? 35 : 0,
        bottom: 0
      }
    },
    // plugins: {
    //   datalabels: {
    //     anchor: chartId == 931 || chartId == 926 ? 'end' : '',
    //     align: chartId == 931 || chartId == 926 ? 'end' : '',

    //     color: chartId == 931 || chartId == 926 ? '#737373' : '',
    //     font: {
    //       weight: chartId == 931 || chartId == 926 ? 'bold' : '',
    //       size: chartId == 931 || chartId == 926 ? 13 : ''
    //     },

    //     // borderWidth:function(ctx){

    //     //   console.log("123",ctx.datasetIndex)
    //     //   return 1},
    //     // borderColor: '#a3a375',
    //     offset: ctx => {
    //       let arr = [];
    //       if (chartId == 926 || chartId == 931) {
    //         let datasets = ctx.chart.data.datasets.filter((ds, datasetIndex) =>
    //           ctx.chart.isDatasetVisible(datasetIndex)
    //         );
    //         if (datasets.length == 3) {
    //           ctx.chart.config.data.datasets.map(dataPoint =>
    //             arr.push(dataPoint.data[ctx.dataIndex])
    //           );
    //         } else {
    //           datasets.map(dataset => {
    //             arr.push(dataset.data[ctx.dataIndex]);
    //           });
    //         }
    //         const min = Math.min(...arr);
    //         //  const sum=  arr.reduce((a, b) => a + b, 0);
    //         if (datasets.length < 3) return '20';
    //         else return '4';
    //       }
    //     },

    //     formatter: (value, ctx) => {
    //       var ci = ctx.chart;

    //       let gpFlag = 0;
    //       if (chartId == 931 || chartId == 926) {
    //         let datasets = ctx.chart.data.datasets.filter((ds, datasetIndex) =>
    //           ctx.chart.isDatasetVisible(datasetIndex)
    //         );

    //         for (var i = datasets.length - 1; i > -1; i--) {
    //           if (datasets[i].label === '  Gross Profit %') {
    //             gpFlag = 1;
    //           }
    //         }
    //         console.log('data', ctx.datasetIndex, datasets.length, gpFlag);
    //         if (
    //           ctx.datasetIndex === datasets.length - 1 ||
    //           (gpFlag == 0 && ctx.datasetIndex == 2) ||
    //           (gpFlag == 1 && ctx.datasetIndex == 1 && datasets.length != 3)
    //         ) {
    //           let sum = 0;
    //           datasets.map(dataset => {
    //             sum += dataset.data[ctx.dataIndex];
    //           });
    //           return '$' + Math.round(sum).toLocaleString();
    //         } else {
    //           return '';
    //         }
    //       }
    //     }
    //   }
    // },
    hover: {
      animationDuration: 0
    },
    animation: {
      onComplete: function() {
        let type;
        //Legend Strike with no data
        /* if (
          this.chart &&
          this.chart.legend &&
          this.chart.legend.options.onClick &&
          chartData &&
          chartData.datasets
        ) {
          chartData.datasets.forEach((item, index) => {
            if (item.data.every(lodash.isNull)) {
              //  setTimeout(() => {
              //   if (this.chart) {
              //     strikeThroughLegendItem(this.chart, index, 'hover');
              //   }
              // }, 100);

              const chartReference = this.chart;
              setTimeout(() => {
                if (chartReference && chartReference.ctx != null) {
                  chartReference.getDatasetMeta(index).hidden = true;
                  chartReference.update();
                }
              }, 100);
            }
          });
        }*/

        if (chartId == 931 || chartId == 926) {
          if (chartId == 931 || chartId == 926) {
            type = '$';
          }

          var chartInstance = this.chart;

          var horizontal =
            chartInstance.config.type.lastIndexOf('horizontal', 0) === 0; //if type of graph starts with horizontal

          var ctx = chartInstance.ctx;
          ctx.save();
          ctx.globalCompositeOperation = 'destination-over';
          ctx.textAlign = horizontal ? 'left' : 'center';
          ctx.textBaseline = horizontal ? 'middle' : 'bottom';
          ctx.font = ' bold  13px  sans-serif';
          ctx.fillStyle = '#000';
          ctx.fontFamily = "'Helvetica Neue', 'Helvetica', 'Arial', sans-serif";

          var dataLabels = {}; //key = x or y coordinate (depends on grouping), value = Array[0] other coordinate , Array[1] = value to print
          var equationForGrouping = horizontal ? Math.max : Math.min; //equation to use for grouping

          //get values from chart, fill them in dataLabels (as seen in https://github.com/chartjs/Chart.js/blob/master/samples/data_label_combo-bar-line.html )
          Chart.helpers.each(
            this.data.datasets.forEach(function(dataset, i) {
              var meta = chartInstance.controller.getDatasetMeta(i);
              Chart.helpers.each(
                meta.data.forEach(function(bar, index) {
                  //for each part of each stacked bar
                  if (meta.hidden != true) {
                    //if data is not hidden (by clicking on it in the legend)
                    var groupByCoordinate = horizontal
                      ? bar._model.y
                      : bar._model.x;
                    var otherCoordinate = horizontal
                      ? bar._model.x
                      : bar._model.y;

                    if (dataLabels[groupByCoordinate]) {
                      dataLabels[groupByCoordinate][0] = equationForGrouping(
                        isNaN(otherCoordinate) === true
                          ? 216.70435910954012
                          : otherCoordinate,
                        isNaN(dataLabels[groupByCoordinate][0]) === true
                          ? 216.70435910954012
                          : dataLabels[groupByCoordinate][0]
                      );
                      dataLabels[groupByCoordinate][1] += dataset.data[index];
                    } else
                      dataLabels[groupByCoordinate] = [
                        otherCoordinate,
                        dataset.data[index]
                      ];
                  }
                }),
                this
              );
            }),
            this
          );

          //draw values onto graph
          for (var key in dataLabels) {
            // var total = data + this.data.datasets[1].data[index];

            if (horizontal)
              ctx.fillText(
                Math.round(dataLabels[key][1]).toLocaleString(),
                Math.round(dataLabels[key][0]).toLocaleString(),
                key
              );
            else {
              ctx.fillText(
                type + Math.round(dataLabels[key][1]).toLocaleString(),
                key,
                Math.round(dataLabels[key][0]).toLocaleString()
              );
            }
          }
          ctx.restore();
        }
      }
    },
    scales: {
      yAxes: [
        {
          display: true,
          scaleLabel: {
            display: false,
            labelString: ''
          },

          ticks: {
            maxTicksLimit: isServiceAdvisorFilter ? 5 : undefined,
            callback: function(value, index, values) {
              return getYAxisLabel(chartType, value, chartId);
            }
          }
        }
      ],
      xAxes: [
        {
          display: true,
          scaleLabel: {
            display: false,
            labelString: ''
          },
          ticks: {
            //reverse:
            // chartId == 942 ||
            // chartId == 939 ||
            // chartId == 920
            //   ? // ((chartId == 940 || chartId == 946 || chartId == 1238) &&
            //     //   isServiceAdvisorFilter == false)
            //     true
            //   : false, // cube pre-aggregation
            beginAtZero: true,
            autoSkip: true,
            maxRotation:
              pathName == '/LaborItemization' ||
              pathName == '/PartsItemization' ||
              isFrom == 'scatter-plot'
                ? 0
                : 45,

            minRotation:
              pathName == '/LaborItemization' ||
              pathName == '/PartsItemization' ||
              isFrom == 'scatter-plot'
                ? 0
                : 45,

            callback: function(value, index, values) {
              if (chartType == 19) {
                return '$' + Math.round(value);
              } else if (chartType == 18) {
                return Math.round(value);
              }
              if (
                chartId == 1073 ||
                chartId == 1098 ||
                chartId == 1356 ||
                chartId == 966 ||
                chartId == 1334
              ) {
                return dateFormatter(value);
              } else
                return pathName == '/CPLaborOverview' ||
                  pathName == '/CPPartsOverview' ||
                  isThreeYearChart(chartId) == true
                  ? dateFormatter(value)
                  : dateFormatterWithYearLabel(value);
            }
          }
        }
      ]
    }
  };

  if (chartId == 1316 || chartId == 1317) {
    options.scales.yAxes[0].ticks.suggestedMin = 0;
    options.scales.yAxes[0].ticks.maxTicksLimit = 8;
  }

  if (chartId == 960 || chartId == 948 || chartId == 1354) {
    options.scales.yAxes[0].ticks.maxTicksLimit = 8;
  }

  if (
    (chartId == 1143 && realm == 'lupient') ||
    chartId == 1326 ||
    chartId == 936 ||
    chartId == 938
  ) {
    options.scales.yAxes[0].ticks.maxTicksLimit = 5;
  }

  if (
    chartId == 927 ||
    chartId == 928 ||
    chartId == 929 ||
    chartId == 933 ||
    chartId == 932 ||
    chartId == 934 ||
    chartId == 931
  ) {
    options.scales.yAxes[0].ticks.min = 0;
    options.scales.yAxes[0].ticks.maxTicksLimit = 5;
  }
  if (chartId == 931 || chartId == 926 || chartId == 921 || chartId == 924) {
    options.scales.yAxes[0].ticks.min = 0;
    options.scales.yAxes[0].ticks.maxTicksLimit = 7;
  }

  if (
    chartType == 4 ||
    chartType == 1 ||
    chartType == 5 ||
    chartType == 6 ||
    (chartType == 8 &&
      chartId != 927 &&
      chartId != 928 &&
      chartId != 929 &&
      chartId != 933 &&
      chartId != 932 &&
      chartId != 934 &&
      chartId != 931 &&
      chartId != 926 &&
      chartId != 921 &&
      chartId != 924) ||
    (chartType == 10 && chartId != 1317) ||
    chartType == 11 ||
    chartType == 12 ||
    chartType == 13 ||
    chartType == 18 ||
    chartType == 19 ||
    chartType == 17 ||
    chartType == 20
  ) {
    options.scales.yAxes[0].ticks.min = getMinValue(chartType, yAxisRanges);

    options.scales.yAxes[0].ticks.stepSize = getStepSize(
      chartType,
      yAxisRanges,
      chartId,
      realm
    );
    if (
      (chartType == 5 && chartId != 940) ||
      chartType == 18 ||
      chartType == 19
    ) {
      options.scales.yAxes[0].ticks.max = getMaxValue(chartType, yAxisRanges);
    }
    if (chartId == 940) {
      options.scales.yAxes[0].ticks.min = 0;
    }
    if (chartId == 1326) {
      options.scales.yAxes[0].ticks.max = 150;
    }

    if (
      (chartId == 948 || chartId == 1099 || chartId == 1100) &&
      (storeId == '236951998' || storeId == '256778041')
    ) {
      options.scales.yAxes[0].ticks.stepSize = 20;
    }

    if (chartId == 1102 && (storeId == '236951998' || storeId == '256778041')) {
      options.scales.yAxes[0].ticks.stepSize = 2;
    }
  }

  if (
    chartId == 1103 ||
    chartId == 1109 ||
    chartId == 1108 ||
    chartId == 1117
  ) {
    options.scales.yAxes[0].ticks.maxTicksLimit = 5;
  }

  if (
    chartId == 1044 ||
    chartId == 1318 ||
    (chartId == 955 && realm == 'lupient')
  ) {
    options.scales.yAxes[0].ticks.maxTicksLimit = 7;
  }

  if (chartId == 1234 || chartId == 1113 || chartId == 1236) {
    options.scales.yAxes[0].ticks.maxTicksLimit = 5;
  }

  if (
    chartId == 942 ||
    chartId == 939 ||
    (chartId == 920 && realm == 'halomc')
  ) {
    options.scales.yAxes[0].ticks.maxTicksLimit = 8;
  }
  if (chartId == 942 && storeId == '256778041') {
    options.scales.yAxes[0].ticks.stepSize = 10000;
    options.scales.yAxes[0].ticks.max = 50000;
  }
  if (chartId == 942 && storeId == '236951998') {
    options.scales.yAxes[0].ticks.stepSize = 10000;
    options.scales.yAxes[0].ticks.max = 30000;
  }
  if ((chartId == 1073 && storeId == '244584918') || storeId == '256778041') {
    options.scales.yAxes[0].ticks.stepSize = 0.2;
    options.scales.yAxes[0].ticks.min = 0;
    options.scales.yAxes[0].ticks.maxTicksLimit = 5;
  }

  if (
    chartId == 1098 ||
    chartId == 1356 ||
    chartId == 955 ||
    (chartId == 1049 && storeId == '256778041') ||
    (chartId == 944 && storeId == '236951998') ||
    (chartId == 939 && storeId == '232297966')
  ) {
    options.scales.yAxes[0].ticks.maxTicksLimit = 5;
  }

  if ((chartId == 939 || chartId == 944) && storeId == '256778041') {
    options.scales.yAxes[0].ticks.stepSize = 5000;
  }
  if (
    (chartId == 942 ||
      chartId == 920 ||
      chartId == 944 ||
      chartId == 939 ||
      chartId == 1044 ||
      chartId == 1318) &&
    realm == 'haleyag'
  ) {
    options.scales.yAxes[0].ticks.maxTicksLimit = 5;
  }
  if (
    (chartId == 942 || chartId == 939) &&
    JSON.parse(localStorage.getItem('selectedStoreId')).length > 1
  ) {
    options.scales.yAxes[0].ticks.maxTicksLimit = 5;
  }
  if (
    chartId == 1127 ||
    chartId == 1238 ||
    chartId == 1133 ||
    chartId == 1138 ||
    chartId == 918 ||
    chartId == 952 ||
    chartId == 953 ||
    chartId == 1143 ||
    chartId == 916 ||
    (chartId == 956 && realm == 'haleyag')
  ) {
    options.scales.yAxes[0].ticks.maxTicksLimit = 5;
    if (
      chartId == 1238 &&
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1
    ) {
      options.scales.yAxes[0].ticks.stepSize = 0.5;
    }
  }

  if (
    chartId == 916 ||
    (chartId == 956 && realm == 'greatlakesan') ||
    chartId == 1334
  ) {
    options.scales.yAxes[0].ticks.maxTicksLimit = 7;
  }
  if (chartType == 7 && chartId == 966) {
    options.scales.yAxes[0].ticks.stepSize = 0.05;
  }
  if (
    chartId == 920 &&
    JSON.parse(localStorage.getItem('selectedStoreId')).length > 1
  ) {
    options.scales.yAxes[0].ticks.maxTicksLimit = 5;
  }
  if (chartId == 920 && storeId == '236951998') {
    options.scales.yAxes[0].ticks.stepSize = 50;
  }
  if (chartId == 920 && storeId == '256778041') {
    options.scales.yAxes[0].ticks.stepSize = 100;
  }
  if (chartId == 939 && storeId == '236951998') {
    options.scales.yAxes[0].ticks.stepSize = 5000;
  }
  if (chartType == 16) {
    options.scales.yAxes[0].ticks.max = 0;
    options.scales.yAxes[0].ticks.stepSize = getStepSize(
      chartType,
      yAxisRanges
    );
  }

  if (chartType == 18 || chartType == 19) {
    options.scales.xAxes[0].ticks.min = getMinValuexAxis(
      chartType,
      xAxisRanges
    );
    options.scales.xAxes[0].ticks.stepSize = getStepSizexAxis(
      chartType,
      xAxisRanges
    );
    options.scales.xAxes[0].ticks.max = getMaxValuexAxis(
      chartType,
      xAxisRanges
    );
    options.scales.yAxes[0].scaleLabel.display = true;
    options.scales.xAxes[0].scaleLabel.display = true;
  }
  if (chartType == 18) {
    options.scales.yAxes[0].scaleLabel.labelString = 'ELR';
    options.scales.xAxes[0].scaleLabel.labelString = 'Sold Hours';
  }
  if (chartType == 19) {
    options.scales.yAxes[0].scaleLabel.labelString = 'Parts Markup';
    options.scales.xAxes[0].scaleLabel.labelString = 'Parts Cost';
  }
  if (chartType == 18 || chartType == 19) {
    options.legend.labels.usePointStyle = true;
  }

  if (chartType == 9 || isStacked || chartId == 1239) {
    options.scales.yAxes[0].stacked = true;
    options.scales.xAxes[0].stacked = true;
  }
  if (
    (chartId == 1103 ||
      chartId == 1105 ||
      chartId == 1106 ||
      chartId == 1107 ||
      chartId == 1108 ||
      chartId == 1109 ||
      chartId == 1110 ||
      chartId == 1117) &&
    realm == 'haleyag'
  ) {
    options.scales.yAxes[0].ticks.suggestedMin = 0;
    options.scales.yAxes[0].ticks.maxTicksLimit = 8;
  }

  if (
    chartType == 4 ||
    (chartType == 2 &&
      (chartId == 1138 || chartId == 1143 || chartId == 1326) &&
      realm != 'keatingag' &&
      realm != 'lupient') ||
    (chartType == 3 &&
      chartId == 944 &&
      realm != 'greatlakesan' &&
      storeId != '236951998' &&
      realm != 'fisherhonda') ||
    (chartType == 7 &&
      chartId == 1073 &&
      realm != 'keatingag' &&
      storeId != '256778041') ||
    (chartType == 2 &&
      chartId == 966 &&
      realm != 'greatlakesan' &&
      realm != 'haleyag' &&
      realm != 'ferrarioat_store')
  ) {
    //options.scales.xAxes[0].offset = true;
    // if (!isServiceAdvisorFilter) {
    if (resultSet.length > 0) {
      var updatedMinMaxArray = getMinAndMaxValues(
        resultSet,
        chartId,
        yAxisRanges,
        realm
      );

      let min = updatedMinMaxArray[0] * 100;
      let roundedMin =
        chartId == 1073 && storeId == '236951998'
          ? Math.round(min / 5) * 5
          : Math.ceil(min / 5) * 5;

      //let roundedMin = parseInt(min / 10, 10) * 10;
      let displayedMin = roundedMin / 100;
      console.log('resultSet11', displayedMin, chartId);
      let max = updatedMinMaxArray[1] * 100;
      let roundedMax = Math.floor(max / 5) * 5;
      // let roundedMax = parseFloat(max / 10, 10) * 10;
      let displayedMax = roundedMax / 100;
      //  options.scales.yAxes[0].ticks.maxTicksLimit = 6;
      options.scales.yAxes[0].ticks.min = displayedMin;
      options.scales.yAxes[0].ticks.max = displayedMax;
      if (chartId == 1073 && storeId == '236951998') {
        options.scales.yAxes[0].ticks.maxTicksLimit = 6;
      }
      if (chartId == 1326 && storeId == '236951998') {
        options.scales.yAxes[0].ticks.max = 150;
      }
      if ((chartId == 1143 || chartId == 1138) && realm == 'ferrarioat_store') {
        options.scales.yAxes[0].ticks.min = 0;
      }

      // }
      //   options.scales.yAxes[0].ticks.min = updatedMinMaxArray[0];
      //   options.scales.yAxes[0].ticks.max = updatedMinMaxArray[1];
    }
    // }
  }

  if (
    chartType == 1 &&
    yAxisRanges != undefined &&
    yAxisRanges[3] != undefined
  ) {
    options.scales.yAxes[0].ticks.suggestedMax = yAxisRanges[3];
  }
  // }
  // if ((chartId == 966) & (yAxisRanges == undefined)) {
  //   if (realm == 'haleyag' && isServiceAdvisorFilter) {
  //     options.scales.yAxes[0].ticks.min = 0;
  //     options.scales.yAxes[0].ticks.maxTicksLimit = 12;
  //   } else {
  //     options.scales.yAxes[0].ticks.min = 0;
  //     options.scales.yAxes[0].ticks.max = 1;
  //   }
  // }
  if (chartId == 966) {
    options.scales.yAxes[0].ticks.min = 0;
  }
  return options;
}
function getTooltipTitle(tooltipItem, data, chartType) {
  var label = tooltipItem[0].datasetIndex;

  if (label == 0) {
    var tooltiplabel = '';
    data.datasets[0].data.map(item => {
      if (
        item['x'] == tooltipItem[0]['xLabel'] &&
        item['y'] == tooltipItem[0]['yLabel']
      ) {
        tooltipItem[0]['count'] = item['count'];
        tooltipItem[0]['label'] = item['label'];
        //   tooltipItem[0]['label'] =
        //     chartType == 18
        //       ? Number(item['label']).toFixed(2)
        //       : '$' +
        //         Number(item['label'])
        //           .toFixed(2)
        //           .toString()
        //           .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      }
    });
    tooltiplabel = tooltipItem[0]['count'] > 1 ? ' Jobs' : 'Job';

    return chartType == 18
      ? tooltipItem[0]['count'] +
          '' +
          tooltiplabel +
          ' @ ' +
          tooltipItem[0]['label'] +
          ' Hrs'
      : tooltipItem[0]['count'] +
          '' +
          tooltiplabel +
          ' @ ' +
          '$' +
          tooltipItem[0]['label'];
  } else if (label == 1) {
    let tooltiplabel = '';
    data.datasets[1].data.map(item => {
      if (
        item['x'] == tooltipItem[0]['xLabel'] &&
        item['y'] == tooltipItem[0]['yLabel']
      ) {
        tooltipItem[0]['count'] = item['count'];
        tooltipItem[0]['label'] = item['label'];
      }
    });
    tooltiplabel = tooltipItem[0]['count'] > 1 ? ' Jobs' : 'Job';

    return chartType == 18
      ? tooltipItem[0]['count'] +
          '' +
          tooltiplabel +
          ' @ ' +
          tooltipItem[0]['label'] +
          ' Hrs'
      : tooltipItem[0]['count'] +
          '' +
          tooltiplabel +
          ' @ ' +
          '$' +
          tooltipItem[0]['label'];
  } else if (label == 2) {
    let tooltiplabel = '';
    let i = 0;
    data.datasets[2].data.map(item => {
      if (
        item['x'] == tooltipItem[0]['xLabel'] &&
        item['y'] == tooltipItem[0]['yLabel']
      ) {
        i++;
        tooltipItem[0]['count'] = item['count'];
        tooltipItem[0]['label'] = item['label'];
      }
    });
    // if (tooltipItem[0]['count'] != i) {
    //   tooltipItem[0]['count'] =
    //     i > tooltipItem[0]['count'] ? i : tooltipItem[0]['count'];
    // }

    tooltiplabel = tooltipItem[0]['count'] > 1 ? ' Jobs' : 'Job';

    return chartType == 18
      ? tooltipItem[0]['count'] +
          '' +
          tooltiplabel +
          ' @ ' +
          tooltipItem[0]['label'] +
          ' Hrs'
      : tooltipItem[0]['count'] +
          '' +
          tooltiplabel +
          ' @ ' +
          '$' +
          tooltipItem[0]['label'];
  }
}

function getYAxisLabel(chartType, value, chartId) {
  if (
    (chartType == 3 ||
      chartType == 4 ||
      chartType == 1 ||
      chartType == 8 ||
      chartId == 1105 ||
      chartId == 1106 ||
      chartId == 1107 ||
      chartId == 1109 ||
      chartId == 1110 ||
      chartId == 1116 ||
      chartId == 1111 ||
      chartId == 1234 ||
      chartId == 955 ||
      chartId == 952 ||
      chartId == 1164 ||
      chartId == 1236 ||
      chartId == 1165 ||
      chartId == 1237 ||
      chartId == 1175 ||
      chartId == 1239 ||
      chartId == 1090 ||
      chartType == 18 ||
      chartId == 930 ||
      chartId == 936 ||
      chartId == 944) &&
    chartId != 1138 &&
    chartId != 1143 &&
    chartId != 1326
  ) {
    return chartType == 18
      ? ' $' +
          Math.round(value)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      : ' $' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
  if (
    chartType == 5 ||
    chartType == 7 ||
    (chartType == 9 && chartId != 935 && chartId != 1357) ||
    (chartType == 12 &&
      chartId != 938 &&
      chartId != 1115 &&
      chartId != 1235 &&
      chartId != 1232 &&
      chartId != 1233)
  ) {
    return Math.round(value * 100) + ' %';
  }
  if (chartId == 935) {
    return Number.isInteger(value * 100)
      ? value * 100 + ' %'
      : parseFloat((value * 100).toFixed(2)) + ' %';
    //return ((value * 100) < 1 && (value * 100) !=0 ) ? (value * 100).toFixed(2) + ' %' :  (value * 100) + ' %';
  }
  if (
    chartId == 1123 ||
    chartId == 1115 ||
    chartId == 1232 ||
    chartId == 1233 ||
    chartId == 1235
  ) {
    return value + ' %';
  }

  if (
    chartType == 10 &&
    chartId != 1316 &&
    // chartId != 1317 &&
    chartId != 1355 &&
    chartId != 1102
  ) {
    return ((value / 100) * 100).toFixed(0) + ' %';
  } else if (chartId == 1317) {
    const decimal =
      ((value / 100) * 100).toFixed(2) -
      Math.floor(((value / 100) * 100).toFixed(2)); // Math.floor(3.78) returns 3

    return Number.isInteger((value / 100) * 100) || decimal == 0
      ? Math.round((value / 100) * 100) + ' %'
      : ((value / 100) * 100).toFixed(2) + ' %';
  } else if (chartId == 1102) {
    return Number.isInteger((value / 100) * 100)
      ? (value / 100) * 100 + ' %'
      : Math.round((value / 100) * 100) + ' %';
  } else if (chartId == 1355) {
    return Math.round((value / 100) * 100) + ' %';
  }

  if (
    chartId == 920 ||
    chartId == 925 ||
    chartId == 1133 ||
    chartId == 1138 ||
    chartId == 918 ||
    chartId == 1143 ||
    chartId == 1100 ||
    chartId == 1104 ||
    chartId == 1108 ||
    chartId == 1326
  ) {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  } else if (chartId == 1096) {
    return Math.round(value);
  } else if (chartId == 1073 || chartId == 966) {
    return Math.round(value * 100) + ' %';
  } else return value;
}
function getMinAndMaxValues(resultSet, chartId, yAxisRanges, realm) {
  const datasets = resultSet[0].datasets;

  // Initialize the maximum value with a small value (since these are percentages)
  let maxValueResult = -1.0;

  // Iterate through each dataset and its data points
  datasets.forEach(dataset => {
    dataset.data.forEach(value => {
      if (value !== null && parseFloat(value) > maxValueResult) {
        maxValueResult = parseFloat(value);
      }
    });
  });
  let minValueResult = Number.MAX_VALUE;

  // Iterate through each dataset and its data points
  datasets.forEach(dataset => {
    dataset.data.forEach(value => {
      if (value !== null && parseFloat(value) < minValueResult) {
        minValueResult = parseFloat(value);
      }
    });
  });
  console.log('Maximum value:', maxValueResult, minValueResult);

  // var filteredArray = arraydata.filter(function(el) {
  //   return el != null;
  // });

  //const isAllZero = filteredArray.every(item => item === '0.00');

  var maxValue,
    increasedmaxValue,
    updatedMaxValue,
    minValue,
    decreasedminValue,
    updatedMinValue;
  if (
    chartId == 960 ||
    chartId == 944 ||
    chartId == 1049 ||
    chartId == 952 ||
    chartId == 931 ||
    chartId == 926
  ) {
    //10,000
    // maxValue = Math.round(Math.max.apply(null, filteredArray));
    increasedmaxValue = (maxValueResult * 10) / 100;
    updatedMaxValue =
      Math.round((maxValueResult + increasedmaxValue) / 10000) * 10000;
    // minValue = Math.round(Math.min.apply(null, filteredArray));
    decreasedminValue = (minValueResult * 10) / 100;
    updatedMinValue =
      Math.round((minValueResult - decreasedminValue) / 10000) * 10000;
    if (yAxisRanges == undefined) {
      updatedMinValue = 0;
      updatedMaxValue = Math.ceil(maxValueResult / 100) * 100;
    }
  }
  if (chartId == 1073 || chartId == 966) {
    // %

    //   maxValue = Math.max.apply(null, filteredArray);
    increasedmaxValue = (maxValueResult * 10) / 100;
    updatedMaxValue = (increasedmaxValue + maxValueResult).toFixed(2);
    //  minValue = Math.min.apply(null, filteredArray);
    decreasedminValue = (minValueResult * 10) / 100;
    updatedMinValue = (minValueResult - decreasedminValue).toFixed(2);

    if (chartId == 1073) {
      let val = updatedMinValue * 100;
      const remainder = val % 5;
      val += 5 - remainder;

      updatedMinValue = val / 100;
    }

    // if (yAxisRanges == undefined) {
    //   updatedMinValue = 0;
    //   updatedMaxValue = Math.ceil(maxValueResult / 100) * 100;
    // }
    console.log(
      'updatedMinValue',
      updatedMinValue,
      updatedMaxValue,
      yAxisRanges
    );
  }
  if (
    chartId == 920 ||
    chartId == 918 ||
    chartId == 956 ||
    chartId == 925 ||
    chartId == 1133 ||
    chartId == 1138 ||
    chartId == 1143 ||
    chartId == 1326
  ) {
    // 100

    increasedmaxValue = (maxValueResult * 10) / 100;
    updatedMaxValue =
      Math.round((maxValueResult + increasedmaxValue) / 100) * 100;

    decreasedminValue = (minValueResult * 10) / 100;
    updatedMinValue =
      Math.round((minValueResult - decreasedminValue) / 100) * 100;

    if (chartId == 1133) {
      updatedMinValue = Math.ceil(updatedMinValue / 200) * 200;
    }
    if (chartId == 1326 && realm == 'ferrarioat_store') {
      updatedMaxValue =
        Math.ceil((maxValueResult + increasedmaxValue) / 100) * 100;
    }

    if (yAxisRanges == undefined) {
      updatedMinValue = 0;
      updatedMaxValue = maxValueResult;
    }
  }
  if (chartId == 1044 || chartId == 1318) {
    increasedmaxValue = (maxValueResult * 10) / 100;
    updatedMaxValue = (increasedmaxValue + maxValue).toFixed(1);

    decreasedminValue = (minValueResult * 10) / 100;
    updatedMinValue = (minValueResult - decreasedminValue).toFixed(1);
  }

  if (
    chartId == 1098 ||
    chartId == 1356 ||
    chartId == 946 ||
    chartId == 955 ||
    chartId == 953 ||
    chartId == 1127
  ) {
    // 10's

    increasedmaxValue = (maxValue * 10) / 100;
    updatedMaxValue =
      Math.round((maxValueResult + increasedmaxValue) / 10) * 10;

    decreasedminValue = (minValueResult * 10) / 100;

    updatedMinValue =
      Math.round((minValueResult - decreasedminValue) / 10) * 10;
  }
  if (chartId == 1138) {
    increasedmaxValue = (maxValueResult * 15) / 100;
    updatedMaxValue =
      Math.round((maxValueResult + increasedmaxValue) / 10) * 10;
  }

  var arr = [updatedMinValue, updatedMaxValue];

  return arr;
}
function getMinValue(chartType, yAxisRanges) {
  if (
    chartType == 4 ||
    chartType == 1 ||
    chartType == 5 ||
    chartType == 6 ||
    chartType == 8 ||
    chartType == 9 ||
    chartType == 10 ||
    chartType == 11 ||
    chartType == 12 ||
    chartType == 13 ||
    chartType == 17 ||
    chartType == 20
  ) {
    return yAxisRanges == undefined ? null : yAxisRanges[0];
  } else return null;
}
function getMinValuexAxis(chartType, xAxisRanges) {
  if (chartType == 18 || chartType == 19) return 0;
  else return null;
}

function getMaxValue(chartType, yAxisRanges) {
  console.log('getMaxValue');
  if (
    chartType == 4 ||
    chartType == 1 ||
    chartType == 5 ||
    chartType == 8 ||
    chartType == 9 ||
    chartType == 10 ||
    chartType == 11 ||
    chartType == 12 ||
    chartType == 13
  ) {
    return yAxisRanges == undefined ? null : yAxisRanges[2];
  } else if (chartType == 18 || chartType == 19) {
    return yAxisRanges == undefined ? null : yAxisRanges[1];
  } else return null;
}

function getStepSize(chartType, yAxisRanges, chartId, realm) {
  if (
    chartType == 4 ||
    chartType == 1 ||
    chartType == 5 ||
    chartType == 6 ||
    chartType == 8 ||
    chartType == 9 ||
    chartType == 10 ||
    chartType == 11 ||
    chartType == 12 ||
    chartType == 13 ||
    chartType == 16 ||
    chartType == 17 ||
    chartType == 20
  ) {
    return yAxisRanges == undefined ? null : yAxisRanges[1];
  } else if (chartType == 18 || chartType == 19) {
    return yAxisRanges == undefined ? null : yAxisRanges[0];
  } else return null;
}

function getMaxValuexAxis(chartType, xAxisRanges) {
  if (chartType == 18 || chartType == 19) return xAxisRanges[1];
  else return null;
}

function getStepSizexAxis(chartType, xAxisRanges) {
  // if (chartType == 18 || chartType == 19) return xAxisRanges[0];
  if (chartType == 18) return 2;
  else if (chartType == 19) return xAxisRanges[0];
  else return null;
}

function getTootipLabel(
  chartType,
  tooltipItem,
  data,
  chartId,
  isStoreCompare,
  resultSet
) {
  var label = '';
  var opporLabel = '';

  if (chartType != 18 && chartType != 19) {
    label = data.datasets[tooltipItem.datasetIndex].label || '';
  } else {
    let initialLabel = data.datasets
      ? data.datasets[tooltipItem.datasetIndex].label
      : data[0].category;
    label =
      (initialLabel.split(' ')[1] == 'Maintenance'
        ? 'Maint'
        : initialLabel.split(' ')[1] == 'Competitive'
        ? 'Compet'
        : initialLabel.split(' ')[1]) +
      ', ' +
      initialLabel.split(' ')[0];
  }
  if (isNaN(tooltipItem.yLabel)) {
    tooltipItem.yLabel = 0;
  }
  if (chartId == 926 || chartId == 931 || chartId == 921) {
    const ds = resultSet.series().map((s, index) => ({
      data: s.series.map(r => r.value)
    }));
    opporLabel = ds[tooltipItem.datasetIndex].data[tooltipItem.index];
  }
  switch (chartType) {
    case 1:
      if (label) {
        label += ': ';
      }

      label +=
        '$' +
        tooltipItem.yLabel
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      break;

    case 2:
    case 18:
    case 19:
      if (label) {
        label += ': ';
      }
      if (chartId == 916) {
        label += tooltipItem.yLabel.toFixed(2);
      } else if (chartId == 1133 || chartId == 918) {
        label += tooltipItem.yLabel
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      } else if (chartId == 955) {
        label +=
          '$' +
          tooltipItem.yLabel
            .toFixed(2)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      } else if (chartId == 1090) {
        label +=
          '$' +
          tooltipItem.yLabel
            .toFixed(0)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      } else if (chartId == 1096) {
        label += tooltipItem.yLabel.toFixed(4);
      } else if (chartId == 1073 || chartId == 966) {
        label += (tooltipItem.yLabel * 100).toFixed(1) + ' %';
        break;
      } else {
        label += tooltipItem.yLabel
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      }
      break;

    case 4:
      if (label) {
        label += ': ';
      }
      label +=
        '$' +
        tooltipItem.yLabel.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      break;

    case 5:
      if (label) {
        label += ': ';
      }
      if (chartId == 940 && isStoreCompare == true) {
        label += (tooltipItem.yLabel * 100).toFixed(1) + '%';
        break;
      } else if (chartId == 940) {
        label += (tooltipItem.yLabel * 100).toFixed(1) + '%';
        break;
      } else {
        label += Math.round(tooltipItem.yLabel * 100);
        break;
      }

    case 6:
      if (label) {
        label += ': ';
      }
      if (chartId == 916 || chartId == 1238 || chartId == 1334) {
        label += tooltipItem.yLabel.toFixed(4);
      } else if (chartId == 920 || chartId == 925) {
        label += tooltipItem.yLabel
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      } else if (chartId == 952 || chartId == 944) {
        label +=
          '$' +
          tooltipItem.yLabel
            .toFixed(2)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      } else if (chartId == 918) {
        label += tooltipItem.yLabel
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      } else {
        label += tooltipItem.yLabel;
      }
      break;
    case 7:
      if (label) {
        label += ': ';
      }
      if (chartId == 1073 || chartId == 966 || chartId == 926) {
        label += (tooltipItem.yLabel * 100).toFixed(1) + ' %';
        break;
      } else {
        label += tooltipItem.yLabel * 100 + ' %';
        break;
      }
    case 8:
    case 3:
      if (label) {
        label += ': ';
      }
      if (chartId == 924) {
        Math.sign(tooltipItem.yLabel) > -1
          ? (label +=
              '$' +
              tooltipItem.yLabel
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ','))
          : (label +=
              '-$' +
              Math.abs(tooltipItem.yLabel)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ','));
      } else if (chartId == 931 || chartId == 926 || chartId == 921) {
        Math.sign(opporLabel) != -1
          ? (label +=
              '$' +
              opporLabel
                .toFixed(2)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ','))
          : (label +=
              '-$' +
              Math.abs(opporLabel.toFixed(2))
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ','));
      } else if (chartId == 1138 || chartId == 1143 || chartId == 1326) {
        label += tooltipItem.yLabel
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      } else {
        Math.sign(tooltipItem.yLabel) > 0
          ? (label +=
              '$' +
              // Math.round(tooltipItem.yLabel)
              tooltipItem.yLabel
                .toFixed(2)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ','))
          : // .toFixed(2)
          // .toString()
          // .replace(/\B(?=(\d{3})+(?!\d))/g, ','))
          Math.round(Math.abs(tooltipItem.yLabel)) == 0
          ? (label +=
              '$' +
              Math.round(Math.abs(tooltipItem.yLabel))
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ','))
          : (label +=
              '-$' +
              Math.round(Math.abs(tooltipItem.yLabel))
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ','));
        // .toFixed(2)
        // .toString()
        // .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      }
      break;

    case 9:
      if (label) {
        label += ': ';
      }
      if (chartId == 1357) {
        label += tooltipItem.yLabel;
      } else {
        label += Math.round(tooltipItem.yLabel * 100) + ' %';
      }

      break;
    case 10:
      if (label) {
        label += ': ';
      }
      label += ((tooltipItem.yLabel / 100) * 100).toFixed(2) + ' %';
      break;
    case 11:
      if (label) {
        label += ': ';
      }
      if (
        chartId == 1175 ||
        chartId == 1105 ||
        chartId == 1106 ||
        chartId == 1107
      ) {
        label +=
          '$' +
          tooltipItem.yLabel
            .toFixed(2)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      } else {
        label += tooltipItem.yLabel;
      }
      break;
    case 12:
      if (label) {
        label += ': ';
      }
      if (
        chartId == 1115 ||
        chartId == 1232 ||
        chartId == 1233 ||
        chartId == 1235
      ) {
        label += tooltipItem.yLabel.toFixed(2) + ' %';
      } else {
        if (chartId == 938) {
          label += tooltipItem.yLabel.toFixed(2) + ' %';
        } else {
          label += (tooltipItem.yLabel * 100).toFixed(2) + ' %';
        }
      }
      break;
    case 13:
      if (label) {
        label += ': ';
      }
      if (
        chartId == 1111 ||
        chartId == 1164 ||
        chartId == 1165 ||
        chartId == 1234 ||
        chartId == 1237 ||
        chartId == 1236 ||
        chartId == 1239
      ) {
        label +=
          '$' +
          tooltipItem.yLabel
            .toFixed(2)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      } else if (chartId == 936 || chartId == 930) {
        label +=
          'P' +
          '$' +
          tooltipItem.yLabel
            .toFixed(2)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
          ' to L$1.00';
      } else if (chartId == 1113 || chartId == 1100) {
        label += tooltipItem.yLabel;
      } else {
        label += tooltipItem.yLabel.toFixed(2);
      }
      break;
    case 16:
      if (label) {
        label += ': ';
      }
      label += tooltipItem.yLabel;
      break;
    case 15:
      if (label) {
        label += ': ';
      }
      label += tooltipItem.yLabel;
      break;
    case 17:
    case 20:
      if (label) {
        label += ': ';
      }
      if (chartId == 1109 || chartId == 1110 || chartId == 1116) {
        label +=
          '$' +
          tooltipItem.yLabel
            .toFixed(2)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      } else if (chartId == 1117) {
        label += tooltipItem.yLabel.toFixed(4);
      } else if (chartId == 1103 || chartId == 1104) {
        label += tooltipItem.yLabel;
      } else {
        label += tooltipItem.yLabel.toFixed(2);
      }
      break;
    default:
      if (label) {
        label += ': ';
      }
      label +=
        '$' +
        tooltipItem.yLabel.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      break;
  }

  return label;
}

/**
 *
 * @param {*} key key of the grid
 * @param {*} value  layout configuration
 * @param {*} viewid value in local storage
 */
export function saveLayoutConfiguration(key, value, viewid) {
  if (global.localStorage) {
    global.localStorage.setItem(
      viewid,
      JSON.stringify({
        [key]: value
      })
    );
  }
}

export function isBarChart(chartId) {
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(
    item =>
      ((item.dbdName == 'AddOns' || item.dbdName == 'Special Metrics') &&
        item.parentId == null) ||
      (item.dbdName == 'Discounts' &&
        (item.parentId == null || item.parentId != item.chartId))
  );
  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
  const barcharts = [];
  orderedData.map((item, index) => {
    barcharts.push(item.chartId);
  });

  return barcharts.includes(chartId);
}

export function isWormixChart(chartId) {
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(
    item =>
      (item.dbdName == 'Labor Workmix' || item.dbdName == 'Parts Workmix') &&
      item.parentId == null
  );
  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
  const barcharts = [];
  orderedData.map((item, index) => {
    barcharts.push(item.chartId);
  });
  return barcharts.includes(chartId);
}

export function isTechChart(chartId) {
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(
    item => item.dbdName == 'Technician Efficiency' && item.parentId == null
  );
  const barcharts = [];
  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
  orderedData.map((item, index) => {
    barcharts.push(item.chartId);
  });
  return barcharts.includes(chartId);
}

export function isTechComparisonChart(chartId) {
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(
    item =>
      item.dbdName == 'Technician Efficiency-Month Comparison' &&
      item.parentId == null
  );
  const barcharts = [];
  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
  orderedData.map((item, index) => {
    barcharts.push(item.chartId);
  });
  return barcharts.includes(chartId);
}

export function isAdvisorComparisonChart(chartId) {
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(
    item =>
      item.dbdName == 'Service Advisor-Month Comparison' &&
      item.parentId == null
  );
  const barcharts = [];
  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
  orderedData.map((item, index) => {
    barcharts.push(item.chartId);
  });
  return barcharts.includes(chartId);
}

export function isAdvisorOpcategoryChart(chartId) {
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(
    item =>
      item.dbdName == 'Service Advisor-Opcategory By Month' &&
      item.parentId == null
  );
  const barcharts = [];
  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
  orderedData.map((item, index) => {
    barcharts.push(item.chartId);
  });
  return barcharts.includes(chartId);
}

export function isOpportunityChart(chartId) {
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(
    item =>
      (item.dbdName == 'CP Labor Gross Volume Opportunity ' ||
        item.dbdName == 'CP Parts Gross Volume Opportunity ' ||
        item.dbdName == 'ELR Opportunity') &&
      item.parentId == null
  );
  const barcharts = [];
  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
  orderedData.map((item, index) => {
    barcharts.push(item.chartId);
  });
  return barcharts.includes(chartId);
}

export function isMonthComparisonChart(chartId) {
  const barcharts = [
    '1114',
    '1125',
    '1259',
    '1260',
    '1261',
    '1262',
    '1263',
    '1121',
    '1122'
  ];
  return barcharts.includes(chartId);
}

export function isAddonsComparisonChart(chartId) {
  const barcharts = ['1118', '1119', '1120'];
  return barcharts.includes(chartId);
}

export function isPartsComparisonChart(chartId) {
  const barcharts = ['1309', '1310', '1311', '1312', '1313'];
  return barcharts.includes(chartId);
}

export function isDiscountComparisonApexChart(chartId) {
  const barcharts = ['1124', '1126'];
  return barcharts.includes(chartId);
}
export function isAdvisorChart(chartId) {
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(
    item =>
      item.dbdName == 'Service Advisor Perfomance' && item.parentId == null
  );
  const barcharts = [];
  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
  orderedData.map((item, index) => {
    barcharts.push(item.chartId);
  });
  return barcharts.includes(chartId);
}
export function isThreeYearChart(chartId) {
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(
    item =>
      (item.dbdName == 'CP Labor Overview' ||
        item.dbdName == 'CP Parts Overview') &&
      item.parentId == null
  );
  const linecharts = [];
  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
  orderedData.map((item, index) => {
    linecharts.push(item.chartId);
  });
  return linecharts.includes(chartId.toString());
}

export function getAllChartDetails() {
  const start = new Date();
  const client = makeApolloClient;
  client
    .query({
      query: GET_ALL_CHART_DETAILS
    })
    .then(result => {
      const spanAttribute = {
        pageUrl: '/ChartMaster',
        origin: '',
        event: 'Menu Load',
        is_from: 'GET_ALL_CHART_DETAILS',
        value: new Date() - start,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Menu Load', spanAttribute);
      if (
        result.data.fdwStatefulServiceConfigurationChartMasters.nodes.length > 0
      ) {
        if (global.localStorage) {
          global.localStorage.setItem(
            'chart-master',
            JSON.stringify(
              result.data.fdwStatefulServiceConfigurationChartMasters.nodes
            )
          );
        }
        window.$chartList =
          result.data.fdwStatefulServiceConfigurationChartMasters.nodes;
      }
    });
}

export function getChartTitle(chartId) {
  let title = '';
  if (global.localStorage) {
    try {
      let ls = JSON.parse(global.localStorage.getItem('chart-master')) || [];
      if (ls.length > 0) {
        ls.map(item => {
          if (chartId == item.chartId) {
            title = item.chartName;
          }
        });
      }
    } catch (e) {
      /*Ignore*/
    }
  }

  return title;
}

function dateRange(startDate, endDate, parent) {
  var start = startDate && startDate.split('-');
  var end = endDate && endDate.split('-');
  var startYear = parseInt(start[0]);
  var endYear = parseInt(end && end[0]);
  var dates = [];

  for (var i = startYear; i <= endYear; i++) {
    var endMonth = i != endYear ? 11 : parseInt(end[1]) - 1;
    var startMon = i === startYear ? parseInt(start[1]) - 1 : 0;
    for (var j = startMon; j <= endMonth; j = j > 12 ? j % 12 || 11 : j + 1) {
      var month = j + 1;
      var displayMonth = month < 10 ? '0' + month : month;
      if (parent == 'opportunity') {
        dates.push(moment([i, displayMonth].join('-')).format('MMM YYYY'));
      } else {
        dates.push([i, displayMonth].join('-'));
      }
    }
  }
  if (parent == 'opportunity' || parent == 'elr_opportunity') {
    let el = dates.shift();
  }
  return dates;
}
/*export function getLast13Months() {
  var endDate = moment(new Date().setMonth(new Date().getMonth() - 2)).format(
    'YYYY-MM-DD'
  );
  var startDate = moment(
    new Date().setMonth(new Date().getMonth() - 14)
  ).format('YYYY-MM-DD');
  return dateRange(startDate, endDate);
}*/
export function getLast13Months(parent) {
  let dateRanges = localStorage.getItem('13Months').split(',');
  var endDate = dateRanges[1].slice(0, -3);
  var startDate = dateRanges[0].slice(0, -3);
  // var endDate = realm == 'ferrarioat_store' ? "2021-06" : "2020-12";
  // var startDate = realm == 'ferrarioat_store' ? "2020-06" : "2019-12";
  return dateRange(startDate, endDate, parent);
}
export function getLastThreeYears(parent) {
  let dateRanges = localStorage.getItem('3Years').split(',');
  var endDate = dateRanges[1].slice(0, -3);
  var startDate = dateRanges[0].slice(0, -3);
  // var endDate = realm == 'ferrarioat_store' ? "2021-06" : "2020-12";
  // var startDate = realm == 'ferrarioat_store' ? "2020-06" : "2019-12";
  return dateRange(startDate, endDate, parent);
}
// export function getComparisonMonths() {
//   let dateRanges = localStorage.getItem('13Months')
//     ? localStorage.getItem('13Months').split(',')
//     : '';
//   var endDate = dateRanges[1].slice(0, -3);
//   var startDate = moment(dateRanges[1])
//     .subtract(1, 'months')
//     .format('YYYY-MM');
//   return dateRange(startDate, endDate);
// }

export function getComparisonMonths() {
  if (localStorage.getItem('versionFlag') == 'TRUE') {
    let dateRanges = localStorage.getItem('3Years')
      ? localStorage.getItem('3Years').split(',')
      : '';
    var endDate = dateRanges[1] && dateRanges[1].slice(0, -3);
    var startDate = moment(dateRanges[1])
      .subtract(1, 'months')
      .format('YYYY-MM');
    return dateRange(startDate, endDate);
  } else {
    let dateRanges = localStorage.getItem('13Months')
      ? localStorage.getItem('13Months').split(',')
      : '';
    var endDate = dateRanges[1].slice(0, -3);
    var startDate = moment(dateRanges[1])
      .subtract(1, 'months')
      .format('YYYY-MM');
    return dateRange(startDate, endDate);
  }
}

export function getLastSixMonths() {
  let dateRanges = localStorage.getItem('13Months').split(',');
  var startDate = moment(dateRanges[1])
    .subtract(5, 'months')
    .format('YYYY-MM');
  return [startDate + '-01', dateRanges[1]];
}

export function getHasuraRenderedCharts() {
  return [
    '939',
    '942',
    '940',
    '946',
    '1072',
    '967',
    '1097',
    '937',
    '988',
    '987',
    '986',
    '1066',
    '1073',
    '1127',
    '1098',
    '1356',
    '966',
    '916',
    '1238',
    '1128',
    '1129',
    '1130',
    '1131',
    '1132',
    '949',
    '1076',
    '1077',
    '1078',
    '1083',
    '1079',
    '953',
    '1012',
    '1015',
    '1014',
    '1013',
    '1091',
    '1092',
    '1092',
    '1094',
    '955',
    '1084',
    '1085',
    '1086',
    '1087',
    '1088',
    '1044',
    '1318',
    '923',
    '1355',
    '930',
    '935',
    '936',
    '1357',
    '938',
    '1174',
    '1175',
    '1176',
    '1177',
    '1178',
    '1179',
    '1180',
    '1181',
    '1182',
    '1183',
    '1184',
    '1185',
    '1186',
    '1187',
    '1188',
    '1189',
    '1197',
    '1198',
    '1199',
    '1200',
    '1201',
    '1202',
    '1203',
    '1211',
    '1212',
    '1213',
    '1214',
    '1215',
    '1216',
    '1217',
    '1218',
    '1219',
    '1220',
    '1221',
    '1222',
    '1223',
    '1224',
    '1225',
    '1226',
    '1227',
    '1228',
    '1229',
    '1230',
    '1231',
    '1316',
    '1317',
    '1319',
    '1320',
    '1321',
    '1322',
    '1323',
    '1324',
    '1325',
    '1334',
    '931',
    '926',
    '921'
  ];
}
var defaultLegendClickHandler = function(e, legendItem) {
  var index = legendItem.datasetIndex;
  var ci = this.chart;
  var meta = ci.getDatasetMeta(index);

  // See controller.isDatasetVisible comment
  meta.hidden = meta.hidden === null ? !ci.data.datasets[index].hidden : null;

  // We hid a dataset ... rerender the chart
  ci.update();
};
var newLegendClickHandler = function(e, legendItem) {
  var index = legendItem.datasetIndex;
  if (index > 1) {
    // Do the original logic
    defaultLegendClickHandler(e, legendItem);
  } else {
    let ci = this.chart;
    [ci.getDatasetMeta(0), ci.getDatasetMeta(1)].forEach(function(meta) {
      meta.hidden =
        meta.hidden === null ? !ci.data.datasets[index].hidden : null;
    });
    ci.update();
  }
};

export function getDataforComparisonChart(resultSet) {
  const COLORS_SERIES = ['#3366CC', '#DC3912'];
  let dimensions = [];
  resultSet.map((val, i) => {
    val['dimension'].map((val, i) => {
      dimensions.push(val);
    });
  });
  const data = {
    //labels: lodash.uniqBy(dimensions).map(val => dateFormatter(val)),
    labels: lodash.uniqBy(dimensions).map(val => dateFormatterWithYear(val)),
    datasets: resultSet.map((s, index) => ({
      label: s['name'],
      data: s['data'],
      borderColor: COLORS_SERIES[index],
      backgroundColor: COLORS_SERIES[index],
      fill: false,
      type: 'line',
      borderWidth: 1.5,
      lineTension: 0,
      pointRadius: 1.5
    }))
  };
  return data;
}

export function getComparisonChartConfiguration(chartId, resultSet, chartType) {
  const options = {
    maintainAspectRatio: false,
    tooltips: {
      backgroundColor: '#2121216e',
      mode: 'index',
      axis: 'y',
      position: 'nearest',
      callbacks: {
        label: function(tooltipItem, data) {
          return getTootipLabel(chartType, tooltipItem, data, chartId, true);
        },
        title: function(tooltipItem, data) {
          tooltipItem = resultSet[0].dimension[tooltipItem[0].index];
          return dateFormatterWithYear(tooltipItem);
        }
      }
    },
    plugins: {
      datalabels: { display: false }
    },
    legend: {
      position: 'bottom',
      align: 'center',
      labels: {
        boxWidth: 12,
        usePointStyle: false
      }
    },
    scales: {
      yAxes: [
        {
          display: true,
          scaleLabel: {
            display: false,
            labelString: ''
          },
          ticks: {
            maxTicksLimit: 5,
            // maxTicksLimit: isServiceAdvisorFilter ? 5 : undefined,
            callback: function(value, index, values) {
              return getYAxisLabel(chartType, value, chartId);
            }
          }
        }
      ],
      xAxes: [
        {
          display: true,
          scaleLabel: {
            display: false,
            labelString: ''
          },
          ticks: {
            beginAtZero: true,
            autoSkip: true,
            maxRotation: 45,
            minRotation: 45,

            callback: function(value, index, values) {
              // return  dateFormatterWithYearLabel(value);
              return value;
            }
          }
        }
      ]
    }
  };
  if (chartId == 920) {
    options.scales.yAxes[0].ticks.maxTicksLimit = 5;
  }

  return options;
}

export function getIndexForOpportunityCharts(chartId) {
  switch (chartId) {
    case 931:
    case 926:
      return 0;
    case 932:
    case 929:
      return 4;
    case 933:
    case 927:
      return 3;
    case 934:
    case 928:
      return 5;
  }
}

export function createDataForTrancheReports(data) {
  var result = [];
  if (data.reportName == 'Best 100 ROs') {
    result.push({
      ronumber: 'Total',
      lbrsale: data.labrSale,
      lbrsoldhours: data.soldHours,
      elr: data.elr
    });
  } else if (
    data.reportName == 'Included Op Codes' ||
    data.reportName == 'WARRANTY OPPORTUNITY'
  ) {
    result.push({
      lbropcode:
        data.reportName == 'Included Op Codes' ? 'Total Includes' : 'Total',
      laborsale: data.laborsale,
      frh: data.frh,
      elr: data.elr,
      monthlyImpact: data.monthlyImpact,
      annualImpact: data.annualImpact
    });
  } else {
    result.push({
      lbropcode: 'Total Excludes',
      laborsale: data.laborsale,
      frh: data.frh,
      elr: data.elr
    });
  }
  return result;
}
function isLastDay(dt) {
  var test = new Date(dt.getTime());
  test.setDate(test.getDate() + 1);
  return test.getDate() === 1;
}
export function checkClosedDateInCurrentMonth(ClosedDate, OpenDate) {
  if (ClosedDate && OpenDate) {
    localStorage.setItem('closedDate', ClosedDate);
    localStorage.setItem('openDate', OpenDate);
  }
  var date1 = new Date(ClosedDate);
  // date1 = new Date(date1.getTime() + date1.getTimezoneOffset() * 60000);
  var date2 = new Date();
  date1 = moment(ClosedDate)._d;
  var lday = isLastDay(new Date(date2));
  var sday = isLastDay(new Date(OpenDate));

  if (lday) {
    if (
      date1.getFullYear() == date2.getFullYear() &&
      date1.getMonth() == date2.getMonth() &&
      date1.getDate() == date2.getDate()
    ) {
      //
      console.log('lday---false--true');
      return 1;
    } else {
      console.log('lday---false');
      return false;
    }
  } else {
    console.log(isLastDay(new Date(date1)));
    if (isLastDay(new Date(date1))) {
      if (
        date1.getMonth() != date2.getMonth() &&
        date1.getDate() != date2.getDate()
      ) {
        console.log('lday---false--11');
        return 1;
      } else {
        console.log('lday---false--22');
        return true;
      }
    } else {
      console.log('lday---false--44');
      return false;
    }
  }
}

// export function checkClosedDateInCurrentMonth(ClosedDate, OpenDate) {
//   if (ClosedDate && OpenDate) {
//     localStorage.setItem('closedDate', ClosedDate);
//     localStorage.setItem('openDate', OpenDate);
//   }
//   if (localStorage.getItem('closedDate') && localStorage.getItem('openDate')) {
//     let closedDate = localStorage.getItem('closedDate');
//     let openDate = localStorage.getItem('openDate');
//     var selecteddate = closedDate;
//     var datestr = selecteddate.split('-');
//     var month = datestr[1];
//     var year = datestr[0];
//     var openStr = openDate.split('-');
//     var openMonth = openStr[1];
//     var openYear = openStr[0];
//     // var lastDate = new Date(year, (month - 1) +1, 1).toISOString().slice(0, 10);
//     var startDate = openYear + '-' + openMonth + '-05';
//     var endDate = year + '-' + month + '-25';
//     if (
//       selecteddate < endDate ||
//       (openYear >= year - 2 && openDate > startDate)
//     ) {
//       return true;
//     } else {
//       return false;
//     }
//   }
// }
export function getFilterMonthIndex(data, month) {
  let arrIndex = '';
  data &&
    data.labels &&
    data.labels.filter((value, index) => {
      if (value.split('-')[1] == month) {
        arrIndex = index;
      }
    });
  return arrIndex;
}

export function getFilterMonthIndexWorkmix(data, month) {
  let arrIndex = '';
  data &&
    data[0].labels &&
    data[0].labels.filter((value, index) => {
      if (value.split('-')[1] == month) {
        arrIndex = index;
      }
    });
  return arrIndex;
}

export function applyPartialMonthFilterWorkMix(data) {
  var selecteddate = localStorage.getItem('closedDate');
  var datestr = selecteddate.split('-');
  var month = datestr[1];
  var year = datestr[0];
  selecteddate = moment(selecteddate)._d;
  // var lastDate = new Date(year, (month - 1) +1, 1).toISOString().slice(0, 10);
  //var lastDate = year + '-' + month + '-25';
  var date = new Date(selecteddate),
    y = date.getFullYear(),
    m = date.getMonth();
  var lastDay = new Date(y, m + 1, 0);
  var lastDate = new Date(
    lastDay.getTime() - lastDay.getTimezoneOffset() * 60000
  );

  var openDate = localStorage.getItem('openDate');
  // var openStr = openDate.split('-');
  // var openMonth = openStr[1];
  // var openYear = openStr[0];
  // var startDate = openYear + '-' + openMonth + '-01';

  var date = new Date(openDate),
    y = date.getFullYear(),
    m = date.getMonth();
  var firstday = new Date(y, m, 1);
  var openStr = openDate.split('-');
  var openMonth = openStr[1];
  var openYear = openStr[0];
  var startDate = new Date(
    firstday.getTime() - firstday.getTimezoneOffset() * 60000
  )
    .toISOString()
    .split('T')[0];
  var closedMonthIndex = getFilterMonthIndexWorkmix(data, month);

  if (data.length > 1) {
    if (selecteddate < lastDate) {
      if (
        data[0].data &&
        // closedMonthIndex &&
        (closedMonthIndex != '' || closedMonthIndex == 0)
      ) {
        data[0].data = data[0].data.filter(
          (value, index) => index < closedMonthIndex
        );
      } else if (data[0].data && closedMonthIndex == '') {
        data[0].data = data[0].data.filter(
          (value, index) => index <= month - 1
        );
      }
    }
  }
  // if (selecteddate < lastDate) {
  //   data[0].data = data[0].data.filter((value, index) => index < month - 1);
  // }
  // if (openYear >= year - 2 && openDate > startDate) {
  //   data[2].data.filter((value, index) => {
  //     if (index > openMonth - 1) {
  //       data[2].data[index] = data[2].data[index];
  //     } else {
  //       data[2].data[index] = null;
  //     }
  //   });
  // }
  return data;
  // if(selecteddate < lastDate)
  //   {
  //     data[2].data = data[2].data.filter((value ,index) => index < month-1)
  //     return data[2].data
  //   }
}
export function applyPartialMonthFilterLineCharts(data) {
  var selecteddate = localStorage.getItem('closedDate');
  var datestr = selecteddate.split('-');
  var monthVal = datestr[1];
  var selectedDateObj = moment(selecteddate).toDate();
  var year = selectedDateObj.getFullYear();
  var month = selectedDateObj.getMonth();
  var lastDay = new Date(year, month + 1, 0);
  selectedDateObj.setHours(lastDay.getHours());
  selectedDateObj.setMinutes(lastDay.getMinutes());
  selectedDateObj.setSeconds(lastDay.getSeconds());
  selectedDateObj.setMilliseconds(lastDay.getMilliseconds());
  selecteddate = selectedDateObj;
  var lastDate = lastDay;

  var openDate = localStorage.getItem('openDate');
  var date = new Date(openDate),
    y = date.getFullYear(),
    m = date.getMonth();
  var firstday = new Date(y, m, 1);
  var openStr = openDate.split('-');
  var openMonth = openStr[1];
  var openYear = openStr[0];
  var startDate = new Date(
    firstday.getTime() - firstday.getTimezoneOffset() * 60000
  )
    .toISOString()
    .split('T')[0];

  var openMonthIndex = getFilterMonthIndex(data, openMonth);
  var closedMonthIndex = getFilterMonthIndex(data, monthVal);
  // console.log('lastDate===', lastDay, lastDate, closedMonthIndex);
  if (data.datasets.length > 1) {
    if (selecteddate < lastDate) {
      if (
        data.datasets[0].data &&
        // closedMonthIndex &&
        (closedMonthIndex != '' || closedMonthIndex == 0)
      ) {
        data.datasets[0].data = data.datasets[0].data.filter(
          (value, index) => index < closedMonthIndex
        );
      } else if (data.datasets[0].data && closedMonthIndex == '') {
        data.datasets[0].data = data.datasets[0].data.filter(
          (value, index) => index <= monthVal - 1
        );
      }
    } else if (selecteddate.toString() == lastDate.toString()) {
      if (data.datasets[0].data) {
        data.datasets[0].data = data.datasets[0].data.filter(
          (value, index) => index <= monthVal - 1
        );
      }
    }
    // if (openYear >= year - 2 && openDate > startDate) {
    //   data.datasets[2].data.filter((value, index) => {
    //     if (index > openMonthIndex) {
    //       data.datasets[0].data[index] = data.datasets[0].data[index];
    //     } else {
    //       data.datasets[0].data[index] = null;
    //     }
    //   });
    // }

    return data;
  }
}

export function applyPartialMonthFilterLineCharts_OLD(data) {
  var selecteddate = localStorage.getItem('closedDate');
  var datestr = selecteddate.split('-');
  var month = datestr[1];
  var year = datestr[0];
  selecteddate = moment(selecteddate)._d;
  // var lastDate = new Date(year, (month - 1) +1, 1).toISOString().slice(0, 10);
  var date = new Date(selecteddate),
    y = date.getFullYear(),
    m = date.getMonth();
  var lastDay = new Date(y, m + 1, 0);
  var lastDate = new Date(
    lastDay.getTime() - lastDay.getTimezoneOffset() * 60000
  );

  var openDate = localStorage.getItem('openDate');
  var date = new Date(openDate),
    y = date.getFullYear(),
    m = date.getMonth();
  var firstday = new Date(y, m, 1);
  var openStr = openDate.split('-');
  var openMonth = openStr[1];
  var openYear = openStr[0];
  var startDate = new Date(
    firstday.getTime() - firstday.getTimezoneOffset() * 60000
  )
    .toISOString()
    .split('T')[0];

  var openMonthIndex = getFilterMonthIndex(data, openMonth);
  var closedMonthIndex = getFilterMonthIndex(data, month);
  // console.log('lastDate===', lastDay, lastDate, closedMonthIndex);

  if (data.datasets.length > 1) {
    if (selecteddate < lastDate) {
      if (
        data.datasets[0].data &&
        // closedMonthIndex &&
        (closedMonthIndex != '' || closedMonthIndex == 0)
      ) {
        data.datasets[0].data = data.datasets[0].data.filter(
          (value, index) => index < closedMonthIndex
        );
      } else if (data.datasets[0].data && closedMonthIndex == '') {
        data.datasets[0].data = data.datasets[0].data.filter(
          (value, index) => index <= month - 1
        );
      }
    }
    // if (openYear >= year - 2 && openDate > startDate) {
    //   data.datasets[2].data.filter((value, index) => {
    //     if (index > openMonthIndex) {
    //       data.datasets[0].data[index] = data.datasets[0].data[index];
    //     } else {
    //       data.datasets[0].data[index] = null;
    //     }
    //   });
    // }

    return data;
  }
}
export function titleCase(text) {
  return text
    .toLowerCase()
    .split(' ')
    .map(value => {
      return value.charAt(0).toUpperCase() + value.substring(1);
    })
    .join(' ');
}

export function onBtExportExcludedMakes() {
  const params = {
    sheetName: 'Report',
    columnWidth: 150,
    columnKeys: ['makesId', 'manufacturer', 'excludedMakes'],
    fileName: 'Excluded Makes',
    customHeader: [
      [],
      [
        {
          styleId: 'bigHeader',
          data: { type: 'String', value: 'Excluded Makes' },
          mergeAcross: 3
        }
      ]
    ]
  };
  return params;
}
export function formatCellValue(params) {
  if (params != null && params != 0) {
    return Math.sign(params) > -1
      ? '$' +
          parseFloat(params)
            .toFixed(2)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      : '-$' +
          Math.abs(parseFloat(params))
            .toFixed(2)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  } else {
    if (params == '  ' || params == '') {
      return params;
    } else {
      return 0;
    }
  }
}
export function formatCellValuePerc(heading, params) {
  if (heading.startsWith('-')) {
    if (params != null && params != 0) {
      return Math.sign(params) > -1
        ? parseFloat(params)
            .toFixed(2)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
        : Math.abs(parseFloat(params))
            .toFixed(2)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%';
    } else {
      if (params == '  ' || params == '') {
        return params;
      } else {
        return '0.00%';
      }
    }
  } else {
    if (params != null && params != 0) {
      return Math.sign(params) > -1
        ? '$' +
            parseFloat(params)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      if (params == '  ' || params == '') {
        return params;
      } else {
        return 0;
      }
    }
  }
}
export function formatCellValueCount(params) {
  if (params != null && params != 0) {
    return (Math.round(Number(params) * 100) / 100).toLocaleString();
  } else {
    if (params == '  ' || params == '') {
      return params;
    } else {
      return 0;
    }
  }
}

export function getTooltipForDuplicateDashboards(chartId) {
  let title = [];
  if (global.localStorage) {
    try {
      let ls = JSON.parse(global.localStorage.getItem('chart-master')) || [];

      if (ls.length > 0) {
        ls.map(item => {
          if (chartId == item.chartId) {
            title.push(item.duplicateDbd);
          }
        });
      }
    } catch (e) {
      /*Ignore*/
    }
  }

  return title;
}

export function getChartConfigurationKPIs(resultSet, chartId, parent) {
  //  let max = Math.max.apply(Math, resultSet.map(function(item) { return item.val; }))
  const options = {
    maintainAspectRatio: false,
    tooltips: {
      enabled: false,
      titleFontColor: '#003d6b',
      bodyFontColor: '#003d6b',
      borderColor: '#003d6b',
      borderWidth: 1,
      backgroundColor: '#ddeaf4',
      mode: 'index',
      axis: 'y',
      yPadding: 10,
      position: 'nearest',
      callbacks: {
        label: function(tooltipItem, resultSet) {
          return getTootipLabelKPI(tooltipItem, resultSet);
        }
      }
    },
    layout: {
      padding: {
        left: 0,
        right: 0,
        top: 30,
        bottom: 0
      }
    },
    scales: {
      xAxes: [
        {
          //barPercentage: 0.4,
          ticks: {
            fontSize:
              parent == 'Expanded_View' && window.innerWidth > 2000 ? 18 : 14,
            fontStyle: 'bold'
          }
        }
      ],
      yAxes: [
        {
          ticks: {
            fontSize:
              parent == 'Expanded_View' && window.innerWidth > 2000 ? 18 : 12,
            maxTicksLimit: 5,
            callback: function(value, index, values) {
              return value
                ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                : 0;
            }
            // max: Math.ceil(Math.round(max+max)/10) *10,
          }
        }
      ]
    },

    legend: {
      display: chartId == 1335 ? false : true,
      position:
        chartId == 1336 && resultSet.length > 0 && resultSet[2].val != 1
          ? 'bottom'
          : 'right',
      align: 'center',
      labels: {
        boxWidth: 12,
        usePointStyle: false,
        padding: 5
      }
    },

    animation: {
      animateScale: true
    }
  };
  return options;
}
export function getTootipLabelKPI(tooltipItem, resultSet) {
  var label = '';
  label = resultSet.labels[tooltipItem.index] || '';
  label += ': ';
  label += resultSet.datasets[0].data[tooltipItem.index]
    .toString()
    .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return label;
  //return resultSet.labels[tooltipItem.index] +': '+datasets[0].data[tooltipItem.index];
}
export function getLabelforKPIs(data) {
  let label = [];
  data.map(val => {
    if (val.label == 'ctotal' || val.label == 'Customer Pay Vehicles') {
      label.push('CP');
    } else if (val.label == 'wtotal' || val.label == 'Warranty Vehicles') {
      label.push('Wty');
    } else if (val.label == 'itotal' || val.label == 'Internal Vehicles') {
      label.push('Int');
    } else if (val.label == 'ttotal' || val.label == 'All Vehicles') {
      label.push('All');
    } else if (val.label == 'totalro' || val.label == 'totalRos') {
      label.push('Total RO');
    } else if (val.label == 'totalshare') {
      label.push('RO Share');
    } else if (val.label == 'totalroDuration') {
      label.push('Advisor RO');
    } else if (val.label == 'totalshareDuration') {
      label.push('Advisor Share');
    } else if (val.label == 'onelineRos') {
      label.push('1 Line RO');
    } else if (val.label == 'onelinePercentage') {
      label.push('1 Line RO%');
    } else if (val.label == 'multilineRos') {
      label.push('Multi-Line RO');
    } else if (val.label == 'multilinePercentage') {
      label.push('Multi-Line RO%');
    } else if (val.label == 'onelineLbrsale') {
      label.push('1 Line Lbr Sale');
    } else if (val.label == 'multilineLbrsale') {
      label.push('Multi-Line Lbr Sale');
    } else if (val.label == 'onelinePrtsale') {
      label.push('1 Line Prt Sale');
    } else if (val.label == 'multilinePrtsale') {
      label.push('Multi-Line Prt Sale');
    } else if (val.label == 'onelineTotalsale') {
      label.push('1 Line Total Sale');
    } else if (val.label == 'multilineTotalsale') {
      label.push('Multi-Line Total Sale');
    } else {
      label.push(val.label);
    }
  });
  return label;
  // return data.map(val => val.label);
}
export function getResultSetforKPIs(data) {
  return data.map(val => val.val);
}

export function getDataforKPIs(resultSet, chartId) {
  const data = {
    labels: getLabelforKPIs(resultSet),
    datasets: [
      {
        label: chartId == 1335 ? '' : 'My First Dataset',
        data: getResultSetforKPIs(resultSet),
        backgroundColor:
          chartId == 1336
            ? [
                // 'rgb(255, 99, 132)',
                '#1034a6',
                // '#3366CC',
                'rgb(54, 162, 235)',
                'rgb(60,179,113)',
                'rgb(255, 205, 86)'
              ]
            : chartId == 1337 || chartId == 1338 || chartId == 1351
            ? [
                '#DC3912',
                '#FF8C00',
                '#9b59b6',
                '#6495ED',
                '#dc3912',
                '#0e58e1',
                //'#b81414',
                '#dc3912',
                //0ee19d
                '#0e58e1'
              ]
            : chartId == 1344
            ? ['#357edd', '#00C642', '#fb3e44']
            : chartId == 1346 || chartId == 1353
            ? ['#dc3912', '#109618', '#f90']
            : ['#DC3912', '#3366CC', '#993399', '#FFA500'],
        hoverOffset: 4
      }
    ]
  };
  return data;
}

export function getOptionsForPie1(ROShareData, chartId, Advisor, parent) {
  let options = {
    maintainAspectRatio: false,
    cutoutPercentage:
      chartId == 1337 || chartId == 1338 || chartId == 1351 ? 60 : '',

    title: {
      display: true,
      fontSize: parent == 'Expanded_View' && window.innerWidth > 2000 ? 22 : 18,
      fontFamily: 'Roboto',
      //fontWeight: '500',
      text:
        chartId == 1337 || chartId == 1338 || chartId == 1351
          ? ''
          : ROShareData.length > 0 && ROShareData[2].val != 1
          ? [ROShareData[2].val + ' Days', ROShareData[3].val + '%']
          : '100%',
      position: 'top',
      padding: 0,
      lineHeight: 1
    },
    legend: {
      display:
        chartId == 1337 || chartId == 1338 || chartId == 1351 ? false : true,
      position:
        chartId == 1337 || chartId == 1338 || chartId == 1351
          ? 'bottom'
          : ROShareData.length > 0 && ROShareData[2].val != 1
          ? 'right'
          : 'right',
      align: 'center',
      labels: {
        boxWidth: 12,
        usePointStyle: false,
        padding: 18,
        fontSize:
          chartId == 1336 &&
          parent == 'Expanded_View' &&
          window.innerWidth > 2000
            ? 20
            : chartId == 1336 &&
              parent == 'Expanded_View' &&
              window.innerWidth < 2000
            ? 16
            : 14,
        fontFamily: 'Roboto',
        fontStyle: '600'
      }
    },
    tooltips: {
      enabled: false
    },
    hover: { mode: null },
    plugins: {
      datalabels: {
        display: true,
        color: '#FFF',
        borderColor:
          (Advisor != 'All' && chartId == 1336) ||
          chartId == 1337 ||
          chartId == 1338 ||
          chartId == 1351
            ? 'white'
            : '',
        borderWidth:
          (Advisor != 'All' && chartId == 1336) ||
          chartId == 1337 ||
          chartId == 1338 ||
          chartId == 1351
            ? 2
            : '',
        borderRadius:
          (Advisor != 'All' && chartId == 1336) ||
          chartId == 1337 ||
          chartId == 1338 ||
          chartId == 1351
            ? 25
            : 3,
        align:
          (Advisor != 'All' && chartId == 1336) ||
          chartId == 1337 ||
          chartId == 1338 ||
          chartId == 1351
            ? 'end'
            : // : chartId == 1337 || chartId == 1338 || chartId == 1351
              // ? 'none'
              'center',
        anchor:
          (Advisor != 'All' && chartId == 1336) ||
          chartId == 1337 ||
          chartId == 1338 ||
          chartId == 1351
            ? 'end'
            : 'none',
        offset:
          Advisor != 'All' && chartId == 1336
            ? -28
            : chartId == 1337 || chartId == 1338 || chartId == 1351
            ? -27
            : '',
        font: {
          color: 'white',
          weight: '600',
          size:
            Advisor == 'All' && chartId == 1336
              ? 20
              : parent == 'Expanded_View' && window.innerWidth > 2000
              ? 18
              : 14
        },
        backgroundColor: function(context) {
          return (Advisor != 'All' && chartId == 1336) ||
            chartId == 1337 ||
            chartId == 1338 ||
            chartId == 1351
            ? context.dataset.backgroundColor
            : '';
        },
        padding:
          (Advisor != 'All' && chartId == 1336) ||
          chartId == 1337 ||
          chartId == 1338 ||
          chartId == 1351
            ? 6
            : '',

        formatter: value => {
          return value
            ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            : 0;
        }
      },
      doughnutlabel: {
        labels: [
          {
            text:
              ROShareData.length > 0 &&
              (chartId == 1337 || chartId == 1338 || chartId == 1351)
                ? 'Tot. CP ROs'
                : ''
          },
          {
            text:
              ROShareData.length > 0 &&
              (chartId == 1337 || chartId == 1338 || chartId == 1351)
                ? ROShareData[0].val
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                : '',
            font: {
              size: 30,
              weight: 'bold'
            }
          }
        ]
      }
    }
  };
  return options;
}

export function getOptionsForPie2(ROShareData, chartId, type, Advisor, parent) {
  let options = {
    maintainAspectRatio: false,
    cutoutPercentage:
      chartId == 1337 || chartId == 1338 || chartId == 1351 ? 60 : '',
    layout: {
      padding: {
        left: 0,
        right:
          (chartId == 1337 || chartId == 1338 || chartId == 1351) &&
          type == 'bar_chart'
            ? 80
            : 0,
        top: 0,
        bottom: 0
      }
    },
    title: {
      display: true,
      fontSize:
        chartId == 1336 && parent == 'Expanded_View' && window.innerWidth > 2000
          ? 22
          : 18,
      fontFamily: 'Roboto',
      // fontWeight: '500',
      text:
        chartId == 1337 || chartId == 1338 || chartId == 1351
          ? ''
          : ROShareData.length > 0
          ? ['Per Day', ROShareData[3].val + '%']
          : '100%',
      position: 'top',
      padding: 0,
      lineHeight: 1
    },
    plotOptions: {
      column: {
        /* Here is the setting to limit the maximum column width. */
        pointWidth: 50
      }
    },
    legend: {
      display:
        (chartId == 1337 || chartId == 1338 || chartId == 1351) &&
        type != 'bar_chart'
          ? false
          : true,
      position:
        chartId == 1337 || chartId == 1338 || chartId == 1351
          ? 'bottom'
          : ROShareData.length > 0 && ROShareData[2].val != 1
          ? 'right'
          : 'right',
      align: 'center',
      itemWidth: 100,
      labels: {
        boxWidth: 12,
        usePointStyle: false,
        padding: 18,
        fontSize:
          chartId == 1336 &&
          parent == 'Expanded_View' &&
          window.innerWidth > 2000
            ? 20
            : chartId == 1336 &&
              parent == 'Expanded_View' &&
              window.innerWidth < 2000
            ? 16
            : 14,
        fontFamily: 'Roboto',
        fontStyle: '600'
      }
    },
    tooltips: {
      enabled: false
    },
    hover: { mode: null },
    scales: {
      yAxes: [
        {
          gridLines: {
            display: true,
            drawBorder: false,
            borderDash: [3, 3],
            zeroLineBorderDash: [3, 3],
            zeroLineWidth: 0
          },
          display:
            (chartId == 1337 || chartId == 1338 || chartId == 1351) &&
            type == 'bar_chart'
              ? true
              : false,
          scaleLabel: {
            display: false,
            labelString: ''
          },
          // barPercentage: 1,
          barPercentage: 0.6,
          ticks: {
            callback: function(value, index, values) {
              return value
                ? (chartId == 1337 || chartId == 1338 || chartId == 1351) &&
                  type == 'bar_chart'
                  ? value
                  : '$' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                : '';
            },
            fontSize:
              parent == 'Expanded_View' && window.innerWidth > 2000 ? 18 : 12,
            fontStyle: '500'
          }
        }
      ],
      xAxes: [
        {
          gridLines: {
            display: true,
            drawBorder: false,
            borderDash: [3, 3],
            zeroLineBorderDash: [3, 3],
            zeroLineWidth: 0
          },
          display:
            (chartId == 1337 || chartId == 1338 || chartId == 1351) &&
            type == 'bar_chart'
              ? true
              : false,
          ticks: {
            min: 0,
            display: false
            // fontSize: 18
          }
        }
      ]
    },
    plugins: {
      datalabels: {
        display: true,
        color:
          ((Advisor != 'All' && chartId == 1336) ||
            chartId == 1337 ||
            chartId == 1338 ||
            chartId == 1351) &&
          type == 'bar_chart'
            ? '#737373'
            : '#FFF',
        borderColor:
          ((chartId == 1337 || chartId == 1338 || chartId == 1351) &&
            type != 'bar_chart') ||
          (Advisor != 'All' && chartId == 1336)
            ? 'white'
            : '',
        borderWidth:
          ((Advisor != 'All' && chartId == 1336) ||
            chartId == 1337 ||
            chartId == 1338 ||
            chartId == 1351) &&
          type != 'bar_chart'
            ? 2
            : '',
        borderRadius:
          ((Advisor != 'All' && chartId == 1336) ||
            chartId == 1337 ||
            chartId == 1338 ||
            chartId == 1351) &&
          type != 'bar_chart'
            ? 25
            : 3,
        align:
          (Advisor != 'All' && chartId == 1336) ||
          // ((chartId == 1337 || chartId == 1338 || chartId == 1351) &&
          //   type == 'bar_chart')
          chartId == 1337 ||
          chartId == 1338 ||
          chartId == 1351
            ? 'end'
            : // : (chartId == 1337 || chartId == 1338 || chartId == 1351) &&
              //   type != 'bar_chart'
              // ? 'none'
              'center',
        anchor:
          (Advisor != 'All' && chartId == 1336) ||
          chartId == 1337 ||
          chartId == 1338 ||
          chartId == 1351
            ? 'end'
            : 'center',
        offset:
          (Advisor != 'All' && chartId == 1336) ||
          ((chartId == 1337 || chartId == 1338 || chartId == 1351) &&
            type != 'bar_chart')
            ? -28
            : (chartId == 1337 || chartId == 1338 || chartId == 1351) &&
              type == 'bar_chart'
            ? 3
            : '',
        font: {
          color:
            ((Advisor != 'All' && chartId == 1336) ||
              chartId == 1337 ||
              chartId == 1338 ||
              chartId == 1351) &&
            type == 'bar_chart'
              ? '#737373'
              : 'white',
          weight: '600',
          size:
            Advisor == 'All' && chartId == 1336
              ? 20
              : (chartId == 1337 || chartId == 1338 || chartId == 1351) &&
                type == 'bar_chart'
              ? parent == 'Expanded_View' && window.innerWidth > 2000
                ? 18
                : 16
              : parent == 'Expanded_View' && window.innerWidth > 2000
              ? 18
              : 14
        },
        backgroundColor: function(context) {
          return ((Advisor != 'All' && chartId == 1336) ||
            chartId == 1337 ||
            chartId == 1338 ||
            chartId == 1351) &&
            type != 'bar_chart'
            ? context.dataset.backgroundColor
            : '';
        },
        padding:
          ((Advisor != 'All' && chartId == 1336) ||
            chartId == 1337 ||
            chartId == 1338 ||
            chartId == 1351) &&
          type != 'bar_chart'
            ? 6
            : '',

        formatter: value => {
          return value
            ? (chartId == 1337 || chartId == 1338 || chartId == 1351) &&
              type == 'bar_chart'
              ? '$' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              : (chartId == 1337 || chartId == 1338 || chartId == 1351) &&
                type != 'bar_chart'
              ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
              : value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            : type == 'bar_chart'
            ? '$' + 0
            : 0;
        }
      },

      doughnutlabel: {
        labels: [
          // {
          //   text:
          //     ROShareData.length > 0 &&
          //     (chartId == 1337 || chartId == 1338 || chartId == 1351)
          //       ? 'Total'
          //       : ''
          // },
          {
            text:
              ROShareData.length > 0 &&
              (chartId == 1337 || chartId == 1338 || chartId == 1351) &&
              type != 'bar_chart'
                ? '%'
                : '',
            font: {
              size: 40,
              weight: 'bold'
            }
          }
        ]
      }
    }
  };
  return options;
}

export function getOptionsForHighChart(data, type) {
  let options = {
    //colors: getColorScheme(20),
    colors: getColorScheme(22),
    chart: {
      //backgroundColor: type == 'Expanded_View' ? '#f2dada2b' : 'white',
      style: {
        fontFamily: 'Roboto',
        color: '#757575'
      },
      type: 'pyramid'
    },
    title: {
      text: ''
      //  x: -50
    },
    plotOptions: {
      series: {
        dataLabels: {
          // enabled: true,
          // color: '#757575',
          // padding: 0,
          // connectorPadding: 0,

          enabled: true,
          color: '#757575',
          crop: false,
          distance: 20,
          //distance: type == 'Expanded_View' && window.innerWidth > 2000 ? 20 : 10,
          align: 'right',
          y:
            window.innerWidth > 2000 ? -5 : window.innerWidth < 2000 ? -6 : -10,
          // y: type == 'Expanded_View' && window.innerWidth > 2000 ? -5 :
          //   type == 'Expanded_View' && window.innerWidth < 2000 ? -6 : -10,
          padding: 0,
          connectorPadding: 0,

          style: {
            // fontSize: '14px',
            fontSize:
              type == 'Expanded_View' && window.innerWidth > 2000
                ? '20px'
                : '14px',
            fontWeight: '600',
            fontFamily: 'Roboto'
          },
          // inside: true,
          formatter: function(value) {
            return (
              this.key +
              '<br>' +
              '( ' +
              this.y.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
              ' )'
            );
          },
          // format: '<b>{point.name}</b> ({point.y:,.0f})',
          softConnector: true
        },
        center: ['40%', '50%'],
        width: '80%',
        enableMouseTracking: false
      }
    },
    legend: {
      enabled: false
    },
    tooltip: {
      enabled: false
    },
    series: [
      {
        name: 'Unique users',
        data: data.length > 0 ? data : []
      }
    ],
    credits: {
      enabled: false
    }
  };

  return options;
}
export function getKpiChartTitle(chartId, gridTitle) {
  let title;
  switch (chartId) {
    case '1335':
      title = 'CP / Wty / Int / All Vehicles';
      break;
    case '1336':
      title = 'All ROs Per Day Avg = % of Total Share';
      break;
    case '1337':
      title = 'CP 1 Line ROs Under 60k Miles';
      break;
    case '1338':
      title = 'CP 1 Line ROs Over 60k Miles';
      break;
    case '1339':
      title = 'All Sold Hrs / Avg Per Day / CP Avg Hrs Per RO';
      break;
    case '1340':
      title = 'Avg Age / Miles for CP & Wty Vehicles';
      break;
    case '1341':
      title = 'CP Lbr Sls Per RO / GP $ / GP %';
      break;
    case '1342':
      title = 'CP Pts Sls Per RO / GP $ / GP %';
      break;
    case '1343':
      title = 'CP Labor & Parts Sales $ Per RO / Total GP $';
      break;
    case '1344':
      title = 'CP Work Mix';
      break;
    case '1346':
      title =
        gridTitle + ' Repair Labor Price Targets / Misses / % Non-Compliance';
      break;
    case '1353':
      title =
        gridTitle + ' Repair Parts Price Targets  / Misses / % Non-Compliance';
      break;
    case '1351':
      // title = 'All 1 Line ROs';
      title = '1 Line ROs for All Miles';
      break;
  }
  return title;
}

export function getOptionsForHighchartsPie(data, chartId, type) {
  var config = {
    //colors: type == 'Expanded_View' ? getColorScheme(22) : getColorScheme(20),
    colors: getColorScheme(22),
    chart: {
      plotBackgroundColor: null,
      plotBorderWidth: null,
      plotShadow: false,
      type: 'pie'
      //backgroundColor: type == 'Expanded_View' ? '#f2dada2b' : 'white'
    },
    title: {
      text: ''
    },
    tooltip: {
      enabled: false
    },
    accessibility: {
      point: {
        valueSuffix: '%'
      }
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        // cursor: 'pointer',
        dataLabels: {
          enabled: true,
          color: '#757575',
          crop: false,
          distance: 5,
          align: 'right',
          y: -12,
          style: {
            // fontSize: '15px',
            fontSize:
              type == 'Expanded_View' && window.innerWidth > 2000
                ? '20px'
                : '15px',
            fontWeight: '600',
            fontFamily: 'Roboto'
          },
          // format: '<b>{point.name}</b>: {point.percentage:.1f} %'
          formatter: function(value) {
            return this.key + '<br>' + '( ' + this.y + '% )';
          }
        }
      },
      series: {
        enableMouseTracking: false
      }
    },
    series: [
      {
        name: 'Brands',
        colorByPoint: true,
        data: data
      }
    ]
  };
  return config;
}
export function getOptionsForColumnHighcharts(data, type, chartId, parent) {
  let config = {
    colors: ['#ee7600', '#109618'],
    chart: {
      //zoomType: 'xy'
      style: {
        fontFamily: 'Roboto',
        color: '#757575',
        top: 11
      }
    },
    title: {
      text: ''
    },
    plotOptions: {
      column: {
        dataLabels: {
          enabled: true,
          crop: false,
          overflow: 'none',
          color: '#757575',
          style: {
            fontSize: chartId == 1344 ? '16px' : '12px',
            fontWeight: chartId == 1344 ? '600' : '900',
            fontFamily: 'Roboto'
          },
          className: parent == 'Expanded_View' ? 'dataLabels1341' : '',
          formatter: function(value) {
            return this.y > 0
              ? '$' + this.y.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              : '';
          }
        }
      },
      series: {
        enableMouseTracking: false,
        pointWidth: 30
      }
    },
    xAxis: [
      {
        className: parent == 'Expanded_View' ? 'expandedXaxis' : '',
        categories:
          type == 'total' ? ['Sales', 'GP'] : ['Sales / RO', 'GP / RO'],
        labels: {
          rotation: 0,
          style: {
            fontSize: '12px',
            fontWeight: 'bold'
          }
        }
      }
    ],
    yAxis: [
      {
        // Primary yAxis
        className: parent == 'Expanded_View' ? 'expandedYaxis' : '',
        maxPadding: parent == 'Expanded_View' ? 0.1 : 0.1,
        labels: {
          formatter: function() {
            return (
              '$' + this.value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            );
          },
          style: {
            color: getColorScheme(20)
          }
        },
        title: {
          text: ''
        }
      }
    ],
    tooltip: {
      //shared: true
      enabled: false
    },
    legend: {
      layout: 'vertical',
      align: 'left',
      x: 120,
      verticalAlign: 'bottom',
      y: 100,
      floating: true,
      backgroundColor: 'rgba(255,255,255,0.25)',
      style: {
        fontSize: '16px',
        fontWeight: 'bold'
      }
    },
    series: [
      {
        name: type == 'total' ? 'Sales' : 'Sales / RO',
        type: 'column',
        data:
          type == 'total' && chartId != 1343
            ? [Number(data.data[3]), 0]
            : type == 'total' && chartId == 1343
            ? [Number(data.data[2]), 0]
            : [Number(data.data[0]), 0]
      },
      {
        name: type == 'total' ? 'GP' : 'GP / RO',
        type: 'column',
        data:
          type == 'total' && chartId != 1343
            ? [0, Number(data.data[4])]
            : type == 'total' && chartId == 1343
            ? [0, Number(data.data[3])]
            : [0, Number(data.data[1])]
      }
    ]
  };
  return config;
}

export function getOptionsForHighchartsLinearGauge(data, type) {
  let options = {
    chart: {
      inverted: true,
      //marginLeft: 135,
      type: 'bullet',
      height: 60,
      backgroundColor: type == 'extended_kpi' ? '#f2dada2b' : 'white'
    },
    title: {
      text: null
    },
    legend: {
      enabled: false
    },
    yAxis: {
      gridLineWidth: 0
    },
    tooltip: {
      enabled: false
    },
    plotOptions: {
      series: {
        pointPadding: 0.25,
        borderWidth: 0,
        color: '#FFF',
        pointWidth: 4,
        // targetOptions: {
        //     width: '200%'
        // }
        dataLabels: {
          enabled: true,
          borderRadius: 5,
          backgroundColor: 'rgba(252, 255, 197, 0.7)',
          borderWidth: 1,
          borderColor: '#AAA',
          //y: 30,
          x: 5,
          format: '{point.y} Yrs',
          style: {
            fontSize: '16px',
            fontWeight: 'bold',
            fontFamily: 'Roboto'
          }
        }
      }
    },
    credits: {
      enabled: false
    },
    exporting: {
      enabled: false
    },
    xAxis: {
      categories: ['']
      //categories: ['<span class="hc-cat-title">Age</span>']
    },
    yAxis: {
      style: {
        fontSize: '14px',
        fontWeight: 'bold'
      },
      plotBands: [
        {
          from: 0,
          to: 2.5,
          color: '#008000'
        },
        {
          from: 2.5,
          to: 5,
          color: '#7ab55c'
        },
        {
          from: 5,
          to: 7.5,
          color: '#ec5555'
        },
        {
          from: 7.5,
          to: 10,
          color: '#b81414'
        }
      ],
      title: null,
      max: 10
    },
    series: [
      {
        data: [
          {
            y: Math.round(Number(data) * 2) / 2,
            //   dataLabels: {
            //     enabled: true,
            //     outside: true,
            //     format: '{point.y}',
            //     color: '#000'
            // },
            target: Math.round(Number(data) * 2) / 2
          }
        ]
      }
    ]
  };
  return options;
}
export function getOptionsForPie1346(data, chartId, advisor, parent) {
  let options = {
    layout: {
      padding: {
        left: 80,
        //right: 100,
        top: 0,
        bottom: 0
      }
    },
    maintainAspectRatio: false,
    title: {
      display: true,
      position: 'top',
      padding: 0,
      lineHeight: 1
    },
    legend: {
      position: 'right',
      align: 'center',
      labels: {
        boxWidth: 12,
        usePointStyle: false,
        padding: 10,
        fontSize: 14,
        //fontSize: parent == 'Expanded_View' ? 20: 14,
        fontFamily: 'Roboto',
        fontStyle: '500'
      }
    },
    tooltips: {
      enabled: false
    },
    hover: { mode: null },
    plugins: {
      datalabels: {
        display: true,
        // color: '#FFF',
        // borderRadius: 3,
        // align: 'center',
        // font: {
        //   color: 'white',
        //   weight: 'bold',

        // },
        color: '#FFF',
        borderColor: 'white',
        borderWidth: 2,
        borderRadius: 3,
        align: 'end',
        anchor: 'end',
        offset: -28,
        //  align: 'center',
        font: {
          color: 'white',
          weight: 'bold',
          size: parent == 'Expanded_View' && window.innerWidth > 2000 ? 20 : 14
        },
        backgroundColor: function(context) {
          return context.dataset.backgroundColor;
        },
        padding: 6,
        borderRadius: 25,
        formatter: function(value, context) {
          return value
            ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            : 0;
        }
        // formatter: value => {
        //   return value
        //     ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        //     : 0;
        // }
      }
    }
  };
  return options;
}

export function getChartConfigurationItemization(data, allData, chartType) {
  let data1 = [];
  data1.push(data);

  let options = {
    boost: {
      useGPUTranslations: true,
      usePreAllocated: true
    },
    title: {
      text: ''
    },
    tooltip: {
      useHTML: true,
      borderColor: '#003d6b',
      color: '#003d6b',
      borderWidth: 1,
      backgroundColor: '#ddeaf4',
      style: {
        color: '#003d6b'
      },
      formatter: function() {
        var symbol;
        switch (this.series.symbol) {
          case 'circle':
            symbol = '&#9679';
            break;
          case 'diamond':
            symbol = '&#9670';
            break;
          case 'square':
            symbol = '&#9632';
            break;
        }

        var label = this.colorIndex;
        let initialLabel =
          allData.datasets.length > 0
            ? label == 0
              ? allData.datasets[2].label
              : label == 1
              ? allData.datasets[1].label
              : allData.datasets[0].label
            : '';
        let toolTipLabel =
          (initialLabel.split(' ')[1] == 'Maintenance'
            ? 'Maint'
            : initialLabel.split(' ')[1] == 'Competitive'
            ? 'Compet'
            : initialLabel.split(' ')[1]) +
          ', ' +
          initialLabel.split(' ')[0];
        /* label == 0 => Repair , label == 1 => Maint,label == 2 => Comp*/
        let symbolColor =
          label == 0 ? '#35af557a' : label == 1 ? '#f17f7fa8' : '#7cb5ec';

        if (allData.datasets.length > 0) {
          if (label == 0) {
            var tooltiplabel = '';
            let tooltipItem = [];
            allData.datasets[2].data.map(item => {
              if (item['x'] == this.x && item['y'] == this.y) {
                this.count = item['count'];
                this.label = item['label'];
              }
            });
            tooltiplabel = this.count > 1 ? ' Jobs' : ' Job';

            return chartType == 18
              ? '<span id="tooltipItem">' +
                  this.count +
                  '' +
                  tooltiplabel +
                  ' @ ' +
                  this.label +
                  ' Hrs </span><br/>' +
                  '<span style="color:' +
                  symbolColor +
                  ';font-size: 1.0rem">' +
                  symbol +
                  '</span>' +
                  ' ' +
                  toolTipLabel +
                  ': $' +
                  this.y
              : '<span id="tooltipItem">' +
                  this.count +
                  '' +
                  tooltiplabel +
                  ' @ ' +
                  '$' +
                  this.label +
                  ' </span><br/>' +
                  '<span style="color:' +
                  symbolColor +
                  ';font-size: 1.0rem">' +
                  symbol +
                  '</span>' +
                  ' ' +
                  toolTipLabel +
                  ': ' +
                  this.y.toFixed(4);
          } else if (label == 1) {
            let tooltiplabel = '';

            allData.datasets[1].data.map(item => {
              if (item['x'] == this.x && item['y'] == this.y) {
                this.count = item['count'];
                this.label = item['label'];
              }
            });

            tooltiplabel = this.count > 1 ? ' Jobs' : ' Job';

            return chartType == 18
              ? '<span id="tooltipItem">' +
                  this.count +
                  '' +
                  tooltiplabel +
                  ' @ ' +
                  this.label +
                  ' Hrs </span><br/>' +
                  '<span style="color:' +
                  symbolColor +
                  ';font-size: 1.2rem">' +
                  symbol +
                  '</span>' +
                  ' ' +
                  toolTipLabel +
                  ': $' +
                  this.y
              : '<span id="tooltipItem">' +
                  this.count +
                  '' +
                  tooltiplabel +
                  ' @ ' +
                  '$' +
                  this.label +
                  ' </span><br/>' +
                  '<span style="color:' +
                  symbolColor +
                  ';font-size: 1.0rem">' +
                  symbol +
                  '</span>' +
                  ' ' +
                  toolTipLabel +
                  ': ' +
                  this.y.toFixed(4);
          } else if (label == 2) {
            let tooltiplabel = '';
            allData.datasets[0].data.map(item => {
              if (item['x'] == this.x && item['y'] == this.y) {
                this.count = item['count'];
                this.label = item['label'];
              }
            });
            tooltiplabel = this.count > 1 ? ' Jobs' : 'Job';

            return chartType == 18
              ? '<span id="tooltipItem">' +
                  this.count +
                  '' +
                  tooltiplabel +
                  ' @ ' +
                  this.label +
                  ' Hrs </span><br/>' +
                  '<span style="color:' +
                  symbolColor +
                  ';font-size: 1.3rem">' +
                  symbol +
                  '</span>' +
                  ' ' +
                  toolTipLabel +
                  ': $' +
                  this.y
              : '<span id="tooltipItem">' +
                  this.count +
                  '' +
                  tooltiplabel +
                  ' @ ' +
                  '$' +
                  this.label +
                  ' </span><br/>' +
                  '<span style="color:' +
                  symbolColor +
                  ';font-size: 1.0rem">' +
                  symbol +
                  '</span>' +
                  ' ' +
                  toolTipLabel +
                  ': ' +
                  this.y.toFixed(4);
          }
        }
      }
    },
    legend: {
      enabled: true,
      itemStyle: {
        color: '#757575',
        fontFamily: 'Roboto',
        //fontSize: 14,
        fontWeight: 500
      },
      itemHoverStyle: {
        color: '#757575'
      }
    },
    series: [
      {
        name: chartType == 19 ? 'Markup Repair' : 'ELR Repair',
        marker: {
          symbol: 'diamond',
          fillColor: '#35af557a',
          lineWidth: 0.5,
          lineColor: 'rgba(4, 147, 114, 1)'
        },
        data: data[2],
        legendIndex: 2,
        allowPointSelect: true
      },
      {
        name: chartType == 19 ? 'Markup Maintenance' : 'ELR Maintenance',
        marker: {
          symbol: 'square',
          fillColor: '#f17f7fa8',
          lineWidth: 0.5,
          lineColor: 'rgba(220,57,18,1)',
          radius: 4
        },
        data: data[1],
        legendIndex: 1,
        allowPointSelect: true
      },
      {
        name: chartType == 19 ? 'Markup Competitive' : 'ELR Competitive',
        marker: {
          fillColor: '#0389fc61',
          lineWidth: 0.5,
          lineColor: 'rgba(75,192,192,1)'
        },
        data: data[0],
        legendIndex: 0,
        allowPointSelect: true
      }
    ]
  };
  return options;
}
export function getTimeZone() {
  var offset = new Date().getTimezoneOffset(),
    o = Math.abs(offset);
  return (
    (offset < 0 ? '+' : '-') +
    ('00' + Math.floor(o / 60)).slice(-2) +
    ':' +
    ('00' + (o % 60)).slice(-2)
  );
}

export function getNextMonth(date) {
  var nxtMonth = ((new Date(date).getMonth() + 1) % 12) + 1;
  var Year = new Date(date).getFullYear();
  var dateVal = Year + '-' + nxtMonth + '-' + '01';
  return moment(dateVal).format('MMM');
}

export function getYearValue(date) {
  return new Date(date)
    .getFullYear()
    .toString()
    .substr(-2);
}
export function formatCellValueKPI(val, opt, dec) {
  if (opt == '$') {
    return val == 0 || val == null
      ? '$0'
      : Math.sign(val) > -1
      ? '$' +
        (dec == 1
          ? val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
          : parseInt(val)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ','))
      : '-$' +
        (dec == 1
          ? Math.abs(val)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
          : Math.abs(parseInt(val))
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ','));
  } else if (opt == '%') {
    return val == 0 || val == null || val == ''
      ? '0%'
      : // : Math.floor(val).toLocaleString();
      dec == 1
      ? val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      : val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%';
  } else {
    return val != null
      ? dec == 1
        ? val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      : 0;
  }
}
export function formatCellValueKPInew(val) {
  return Number(val)
    .toFixed(1)
    .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}
export function getChartParentId(chartId) {
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(item => item.chartId == chartId);
  const parentId = filteredResult.length > 0 && filteredResult[0].parentId;
  return parentId;
}

export default getYearLegend;
