import '@ag-grid-enterprise/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-enterprise/all-modules/dist/styles/ag-theme-balham.css';
import { LicenseManager } from '@ag-grid-enterprise/core';
import React from 'react';
import 'react-app-polyfill/ie11';
import 'react-app-polyfill/stable';
import ReactDOM from 'react-dom';
import App from './App';
import * as serviceWorker from './serviceWorker';
import { CookiesProvider } from "react-cookie";
LicenseManager.setLicenseKey(process.env.REACT_APP_AG_GRID_LICIENCE_KEY);

ReactDOM.render(<CookiesProvider> <App /> </CookiesProvider>, document.getElementById('root'));

serviceWorker.unregister();
