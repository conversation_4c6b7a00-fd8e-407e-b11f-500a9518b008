.header_theme {
  background-color: #fff;
  color: #003d6b;
  border-bottom: solid 0.1px #003d6b;
}
.ag-root-wrapper {
  border: solid 1px;
  border-color: var(--ag-border-color, #003d6b) !important;
}
.ag-theme-balham .ag-header-icon {
  color: #003d6b;
}
.ag-tool-panel-wrapper {
  background-color: #fff;
}

.ag-theme-balham .ag-ltr {
  border: solid 1px;
  border-color: var(--ag-border-color, #003d6b) !important;
}
.ag-theme-balham .ag-rtl .ag-side-bar-left,
.ag-theme-balham .ag-ltr .ag-side-bar-right {
  border-left: solid 1px #003d6b;
}
.ag-theme-balham .ag-rtl .ag-side-bar-left .ag-tool-panel-wrapper,
.ag-theme-balham .ag-ltr .ag-side-bar-right .ag-tool-panel-wrapper {
  border-right: solid 1px #003d6b;
}
.ag-theme-balham .ag-column-panel-column-select {
  border-bottom: solid 1px #003d6b;
  /* border-top: solid 1px #003d6b; */
  background: #fff;
}
.ag-theme-balham .ag-selected .ag-side-button-button {
  background-color: #fff;
  /* //border-top-color: #003d6b; */
  border-bottom-color: #003d6b;
}
.ag-cell {
  /* border-bottom: solid 1px #003d6b; */
}
.ag-theme-balham .ag-header .ag-header-row {
  background-color: #fff;
  color: #003d6b;
  border-bottom: solid 1px #003d6b;
}
.ag-theme-balham .ag-column-select-header {
  border-bottom: solid 1px #003d6b;
}
.ag-side-button-button {
  color: #003d6b;
}
.ag-theme-balham .ag-header-cell::after {
  border-left: solid 1px #003d6b99;
}
.ag-theme-balham .ag-icon {
  margin-right: -12px;
}
.ag-theme-balham .ag-icon-none {
  padding-right: 8px;
}
.ag-theme-balham .ag-rtl .ag-side-bar-left .ag-selected .ag-side-button-button,
.ag-theme-balham
  .ag-ltr
  .ag-side-bar-right
  .ag-selected
  .ag-side-button-button {
  color: #003d6b;
}
.ag-theme-balham .ag-icon-filter {
  color: #003d6b;
  margin-right: 1px;
}
.ag-side-button-label {
  color: #003d6b;
}
.ag-theme-balham .ag-side-button-icon-wrapper {
  margin-right: 12px;
}
.ag-icon-tree-open .ag-icon-tree-closed {
  margin-right: 12px;
}
.ag-theme-balham .ag-header {
  border-bottom: none;
}
.ag-icon.ag-icon-filter {
  cursor: pointer;
}
 .ag-theme-balham .ag-header-cell:last-child::after {
  border-left: solid 1px #fff;
}
#data-tab-goal .ag-layout-normal > .ag-body-viewport.ag-layout-normal {
  overflow-y: overlay;
}
.green-bg {background-color: rgb(225 230 234);}

.rag-red-outer {
  border: 1px solid red !important; /* Only border, no background color */
  background-color: transparent; /* Ensure no background color */
}