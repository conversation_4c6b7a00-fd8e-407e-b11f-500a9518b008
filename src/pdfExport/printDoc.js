import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";
import getDocDefinition from "./docDefinition";

pdfMake.vfs = pdfFonts.pdfMake.vfs;


function printDoc(printParams, gridApi, columnApi, realm, fileName,topFiveOpcodeArr, opcodeArr) {
  console.log("Exporting to PDF...");
  const docDefinition = getDocDefinition(printParams, gridApi, columnApi, realm, fileName, topFiveOpcodeArr, opcodeArr);
  pdfMake.createPdf(docDefinition).download(fileName+'.pdf');
}

export default printDoc;
