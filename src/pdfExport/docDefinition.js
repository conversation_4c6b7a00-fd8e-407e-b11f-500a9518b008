import React, { useState, useEffect } from "react";

export default function getDocDefinition(
  printParams,
  agGridApi,
  agGridColumnApi,
  realm,
  fileName,
  topFiveOpcodeArr,
  opcodeArr
) {
  const {
    PDF_HEADER_COLOR,
    PDF_INNER_BORDER_COLOR,
    PDF_OUTER_BORDER_COLOR,
    PDF_ODD_BKG_COLOR,
    PDF_EVEN_BKG_COLOR,
    PDF_HEADER_HEIGHT,
    PDF_ROW_HEIGHT,
    PDF_PAGE_ORITENTATION,
    PDF_WITH_CELL_FORMATTING,
    PDF_WITH_COLUMNS_AS_LINKS,
    PDF_SELECTED_ROWS_ONLY,
    PDF_WITH_FOOTER_PAGE_COUNT,
    PDF_LOGO,
    PDF_HEADER,
    PDF_WITH_FOOTER
  } = printParams;
  let storeId =  JSON.parse(localStorage.getItem('selectedStoreId'))[0];
  return (function() {
    const columnGroupsToExport = getColumnGroupsToExport();

    const columnsToExport = getColumnsToExport();
    const widths = getExportedColumnsWidths(columnsToExport);

    const rowsToExport = getRowsToExport(columnsToExport);
    
    const body = columnGroupsToExport
      ? [columnGroupsToExport, columnsToExport, ...rowsToExport]
      : [columnsToExport, ...rowsToExport];

    const headerRows = columnGroupsToExport ? 2 : 1;
    const header = PDF_HEADER
      ? {
          columns: [
            PDF_HEADER.split('\n').length > 1 ?
            [
              { text: PDF_HEADER.split('\n')[0], alignment: 'center', fontSize: 18, bold: true,color: '#cc0052' },
              { text: PDF_HEADER.split('\n')[1], alignment: 'center', fontSize: 16, bold: true, },
              { text: PDF_HEADER.split('\n')[2], alignment: "center", fontSize: 12 },
              { text: PDF_HEADER.split('\n')[3], alignment: "center", fontSize: 12 },
              { text: PDF_HEADER.split('\n')[4], alignment: "center", fontSize: 12 },
              { text: PDF_HEADER.split('\n')[5],alignment : "center", fontSize: 14 },
              { text: PDF_HEADER.split('\n')[6], alignment: "left", fontSize: 14 ,margin:[10,0,-10,-10]},
              { text: PDF_HEADER.split('\n')[7], alignment: "right", fontSize: 14, margin: [5,0,10,0] },
              { text: PDF_HEADER.split('\n')[8], alignment: "left", fontSize: 14,margin: [10,0,-10,-10] },
              { text: PDF_HEADER.split('\n')[9], alignment: "right", fontSize: 14 , margin: [5,0,10,0]},
              { text: PDF_HEADER.split('\n')[10], alignment: "left", fontSize: 14,margin: [10,0,0,0] },
              { text: PDF_HEADER.split('\n')[11], alignment: "left", fontSize: 14, margin: [10,0,0,0]},
              { text: PDF_HEADER.split('\n')[12], alignment: "left", fontSize: 14, margin: [10,0,0,0]},
              { text: PDF_HEADER.split('\n')[13], alignment: "left", fontSize: 14, margin: [10,0,0,0]},
              { text: PDF_HEADER.split('\n')[14], alignment: "left", fontSize: 14, margin: [10,0,0,0]},
              {text: '\n'}
            ] : [
              { text: PDF_HEADER.split('\n')[0]+'\n', alignment: 'left', fontSize: 14 , bold: true,color: '#cc0052' }
            ],
          ],
        }

      : null;

    const footer = PDF_WITH_FOOTER
      ? {
          columns: 
            (PDF_WITH_FOOTER.split('\n')[0] == 'Best 100 ROs ') ?
              [
                { text: 'Total', alignment: 'left', fontSize: 15, bold: true,margin:[10,0,-10,-10] ,color: '#cc0052'},
                { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[260,0,70,0],color: '#cc0052' },
                { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[-60,0,-20,0],color: '#cc0052' },
                { text: PDF_WITH_FOOTER.split('\n')[3], fontSize: 12, bold: true,margin:[-395,0,90,0],color: '#cc0052' }
              ]
            : (PDF_WITH_FOOTER.split('\n')[0] == 'Included Op Codes '  && localStorage.getItem('storeNames') != 'Lupient Chevrolet ' 
            && storeId != '232297966' && storeId != '256778041' && storeId != '236951998' && localStorage.getItem('storeNames') != 'Fisher Honda ' ) ?
              [
                { text: 'Total Includes', alignment: 'left', fontSize: 15, bold: true,margin:[0,0,-10,-10],color: '#cc0052' },
                { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[165,0,-10,0],color: '#cc0052' },
                { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[18,0,-20,0],color: '#cc0052' },
                { text: PDF_WITH_FOOTER.split('\n')[3], fontSize: 12, bold: true,margin:[-150,0,90,0] ,color: '#cc0052'},
                { text: PDF_WITH_FOOTER.split('\n')[4], fontSize: 12, bold: true, margin:[-210,0,-30,0],color: '#cc0052' },
                { text: PDF_WITH_FOOTER.split('\n')[5], fontSize: 12, bold: true,margin:[-365,0,-30,0],color: '#cc0052' },
              ]
            :( PDF_WITH_FOOTER.split('\n')[0] == 'Included Op Codes '  && (storeId == '232297966' || localStorage.getItem('storeNames') != 'Fisher Honda ')) ?
            [
              { text: 'Total Includes', alignment: 'left', fontSize: 15, bold: true,margin:[10,0,-10,-10],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[150,0,-10,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[15,0,-20,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[3], fontSize: 12, bold: true,margin:[-125,0,90,0] ,color: '#cc0052'},
              { text: PDF_WITH_FOOTER.split('\n')[4], fontSize: 12, bold: true, margin:[-150,0,-30,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[5], fontSize: 12, bold: true,margin:[-290,0,-30,0],color: '#cc0052' },
             ]
             :( PDF_WITH_FOOTER.split('\n')[0] == 'Included Op Codes '  && storeId == '256778041') ?
            [
              { text: 'Total Includes', alignment: 'left', fontSize: 15, bold: true,margin:[10,0,-10,-10],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[140,0,-10,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[15,0,-20,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[3], fontSize: 12, bold: true,margin:[-98,0,90,0] ,color: '#cc0052'},
              { text: PDF_WITH_FOOTER.split('\n')[4], fontSize: 12, bold: true, margin:[-98,0,-30,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[5], fontSize: 12, bold: true,margin:[-210,0,-30,0],color: '#cc0052' },
             ]
             :( PDF_WITH_FOOTER.split('\n')[0] == 'Included Op Codes '  && storeId == '236951998') ?
            [
              { text: 'Total Includes', alignment: 'left', fontSize: 15, bold: true,margin:[10,0,-10,-10],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[155,0,-10,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[20,0,-20,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[3], fontSize: 12, bold: true,margin:[-110,0,90,0] ,color: '#cc0052'},
              { text: PDF_WITH_FOOTER.split('\n')[4], fontSize: 12, bold: true, margin:[-135,0,-30,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[5], fontSize: 12, bold: true,margin:[-280,0,-30,0],color: '#cc0052' },
             ]
            :( PDF_WITH_FOOTER.split('\n')[0] == 'Included Op Codes '  && localStorage.getItem('storeNames') == 'Fisher Honda ') ?
            [
              { text: 'Total Includes', alignment: 'left', fontSize: 15, bold: true,margin:[10,0,-10,-10],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[125,0,-10,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[15,0,-20,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[3], fontSize: 12, bold: true,margin:[-90,0,90,0] ,color: '#cc0052'},
              { text: PDF_WITH_FOOTER.split('\n')[4], fontSize: 12, bold: true, margin:[-75,0,-30,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[5], fontSize: 12, bold: true,margin:[-170,0,-30,0],color: '#cc0052' },
             ]
            : PDF_WITH_FOOTER.split('\n')[0] == 'Included Op Codes ' ?
            [
                { text: 'Total Includes', alignment: 'left', fontSize: 15, bold: true,margin:[10,0,-10,-10],color: '#cc0052' },
                { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[160,0,-10,0],color: '#cc0052' },
                { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[15,0,-20,0],color: '#cc0052' },
                { text: PDF_WITH_FOOTER.split('\n')[3], fontSize: 12, bold: true,margin:[-140,0,90,0] ,color: '#cc0052'},
                { text: PDF_WITH_FOOTER.split('\n')[4], fontSize: 12, bold: true, margin:[-190,0,-30,0],color: '#cc0052' },
                { text: PDF_WITH_FOOTER.split('\n')[5], fontSize: 12, bold: true,margin:[-335,0,-30,0],color: '#cc0052' },
              ]
            : PDF_WITH_FOOTER.split('\n')[0] == 'Excluded Op Codes ' && realm != 'keatingag' 
            && storeId != '232297966'  && storeId != '256778041' && storeId != '236951998' && localStorage.getItem('storeNames') != 'Fisher Honda ' ? [
              { text: 'Total Excludes', alignment: 'left', fontSize: 15, bold: true,margin:[10,0,-10,-10],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[260,0,50,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[-40,0,-20,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[3], fontWeight: 12, bold: true,margin:[-360,0,-20,0],color: '#cc0052' }
            ] 
            : PDF_WITH_FOOTER.split('\n')[0] == 'Excluded Op Codes ' && storeId == '232297966'  ? [
              { text: 'Total Excludes', alignment: 'left', fontSize: 15, bold: true,margin:[10,0,-10,-10],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[250,0,50,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[-40,0,-20,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[3], fontWeight: 12, bold: true,margin:[-310,0,-20,0],color: '#cc0052' }
            ]
            : PDF_WITH_FOOTER.split('\n')[0] == 'Excluded Op Codes ' && storeId == '256778041'  ? [
              { text: 'Total Excludes', alignment: 'left', fontSize: 15, bold: true,margin:[10,0,-10,-10],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[210,0,50,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[-40,0,-20,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[3], fontWeight: 12, bold: true,margin:[-270,0,-20,0],color: '#cc0052' }
            ]
            : PDF_WITH_FOOTER.split('\n')[0] == 'Excluded Op Codes ' &&  storeId == '236951998'  ? [
              { text: 'Total Excludes', alignment: 'left', fontSize: 15, bold: true,margin:[10,0,-10,-10],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[255,0,50,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[-45,0,-20,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[3], fontWeight: 12, bold: true,margin:[-320,0,-20,0],color: '#cc0052' }
            ]
            : PDF_WITH_FOOTER.split('\n')[0] == 'Excluded Op Codes ' &&  localStorage.getItem('storeNames') == 'Fisher Honda ' ?[
              { text: 'Total Excludes', alignment: 'left', fontSize: 15, bold: true,margin:[10,0,-10,-10],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[110,0,50,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[-40,0,-20,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[3], fontWeight: 12, bold: true,margin:[-170,0,-20,0],color: '#cc0052' }
            ]
            : (PDF_WITH_FOOTER.split('\n')[0] == 'Warranty Opportunity ' && localStorage.getItem('storeNames') == 'Lupient Chevrolet '  )? [
              { text: 'Total', alignment: 'left', fontSize: 15, bold: true,margin:[10,0,-10,-10],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[170,0,-10,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[18,0,-20,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[3], fontSize: 12, bold: true,margin:[-155,0,90,0] ,color: '#cc0052'},
              { text: PDF_WITH_FOOTER.split('\n')[4], fontSize: 12, bold: true, margin:[-210,0,-30,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[5], fontSize: 12, bold: true,margin:[-380,0,-30,0],color: '#cc0052' },
            ]
            : (PDF_WITH_FOOTER.split('\n')[0] == 'Warranty Opportunity ' && (localStorage.getItem('storeNames') != 'Lupient Chevrolet ' 
             && storeId != '256778041' && storeId != '236951998' && localStorage.getItem('storeNames') != 'Fisher Honda '))? [
              { text: 'Total' , alignment: 'left', fontSize: 15, bold: true,margin:[10,0,-10,-10],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[168,0,-10,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[18,0,-20,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[3], fontSize: 12, bold: true,margin:[-154,0,90,0] ,color: '#cc0052'},
              { text: PDF_WITH_FOOTER.split('\n')[4], fontSize: 12, bold: true, margin:[-210,0,-30,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[5], fontSize: 12, bold: true,margin:[-370,0,-30,0],color: '#cc0052' },
            ]
            : (PDF_WITH_FOOTER.split('\n')[0] == 'Warranty Opportunity ' && storeId == '256778041' || storeId == '236951998' )? [
              { text: 'Total' , alignment: 'left', fontSize: 15, bold: true,margin:[10,0,-10,-10],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[170,0,-10,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[20,0,-20,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[3], fontSize: 12, bold: true,margin:[-128,0,90,0] ,color: '#cc0052'},
              { text: PDF_WITH_FOOTER.split('\n')[4], fontSize: 12, bold: true, margin:[-165,0,-30,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[5], fontSize: 12, bold: true,margin:[-310,0,-30,0],color: '#cc0052' },
            ]
            : (PDF_WITH_FOOTER.split('\n')[0] == 'Warranty Opportunity ' && localStorage.getItem('storeNames') == 'Fisher Honda ')? [
              { text: 'Total' , alignment: 'left', fontSize: 15, bold: true,margin:[10,0,-10,-10],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[120,0,-10,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[20,0,-20,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[3], fontSize: 12, bold: true,margin:[-70,0,90,0] ,color: '#cc0052'},
              { text: PDF_WITH_FOOTER.split('\n')[4], fontSize: 12, bold: true, margin:[35,0,-30,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[5], fontSize: 12, bold: true,margin:[-230,0,-30,0],color: '#cc0052' },
            ]
            : [
              { text: 'Total Excludes', alignment: 'left', fontSize: 15, bold: true,margin:[10,0,-10,-10],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[1], fontSize: 12, bold: true, margin:[185,0,50,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[2], fontSize: 12, bold: true, margin:[-40,0,-20,0],color: '#cc0052' },
              { text: PDF_WITH_FOOTER.split('\n')[3], fontWeight: 12, bold: true,margin:[-250,0,-20,0],color: '#cc0052' }
            ]
      }
      : null;
      
    const pageMargins = [
      10,
      40,
      10,
      PDF_WITH_FOOTER_PAGE_COUNT ? 40 : 10
    ];
    const heights = rowIndex =>
      rowIndex < headerRows ? PDF_HEADER_HEIGHT : PDF_ROW_HEIGHT;

    const fillColor = (rowIndex, node, columnIndex) => {
      let index = [];
      if (rowIndex < node.table.headerRows) {
        return PDF_HEADER_COLOR;
      }
      if(fileName == 'Included Op Codes') {
        return (rowIndex > 0 && rowIndex <= 5) ? PDF_ODD_BKG_COLOR : PDF_EVEN_BKG_COLOR;
      } 
     else if(fileName == 'Best 100 ROs') {
        opcodeArr.some((r,i) => {
          if(topFiveOpcodeArr.includes(r)){
            index.push(i+1);
          }
        })
        return index.includes(rowIndex) ? PDF_ODD_BKG_COLOR : PDF_EVEN_BKG_COLOR;
      }
    };

    const hLineWidth = (i, node) =>
      i === 0 || i === node.table.body.length ? 1 : 1;

    const vLineWidth = (i, node) =>
      i === 0 || i === node.table.widths.length ? 1 : 0;

    const hLineColor = (i, node) =>
      i === 0 || i === node.table.body.length
        ? PDF_OUTER_BORDER_COLOR
        : PDF_INNER_BORDER_COLOR;

    const vLineColor = (i, node) =>
      i === 0 || i === node.table.widths.length
        ? PDF_OUTER_BORDER_COLOR
        : PDF_INNER_BORDER_COLOR;

    const docDefintiion = {
      pageOrientation: PDF_PAGE_ORITENTATION,
      // header,
      //footer,
     // footerContent,
      content: [
        header,
        {
          style: "myTable",
          headerRows,

          table: {
            // headerRows,
            widths,
            body,
            heights
          },
          layout: {
            fillColor,
            hLineWidth,
            vLineWidth,
            hLineColor,
            vLineColor
          }
        },
        footer,
      ],
      images: {
        "ag-grid-logo": PDF_LOGO
      },
      styles: {
        myTable: {
          margin: [0, 0, 0, 0],
          width: 'auto'
        },
        tableHeader: {
          bold: true,
          margin: [0, PDF_HEADER_HEIGHT / 3, 0, 0]
        },
        tableCell: {
          // margin: [0, 15, 0, 0]
        }
      },
      pageMargins
    };

    return docDefintiion;
  })();

  function getColumnGroupsToExport() {
    let displayedColumnGroups = agGridColumnApi.getAllDisplayedColumnGroups();

    let isColumnGrouping = displayedColumnGroups.some(col =>
      col.hasOwnProperty("children")
    );

    if (!isColumnGrouping) {
      return null;
    }

    let columnGroupsToExport = [];

    displayedColumnGroups.forEach(colGroup => {
      let isColSpanning = colGroup.children.length > 1;
      let numberOfEmptyHeaderCellsToAdd = 0;

      if (isColSpanning) {
        let headerCell = createHeaderCell(colGroup);
        columnGroupsToExport.push(headerCell);
        // subtract 1 as the column group counts as a header
        numberOfEmptyHeaderCellsToAdd--;
      }

      // add an empty header cell now for every column being spanned
      colGroup.displayedChildren.forEach(childCol => {
        let pdfExportOptions = getPdfExportOptions(childCol.getColId());
        if (!pdfExportOptions || !pdfExportOptions.skipColumn) {
          numberOfEmptyHeaderCellsToAdd++;
        }
      });

      for (let i = 0; i < numberOfEmptyHeaderCellsToAdd; i++) {
        columnGroupsToExport.push({});
      }
    });

    return columnGroupsToExport;
  }

  function getColumnsToExport() {
    let columnsToExport = [];

    agGridColumnApi.getAllDisplayedColumns().forEach(col => {
      let pdfExportOptions = getPdfExportOptions(col.getColId());
      if (pdfExportOptions && pdfExportOptions.skipColumn) {
        return;
      }
      let headerCell = createHeaderCell(col);
      columnsToExport.push(headerCell);
    });

    return columnsToExport;
  }

  function getRowsToExport(columnsToExport) {
    let rowsToExport = [];
    // array = [];
    // gridOptions.api.forEachNodeAfterFilterAndSort(function(node) {
    //     array.push(node.data);
    // });
    agGridApi.api.forEachNodeAfterFilterAndSort(function(node) {
      if (PDF_SELECTED_ROWS_ONLY && !node.isSelected()) {
        return;
      }
      let rowToExport = columnsToExport.map(({ colId }) => {
        let cellValue = agGridApi.api.getValue(colId, node);
        let tableCell = createTableCell(cellValue, colId);
        return tableCell;
      });
      rowsToExport.push(rowToExport);
    });
    return rowsToExport;
  }

  function getExportedColumnsWidths(columnsToExport) {
    return 'auto';
   // return columnsToExport.map(() => 100 / columnsToExport.length + "%");
  }

  function createHeaderCell(col) {
    let headerCell = {};

    let isColGroup = col.hasOwnProperty("children");

    if (isColGroup) {
      headerCell.text = col.originalColumnGroup.colGroupDef.headerName;
      headerCell.colSpan = col.children.length;
      headerCell.colId = col.groupId;
    } else {
      let headerName = col.colDef.headerName;

      if (col.sort) {
        headerName += ` (${col.sort})`;
      }
      if (col.filterActive) {
        headerName += ` [FILTERING]`;
      }

      headerCell.text = headerName;
      headerCell.colId = col.getColId();
    }

    headerCell["style"] = "tableHeader";

    return headerCell;
  }

  function createTableCell(cellValue, colId) {
    const tableCell = {
      text: cellValue === undefined
      ? ""
      : cellValue !== undefined && (colId !== 'elr' && colId !== 'lbrsale' &&
        colId !== 'laborsale' && colId !== 'varianceElr' &&
        colId !== 'reveImpact' && colId !== 'monthlyImpact' &&
        colId !== 'annualImpact' && colId !== 'analysisElr' && colId != 'frh' && colId != 'lbrsoldhours' ) 
      ? (cellValue)
      : (cellValue !== undefined && (colId == 'frh' || colId == 'lbrsoldhours' )) 
      ? Number(cellValue).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      : (cellValue !== undefined && colId == 'analysisElr') 
      ? '$'+ Math.round(Number(cellValue))
      : '$'+ Number(cellValue).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      style: "tableCell"
    };

    const pdfExportOptions = getPdfExportOptions(colId);

    if (pdfExportOptions) {
      const { styles, createURL } = pdfExportOptions;

      if (PDF_WITH_CELL_FORMATTING && styles) {
        Object.entries(styles).forEach(
          ([key, value]) => (tableCell[key] = value)
        );
      }

      if (PDF_WITH_COLUMNS_AS_LINKS && createURL) {
        tableCell["link"] = createURL(cellValue);
        tableCell["color"] = "blue";
        tableCell["decoration"] = "underline";
      }
    }

    return tableCell;
  }

  function getPdfExportOptions(colId) {
    let col = agGridColumnApi.getColumn(colId);
    return col.colDef.pdfExportOptions;
  }
}
