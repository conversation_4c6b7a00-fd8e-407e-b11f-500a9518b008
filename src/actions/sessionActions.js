export const SESSION_LOGIN = 'SESSION_LOGIN';
export const SESSION_LOGOUT = 'SESSION_LOGOUT';
export const SET_SERVICE_ADVISOR = 'SET_SERVICE_ADVISOR';
export const SET_PREV_PATH = 'SET_PREV_PATH';
export const SET_CURRENT_PATH = 'SET_CURRENT_PATH';
export const SET_ALL_FAVOURITES = 'SET_ALL_FAVOURITES';
export const SET_USERNAME = 'SET_USERNAME';
export const SET_STOREID = 'SET_STOREID';
export const SET_PAY_TYPE_TAB = 'SET_PAY_TYPE_TAB';
export const SET_PAY_TYPE_ERROR = 'SET_PAY_TYPE_ERROR';
export const SET_PAY_TYPE_ERROR_COUNT = 'SET_PAY_TYPE_ERROR_COUNT';
export const SET_DRILL_DOWN_COUNT = 'SET_DRILL_DOWN_COUNT';
export const SET_SEARCH = 'SET_SEARCH';
export const SET_TOKEN = 'SET_TOKEN';
export const SET_REFRESH_STATUS = 'SET_REFRESH_STATUS';
export const SET_RELOAD_STATUS = 'SET_RELOAD_STATUS';
export const SET_DASHBOARD_RELOAD_STATUS = 'SET_DASHBOARD_RELOAD_STATUS';
export const SET_REFRESH_ERROR_STATUS = 'SET_REFRESH_ERROR_STATUS';
export const SET_ALL_FAVOURITES_COMPARISON_MONTHS =
  'SET_ALL_FAVOURITES_COMPARISON_MONTHS';
export const SET_RONUMBER = 'SET_RONUMBER';
export const SET_NAV_ITEMS = 'SET_NAV_ITEMS';
export const SET_NAV_ITEM_STATUS = 'SET_NAV_ITEM_STATUS';
export const SET_ADVISOR_STATUS = 'SET_ADVISOR_STATUS';
export const SET_ADVISOR_WITHID = 'SET_ADVISOR_WITHID';
export const SET_MENU_SELECTED = 'SET_MENU_SELECTED';
export const SET_STORE_VALUE = 'SET_STORE_VALUE';
export const SET_KPI_REPORT_ADVISOR = 'SET_KPI_REPORT_ADVISOR';
export const SET_KPI_TOGGLE = 'SET_KPI_TOGGLE';
export const SET_INTERNAL_KPI_TOGGLE = 'SET_INTERNAL_KPI_TOGGLE';
export const SET_ADVISOR_NICK_NAME = 'SET_ADVISOR_NICK_NAME';
export const SET_EDITED_OPCODES = 'SET_EDITED_OPCODES';
export const SET_OPCODE_ERROR = 'SET_OPCODE_ERROR';
export const SET_OPCODE_ERROR_COUNT = 'SET_OPCODE_ERROR_COUNT';
export const SET_HISTORY_STATUS = 'SET_HISTORY_STATUS';
export const SET_ADVISOR_ERRORS = 'SET_ADVISOR_ERRORS';
export const SET_TECH_ERRORS = 'SET_TECH_ERRORS';
export const SET_ADVISOR_ERROR_COUNT = 'SET_ADVISOR_ERROR_COUNT';
export const SET_TECH_ERROR_COUNT = 'SET_TECH_ERROR_COUNT';
export const SET_ALL_ERROR_COUNT = 'SET_ALL_ERROR_COUNT';
export const SET_TECHNICIAN = 'SET_TECHNICIAN';
export const SET_TECHNICIAN_SUMMARY = 'SET_TECHNICIAN_SUMMARY';
export const SET_ALL_ADVISOR_NAMES = 'SET_ALL_ADVISOR_NAMES';
export const SET_ALL_ADVISOR_IDS = 'SET_ALL_ADVISOR_IDS';
export const SET_LABOR_GRID_TYPES = 'SET_LABOR_GRID_TYPES';
export const SET_PARTS_MATRIX_TYPES = 'SET_PARTS_MATRIX_TYPES';

export const SET_DAILY_IMPORT_ERROR = 'SET_DAILY_IMPORT_ERROR';
export const SET_DAILY_IMPORT_ERROR_COUNT = 'SET_DAILY_IMPORT_ERROR_COUNT';
export const SET_USER_DETAILS = 'SET_USER_DETAILS';
export const SET_NEW_ADVISOR_ERROR = 'SET_NEW_ADVISOR_ERROR';
export const SET_NEW_ADVISOR_ERROR_COUNT = 'SET_NEW_ADVISOR_ERROR_COUNT';
export const SET_ACTIVE_ADVISORS = 'SET_ACTIVE_ADVISORS';
export const SET_DATE_DATA = 'SET_DATE_DATA';
export const SET_KPI_HOME_TOGGLE = 'SET_KPI_HOME_TOGGLE';
export const SET_NEW_PAYTYPE_ERROR = 'SET_NEW_PAYTYPE_ERROR';
export const SET_NEW_PAYTYPE_ERROR_COUNT = 'SET_NEW_PAYTYPE_ERROR_COUNT';
export const SET_TOGGLE_STATUS = 'SET_TOGGLE_STATUS';
export const SET_TECHNICIAN_STATUS = 'SET_TECHNICIAN_STATUS';
export const SET_ACTIVE_TECHS = 'SET_ACTIVE_TECHS';
export const SET_SELECTED_TECH = 'SET_SELECTED_TECH';
export const SET_SORT_ORDER = 'SET_SORT_ORDER';
export const SET_HIDDEN_KPI_ROWS = 'SET_HIDDEN_KPI_ROWS';
export const SET_KPI_TOGGLE_START_DATE = 'SET_KPI_TOGGLE_START_DATE';
export const SET_KPI_TOGGLE_END_DATE = 'SET_KPI_TOGGLE_END_DATE';
export const SET_KPIGRAPHICS_TOGGLE_START_DATE =
  'SET_KPIGRAPHICS_TOGGLE_START_DATE';
export const SET_KPIGRAPHICS_TOGGLE_END_DATE =
  'SET_KPIGRAPHICS_TOGGLE_END_DATE';
export const SET_SORT_ORDER_STORE = 'SET_SORT_ORDER_STORE';
export const SET_HIDDEN_KPI_ROWS_STORE = 'SET_HIDDEN_KPI_ROWS_STORE';
export const SET_SEL_ADVISORS = 'SET_SEL_ADVISORS';
export const SET_SEL_STORES = 'SET_SEL_STORES';
export const SET_HID_ADVISORS = 'SET_HID_ADVISORS';
export const SET_VERSION_FLAG = 'SET_VERSION_FLAG';
export const SET_MPI_FLAG = 'SET_MPI_FLAG';
export const SET_MENU_FLAG = 'SET_MENU_FLAG';
export const SET_NEW_MODEL_ERROR_COUNT = 'SET_NEW_MODEL_ERROR_COUNT';
export const SET_NEW_MODEL = 'SET_NEW_MODEL';
export const SET_SEL_ADV = 'SET_SEL_ADV';
export const KPI_DATA = 'KPI_DATA';
export const SET_SEL_STORE = 'SET_SEL_STORE';
export const CURRENT_USER = 'CURRENT_USER';
export const RETAIL_FLAG = 'RETAIL_FLAG';
export const login = () => dispatch =>
  dispatch({
    type: SESSION_LOGIN
  });

export const logout = () => dispatch =>
  dispatch({
    type: SESSION_LOGOUT
  });

export const setServiceAdvisor = data => dispatch =>
  dispatch({
    type: SET_SERVICE_ADVISOR,
    payload: data
  });
export const setStorValue = data => dispatch =>
  dispatch({
    type: SET_STORE_VALUE,
    payload: data
  });
export const setRONumber = data => dispatch =>
  dispatch({
    type: SET_RONUMBER,
    payload: data
  });
export const setNavItemStatus = data => dispatch =>
  dispatch({
    type: SET_NAV_ITEM_STATUS,
    payload: data
  });
export const setNavItems = data => dispatch =>
  dispatch({
    type: SET_NAV_ITEMS,
    payload: data
  });
export const setPreviousPath = data => dispatch =>
  dispatch({
    type: SET_PREV_PATH,
    payload: data
  });
export const setCurrentPath = data => dispatch =>
  dispatch({
    type: SET_CURRENT_PATH,
    payload: data
  });
export const setFavouriteCharts = data => dispatch =>
  dispatch({
    type: SET_ALL_FAVOURITES,
    payload: data
  });
export const setFavouriteChartsComparisonMonths = data => dispatch =>
  dispatch({
    type: SET_ALL_FAVOURITES_COMPARISON_MONTHS,
    payload: data
  });
export const setUsername = data => dispatch =>
  dispatch({
    type: SET_USERNAME,
    payload: data
  });
export const setStoreId = data => dispatch =>
  dispatch({
    type: SET_STOREID,
    payload: data
  });
export const setPayTypeTab = data => dispatch =>
  dispatch({
    type: SET_PAY_TYPE_TAB,
    payload: data
  });
export const setPayTypeError = data => dispatch =>
  dispatch({
    type: SET_PAY_TYPE_ERROR,
    payload: data
  });
export const setDateData = data => dispatch =>
  dispatch({
    type: SET_DATE_DATA,
    payload: data
  });
export const setPayTypeErrorCount = data => dispatch =>
  dispatch({
    type: SET_PAY_TYPE_ERROR_COUNT,
    payload: data
  });
export const setOpcodeError = data => dispatch =>
  dispatch({
    type: SET_OPCODE_ERROR,
    payload: data
  });
export const setOpcodeErrorCount = data => dispatch =>
  dispatch({
    type: SET_OPCODE_ERROR_COUNT,
    payload: data
  });
export const setSearch = data => dispatch =>
  dispatch({
    type: SET_SEARCH,
    payload: data
  });
export const setDrillDownCount = data => dispatch =>
  dispatch({
    type: SET_DRILL_DOWN_COUNT,
    payload: data
  });
export const setToken = data => dispatch =>
  dispatch({
    type: SET_TOKEN,
    payload: data
  });
export const setRefreshStatus = data => dispatch =>
  dispatch({
    type: SET_REFRESH_STATUS,
    payload: data
  });
export const setReloadStatus = data => dispatch =>
  dispatch({
    type: SET_RELOAD_STATUS,
    payload: data
  });
export const setDashboardReloadStatus = data => dispatch =>
  dispatch({
    type: SET_DASHBOARD_RELOAD_STATUS,
    payload: data
  });
export const setRefreshErrorStatus = data => dispatch =>
  dispatch({
    type: SET_REFRESH_ERROR_STATUS,
    payload: data
  });
export const setAdvisorStatus = data => dispatch =>
  dispatch({
    type: SET_ADVISOR_STATUS,
    payload: data
  });
export const setAdvisorNickName = data => dispatch =>
  dispatch({
    type: SET_ADVISOR_NICK_NAME,
    payload: data
  });
export const setSelectedadvisorWithId = data => dispatch =>
  dispatch({
    type: SET_ADVISOR_WITHID,
    payload: data
  });
export const setMenuSelected = data => dispatch =>
  dispatch({
    type: SET_MENU_SELECTED,
    payload: data
  });
export const setKpiReportAdvisor = data => dispatch =>
  dispatch({
    type: SET_KPI_REPORT_ADVISOR,
    payload: data
  });
export const setKpiToggle = data => dispatch =>
  dispatch({
    type: SET_KPI_TOGGLE,
    payload: data
  });
export const setInternalKpiToggle = data => dispatch =>
  dispatch({
    type: SET_INTERNAL_KPI_TOGGLE,
    payload: data
  });
export const setHistoryStatus = data => dispatch =>
  dispatch({
    type: SET_HISTORY_STATUS,
    payload: data
  });
export const setServiceAdvisorErrors = data => dispatch =>
  dispatch({
    type: SET_ADVISOR_ERRORS,
    payload: data
  });
export const setTechnicianErrors = data => dispatch =>
  dispatch({
    type: SET_TECH_ERRORS,
    payload: data
  });
export const setServiceAdvisorErrorsCount = data => dispatch =>
  dispatch({
    type: SET_ADVISOR_ERROR_COUNT,
    payload: data
  });
export const setTechnicianErrorsCount = data => dispatch =>
  dispatch({
    type: SET_TECH_ERROR_COUNT,
    payload: data
  });
export const setAllErrorsCount = data => dispatch =>
  dispatch({
    type: SET_ALL_ERROR_COUNT,
    payload: data
  });
export const setTechnician = data => dispatch =>
  dispatch({
    type: SET_TECHNICIAN,
    payload: data
  });
export const setAllAdvisorNames = data => dispatch =>
  dispatch({
    type: SET_ALL_ADVISOR_NAMES,
    payload: data
  });
export const setAllAdvisorIds = data => dispatch =>
  dispatch({
    type: SET_ALL_ADVISOR_IDS,
    payload: data
  });
export const setLaborGridTypes = data => dispatch =>
  dispatch({
    type: SET_LABOR_GRID_TYPES,
    payload: data
  });
export const setPartsMatrixTypes = data => dispatch =>
  dispatch({
    type: SET_PARTS_MATRIX_TYPES,
    payload: data
  });

export const setDailyImportError = data => dispatch =>
  dispatch({
    type: SET_DAILY_IMPORT_ERROR,
    payload: data
  });
export const setDailyImportErrorCount = data => dispatch =>
  dispatch({
    type: SET_DAILY_IMPORT_ERROR_COUNT,
    payload: data
  });
export const setUserDetails = data => dispatch =>
  dispatch({
    type: SET_USER_DETAILS,
    payload: data
  });
export const setKpiHomeToggle = data => dispatch =>
  dispatch({
    type: SET_KPI_HOME_TOGGLE,
    payload: data
  });
export const setNewAdvisors = data => dispatch =>
  dispatch({
    type: SET_NEW_ADVISOR_ERROR,
    payload: data
  });

export const setNewAdvisorsErrorCount = data => dispatch =>
  dispatch({
    type: SET_NEW_ADVISOR_ERROR_COUNT,
    payload: data
  });
export const setNewPaytype = data => dispatch =>
  dispatch({
    type: SET_NEW_PAYTYPE_ERROR,
    payload: data
  });
export const setNewPaytypeErrorCount = data => dispatch =>
  dispatch({
    type: SET_NEW_PAYTYPE_ERROR_COUNT,
    payload: data
  });
export const setActiveAdvisors = data => dispatch =>
  dispatch({
    type: SET_ACTIVE_ADVISORS,
    payload: data
  });
export const setToggleStatus = data => dispatch =>
  dispatch({
    type: SET_TOGGLE_STATUS,
    payload: data
  });
export const setTechnicianStatus = data => dispatch =>
  dispatch({
    type: SET_TECHNICIAN_STATUS,
    payload: data
  });
export const setActiveTechs = data => dispatch =>
  dispatch({
    type: SET_ACTIVE_TECHS,
    payload: data
  });
export const setTechnicianSummary = data => dispatch =>
  dispatch({
    type: SET_TECHNICIAN_SUMMARY,
    payload: data
  });
export const setSelectedTech = data => dispatch =>
  dispatch({
    type: SET_SELECTED_TECH,
    payload: data
  });
export const setRowSortOrder = data => dispatch =>
  dispatch({
    type: SET_SORT_ORDER,
    payload: data
  });
export const setHiddenKpiRows = data => dispatch =>
  dispatch({
    type: SET_HIDDEN_KPI_ROWS,
    payload: data
  });
export const setRowSortOrderStore = data => dispatch =>
  dispatch({
    type: SET_SORT_ORDER_STORE,
    payload: data
  });
export const setHiddenKpiRowsStore = data => dispatch =>
  dispatch({
    type: SET_HIDDEN_KPI_ROWS_STORE,
    payload: data
  });

export const setKpiToggleStartDate = data => dispatch =>
  dispatch({
    type: SET_KPI_TOGGLE_START_DATE,
    payload: data
  });
export const setKpiToggleEndDate = data => dispatch =>
  dispatch({
    type: SET_KPI_TOGGLE_END_DATE,
    payload: data
  });
export const setKpiGraphicsToggleStartDate = data => dispatch =>
  dispatch({
    type: SET_KPIGRAPHICS_TOGGLE_START_DATE,
    payload: data
  });
export const setKpiGraphicsToggleEndDate = data => dispatch =>
  dispatch({
    type: SET_KPIGRAPHICS_TOGGLE_END_DATE,
    payload: data
  });

export const setSelectedAdvisors = data => dispatch =>
  dispatch({
    type: SET_SEL_ADVISORS,
    payload: data
  });
export const setHiddenAdvisors = data => dispatch =>
  dispatch({
    type: SET_HID_ADVISORS,
    payload: data
  });
export const setSelectedStores = data => dispatch =>
  dispatch({
    type: SET_SEL_STORES,
    payload: data
  });
export const setVersionFlag = data => dispatch =>
  dispatch({
    type: SET_VERSION_FLAG,
    payload: data
  });
export const setMpiFlag = data => dispatch =>
  dispatch({
    type: SET_MPI_FLAG,
    payload: data
  });
export const setMenuFlag = data => dispatch =>
  dispatch({
    type: SET_MENU_FLAG,
    payload: data
  });
export const setNewModelErrorCount = data => dispatch =>
  dispatch({
    type: SET_NEW_MODEL_ERROR_COUNT,
    payload: data
  });
export const setNewModel = data => dispatch =>
  dispatch({
    type: SET_NEW_MODEL,
    payload: data
  });
export const setSelAdv = data => dispatch =>
  dispatch({
    type: SET_SEL_ADV,
    payload: data
  });
export const setSelStoreIds = data => dispatch =>
  dispatch({
    type: SET_SEL_STORE,
    payload: data
  });
export const setKpiDataAll = data => dispatch =>
  dispatch({
    type: KPI_DATA,
    payload: data
  });
export const setCurrentUser = data => dispatch =>
  dispatch({
    type: CURRENT_USER,
    payload: data
  });
export const setRetailFlag = data => dispatch =>
  dispatch({
    type: RETAIL_FLAG,
    payload: data
  });
