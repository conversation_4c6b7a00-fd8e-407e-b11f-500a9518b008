[{"kpi_type": "Financial-CustomerPay", "kpi_type_code": "A", "kpi_name": "LaborSales", "kpi_no": "1", "kpi_slno": "1", "kpi_value": "111111"}, {"kpi_type": "Financial-CustomerPay", "kpi_type_code": "A", "kpi_name": "LaborGrossProfit", "kpi_no": "1", "kpi_slno": "2", "kpi_value": "1128233/75.3"}, {"kpi_type": "Financial-CustomerPay", "kpi_type_code": "A", "kpi_name": "LaborSalesPerRO", "kpi_no": "1", "kpi_slno": "3", "kpi_value": "2541"}, {"kpi_type": "Financial-CustomerPay", "kpi_type_code": "A", "kpi_name": "LaborGPPerRO", "kpi_no": "1", "kpi_slno": "4", "kpi_value": "1950"}, {"kpi_type": "Financial-CustomerPay", "kpi_type_code": "A", "kpi_name": "PartsSales", "kpi_no": "2", "kpi_slno": "1", "kpi_value": "1260715"}, {"kpi_type": "Financial-CustomerPay", "kpi_type_code": "A", "kpi_name": "PartsGrossProfit", "kpi_no": "2", "kpi_slno": "2", "kpi_value": "502159/39.8"}, {"kpi_type": "Financial-CustomerPay", "kpi_type_code": "A", "kpi_name": "PartsSalesPerRO", "kpi_no": "2", "kpi_slno": "3", "kpi_value": "3398"}, {"kpi_type": "Financial-CustomerPay", "kpi_type_code": "A", "kpi_name": "PartsGPPerRO", "kpi_no": "2", "kpi_slno": "4", "kpi_value": "1213"}, {"kpi_type": "Financial-CustomerPay", "kpi_type_code": "A", "kpi_name": "Labor+PartsSales", "kpi_no": "3", "kpi_slno": "1", "kpi_value": "2759451"}, {"kpi_type": "Financial-CustomerPay", "kpi_type_code": "A", "kpi_name": "Labor+PartsGrossProfit", "kpi_no": "3", "kpi_slno": "2", "kpi_value": "1630393"}, {"kpi_type": "Financial-CustomerPay", "kpi_type_code": "A", "kpi_name": "PartsToLaborRatio", "kpi_no": "3", "kpi_slno": "3", "kpi_value": "0.84/1.00"}, {"kpi_type": "Pricing-CustomerPay", "kpi_type_code": "B", "kpi_name": "RepairGridTargets/Misses/%ofNon-Compliance", "kpi_no": "1", "kpi_slno": "1", "kpi_value": null}, {"kpi_type": "Pricing-CustomerPay", "kpi_type_code": "B", "kpi_name": "CompetitiveHours/Sales/ELR", "kpi_no": "2", "kpi_slno": "1", "kpi_value": "2556/215425/84"}, {"kpi_type": "Pricing-CustomerPay", "kpi_type_code": "B", "kpi_name": "MaintenanceHours/Sales/ELR", "kpi_no": "2", "kpi_slno": "2", "kpi_value": "4289/333683/78"}, {"kpi_type": "Pricing-CustomerPay", "kpi_type_code": "B", "kpi_name": "RepairHours/Sales/ELR", "kpi_no": "2", "kpi_slno": "3", "kpi_value": "7354/949590/129"}, {"kpi_type": "Pricing-CustomerPay", "kpi_type_code": "B", "kpi_name": "“What-If”RepairELRNon-Complianceat0%", "kpi_no": "2", "kpi_slno": "4", "kpi_value": "0"}, {"kpi_type": "Pricing-CustomerPay", "kpi_type_code": "B", "kpi_name": "TotalHours/TotalSales/TotalELR", "kpi_no": "2", "kpi_slno": "5", "kpi_value": "14200/1498736/106"}, {"kpi_type": "Pricing-CustomerPay", "kpi_type_code": "B", "kpi_name": "“What-If”TotalELRifRepairNon-Complianceat0%", "kpi_no": "2", "kpi_slno": "6", "kpi_value": "0"}, {"kpi_type": "Pricing-CustomerPay", "kpi_type_code": "B", "kpi_name": "MaintenanceWork/RepairWorkMix", "kpi_no": "3", "kpi_slno": "1", "kpi_value": "29/71"}, {"kpi_type": "Pricing-CustomerPay", "kpi_type_code": "B", "kpi_name": "PartsPriceTargets/Misses/Non-Compliance%", "kpi_no": "4", "kpi_slno": "1", "kpi_value": null}, {"kpi_type": "Volume", "kpi_type_code": "C", "kpi_name": "Customer Pay ROs", "kpi_no": "1", "kpi_slno": "1", "kpi_value": "8502"}, {"kpi_type": "Volume", "kpi_type_code": "C", "kpi_name": "Warranty ROs", "kpi_no": "1", "kpi_slno": "2", "kpi_value": "5028"}, {"kpi_type": "Volume", "kpi_type_code": "C", "kpi_name": "Internal ROs", "kpi_no": "1", "kpi_slno": "3", "kpi_value": "7114"}, {"kpi_type": "Volume", "kpi_type_code": "C", "kpi_name": "All Unique ROs", "kpi_no": "1", "kpi_slno": "4", "kpi_value": "20644"}, {"kpi_type": "Volume", "kpi_type_code": "C", "kpi_name": "Average ROs Per Day", "kpi_no": "2", "kpi_slno": "1", "kpi_value": "115"}, {"kpi_type": "Volume", "kpi_type_code": "C", "kpi_name": "Representing What % of Total", "kpi_no": "2", "kpi_slno": "2", "kpi_value": "100"}, {"kpi_type": "Volume", "kpi_type_code": "C", "kpi_name": "Average Days RO’s are Open", "kpi_no": "2", "kpi_slno": "3", "kpi_value": "3"}, {"kpi_type": "Volume", "kpi_type_code": "C", "kpi_name": "Average Vehicle Age", "kpi_no": "3", "kpi_slno": "1", "kpi_value": "7"}, {"kpi_type": "Volume", "kpi_type_code": "C", "kpi_name": "Average Miles per Vehicle", "kpi_no": "3", "kpi_slno": "2", "kpi_value": "80001"}, {"kpi_type": "Volume", "kpi_type_code": "C", "kpi_name": "All SPerold Hours", "kpi_no": "3", "kpi_slno": "3", "kpi_value": "31512"}, {"kpi_type": "Volume", "kpi_type_code": "C", "kpi_name": "Average Hours Sold Day", "kpi_no": "3", "kpi_slno": "4", "kpi_value": "176"}, {"kpi_type": "Volume", "kpi_type_code": "C", "kpi_name": "Customer Pay Hours Per RO", "kpi_no": "3", "kpi_slno": "5", "kpi_value": "2"}, {"kpi_type": "Opportunities – CP Vehicles Under 60K Miles", "kpi_type_code": "D", "kpi_name": "Count of ROs Under 60k Miles", "kpi_no": "1", "kpi_slno": "1", "kpi_value": "2666/100"}, {"kpi_type": "Opportunities – CP Vehicles Under 60K Miles", "kpi_type_code": "D", "kpi_name": "Count of 1 Line ROs Under 60k Miles", "kpi_no": "1", "kpi_slno": "2", "kpi_value": "1558/58"}, {"kpi_type": "Opportunities – CP Vehicles Under 60K Miles", "kpi_type_code": "D", "kpi_name": "Labor Sold Per 1-<PERSON>em RO", "kpi_no": "2", "kpi_slno": "1", "kpi_value": "1875"}, {"kpi_type": "Opportunities – CP Vehicles Under 60K Miles", "kpi_type_code": "D", "kpi_name": "Parts Sold Per 1-Item RO", "kpi_no": "2", "kpi_slno": "2", "kpi_value": "1603"}, {"kpi_type": "Opportunities – CP Vehicles Under 60K Miles", "kpi_type_code": "D", "kpi_name": "Total Sold Per 1-Item RO", "kpi_no": "2", "kpi_slno": "3", "kpi_value": "209384"}, {"kpi_type": "Opportunities – CP Vehicles Under 60K Miles", "kpi_type_code": "D", "kpi_name": "Labor Sold Per Multi-Line RO", "kpi_no": "3", "kpi_slno": "1", "kpi_value": "4127"}, {"kpi_type": "Opportunities – CP Vehicles Under 60K Miles", "kpi_type_code": "D", "kpi_name": "Parts Sold Per Multi-Line RO", "kpi_no": "3", "kpi_slno": "2", "kpi_value": "3792"}, {"kpi_type": "Opportunities – CP Vehicles Under 60K Miles", "kpi_type_code": "D", "kpi_name": "Total Sold Per Multi-Line RO", "kpi_no": "3", "kpi_slno": "3", "kpi_value": "393575"}, {"kpi_type": "Opportunities – CP Vehicles Under 60K Miles", "kpi_type_code": "D", "kpi_name": "Average Jobs Per Multi-Line RO", "kpi_no": "3", "kpi_slno": "4", "kpi_value": "2"}, {"kpi_type": "Opportunities - MPI", "kpi_type_code": "E", "kpi_name": "MPI’s Completed", "kpi_no": "1", "kpi_slno": "1", "kpi_value": "0"}, {"kpi_type": "Opportunities - MPI", "kpi_type_code": "E", "kpi_name": "MPI % of Customer Pay and Warranty ROs", "kpi_no": "1", "kpi_slno": "2", "kpi_value": "0"}, {"kpi_type": "Opportunities - MPI", "kpi_type_code": "E", "kpi_name": "MPI Upsell Potential $", "kpi_no": "1", "kpi_slno": "3", "kpi_value": "0"}, {"kpi_type": "Opportunities - MPI", "kpi_type_code": "E", "kpi_name": "MPI Actual Sold $", "kpi_no": "1", "kpi_slno": "4", "kpi_value": "0"}, {"kpi_type": "Opportunities - MPI", "kpi_type_code": "E", "kpi_name": "MPI Total Hours Upsold", "kpi_no": "1", "kpi_slno": "5", "kpi_value": "0"}, {"kpi_type": "Opportunities - MPI", "kpi_type_code": "E", "kpi_name": "Hours Sold Per MPI Completed", "kpi_no": "1", "kpi_slno": "6", "kpi_value": "0"}, {"kpi_type": "Opportunities - Menu Sales", "kpi_type_code": "F", "kpi_name": "Menu Sales / % of CP and Wty ROs", "kpi_no": "1", "kpi_slno": "1", "kpi_value": "0/0"}, {"kpi_type": "Opportunities - Menu Sales", "kpi_type_code": "F", "kpi_name": "Menu Upsell Actual Sold $", "kpi_no": "1", "kpi_slno": "2", "kpi_value": "0"}, {"kpi_type": "Opportunities - Menu Sales", "kpi_type_code": "F", "kpi_name": "Menu Hours Upsold", "kpi_no": "1", "kpi_slno": "3", "kpi_value": "0"}, {"kpi_type": "Opportunities - Menu Sales", "kpi_type_code": "F", "kpi_name": "Hours Sold Per <PERSON>u", "kpi_no": "1", "kpi_slno": "4", "kpi_value": "0"}, {"kpi_type": "Opportunities – CP Vehicles Over 60K Miles", "kpi_type_code": "G", "kpi_name": "Count of ROs Over 60k Miles", "kpi_no": "1", "kpi_slno": "1", "kpi_value": "5484/100"}, {"kpi_type": "Opportunities – CP Vehicles Over 60K Miles", "kpi_type_code": "G", "kpi_name": "Count of 1 Line ROs Over 60k Miles", "kpi_no": "1", "kpi_slno": "2", "kpi_value": "2851/52"}, {"kpi_type": "Opportunities – CP Vehicles Over 60K Miles", "kpi_type_code": "G", "kpi_name": "Labor Sold Per 1-<PERSON>em RO", "kpi_no": "2", "kpi_slno": "1", "kpi_value": "369325"}, {"kpi_type": "Opportunities – CP Vehicles Over 60K Miles", "kpi_type_code": "G", "kpi_name": "Parts Sold Per 1-Item RO", "kpi_no": "2", "kpi_slno": "2", "kpi_value": "264447"}, {"kpi_type": "Opportunities – CP Vehicles Over 60K Miles", "kpi_type_code": "G", "kpi_name": "Total Sold Per 1-Item RO", "kpi_no": "2", "kpi_slno": "3", "kpi_value": "633772"}, {"kpi_type": "Opportunities – CP Vehicles Over 60K Miles", "kpi_type_code": "G", "kpi_name": "Labor Sold Per Multi-Line RO", "kpi_no": "3", "kpi_slno": "1", "kpi_value": "792866"}, {"kpi_type": "Opportunities – CP Vehicles Over 60K Miles", "kpi_type_code": "G", "kpi_name": "Parts Sold Per Multi-Line RO", "kpi_no": "3", "kpi_slno": "2", "kpi_value": "645482"}, {"kpi_type": "Opportunities – CP Vehicles Over 60K Miles", "kpi_type_code": "G", "kpi_name": "Total Sold Per Multi-Line RO", "kpi_no": "3", "kpi_slno": "3", "kpi_value": "1438348"}, {"kpi_type": "Opportunities – CP Vehicles Over 60K Miles", "kpi_type_code": "G", "kpi_name": "Average Jobs Per Multi-Line RO", "kpi_no": "3", "kpi_slno": "4", "kpi_value": "3"}]