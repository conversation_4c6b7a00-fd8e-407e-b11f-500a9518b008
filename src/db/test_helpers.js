import pg from 'pg';

const withDbFromUrl = async (url, fn) => {
  const client = new pg.Client(url);
  await client.connect();
  await client.query('begin;');
  try {
    await fn(client);
  } finally {
    await client.query('rollback;');
    await client.end();
  }
};

export const becomeUser = (client, userOrUserId = NULL) =>
  client.query(
    "select set_config('role', 'admin', true), set_config('jwt.claim.storeid', $1, true);",
    ['75627608']
  );

export const withRootDb = fn =>
  withDbFromUrl('**********************************************/demodb', fn);
const withAuthenticatorDb = fn =>
  withDbFromUrl('**********************************************/demodb', fn);
export const withAnonymousDb = fn =>
  withAuthenticatorDb(async client => {
    await becomeUser(client, null);
    return fn(client);
  });
export const withUserDb = fn =>
  withRootDb(async client => {
    const result = await client.query(
      'SELECT * FROM mindtree_utils.register_user_or_log_in($1, $2, $3, $4, $5)',
      [null, 'a@b.c', 'facebook', '123456', { firstName: 'A', lastName: 'B' }]
    );
    const user = result.rows[0];
    expect(user.id).not.toBeNull();
    await becomeUser(client, user);
    return fn(client, user);
  });
