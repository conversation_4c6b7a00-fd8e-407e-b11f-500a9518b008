
#report2-grid table {
  border-spacing: 0;
  margin-top: 10px;
  width: max-content;
  font-size: 12px;
  margin-bottom: 10px;
  color: #242f48;
  border: 1px solid #ccc;
  /* border-radius: 0.5rem; */
}
#report2-grid table tr:last-child td {
  border-bottom: 0;
}

#report2-grid table td {
  margin: 0;
  padding: 0.5rem;

  /* position: relative; */
}
.th-header {
  width: 110px !important
}
#report2-grid table th {
  margin: 0;
  padding: 0.5rem;
  width: 87px
  /* position: relative; */
}
#report2-grid th.short-td {
  padding: 0.5rem;
  width: 60px !important
  /* position: relative; */
}
#report2-grid th.long-td {
 
  width: 100px
  /* position: relative; */
}#report2-grid td.long-td {
 
  width: 170px
  /* position: relative; */
}
#report2-grid  .dot {
  height: 5px;
  width: 5px;
  background-color: #000;
  border-radius: 50%;
  display: inline-block;
  margin-bottom: 2.5px;
  margin-left: 5px;
  margin-right: 5px;
}
#report2-grid table th:last-child,
#report2-grid table td:last-child {
  border-right: 0;
}
#report2-grid table tr:nth-child(even)  {
  background-color: #e2e8f0 !important;
}

#report2-grid table th::before {
  position: absolute;
  right: 15px;
  top: 16px;
  content: "";
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
}
#report2-grid table th.sort-asc::before {
  border-bottom: 5px solid #22543d;
}
#report2-grid table th.sort-desc::before {
  border-top: 5px solid #22543d;
}
#report2-grid table tr:nth-of-type(odd) td.fix { background-color: #fff; }

/* …or this */
#report2-grid table tr:nth-of-type(even) td.fix { background-color: #e2e8f0; }
.badge {
  background-color: #9ae6b4;
  color: #22543d;
  margin-right: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}
#report2-grid input {
  padding: 10px;
  margin-bottom: 20px;
  font-size: 18px;
  border-radius: 5px;
  border: 1px solid #ddd;
  box-shadow: none;
}

/*Riju CSS*/
.main {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 25px 50px -12px rgba(0,0,0,.25);
  margin-bottom: 4rem;
  min-height: 500px;
}
.bg-texture-red{
  /* border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem; */
  height: 150px;
  
  padding: 25px;



padding: 25px;
background: linear-gradient(90deg, rgb(187 209 224) 0%, rgba(53,126,183,1) 10%, rgba(3,64,111,1) 50%, rgba(53,126,183,1) 80%, rgb(214 226 234) 100%);

box-shadow: none;

}
.maintitle{
  font-family: "Roboto", "Helvetica", "Arial", sans-serif;
  font-size: 20px;
  color: #fff;
  font-weight: 700;
  /* text-shadow: 0 4px 4px #ce6701; */
}
.title-description{
  font-family: "Roboto", "Helvetica", "Arial", sans-serif;
  font-size: 15px;
  color: #fff;
  font-weight: 500;
  /* text-shadow: 0 4px 4px #ce6701; */
  margin-top: 0;
}
.dropdown{
  /* float: right; */
  display: flex;
  justify-content: end;
gap: 10px;
}
.div-title{
  color: #fff;
 font-family: "Roboto", "Helvetica", "Arial", sans-serif;
 font-weight: bold;
 display: flex
}
.dropdown select{
  background-color: #fff;
  border: 0;
  height: 45px;
  min-width: 200px;
  padding: 5px 10px;
  border-radius: 0.5rem;
}
.data-container {
  margin-left: 2rem;
  margin-right: 2rem;
  margin-top: -2rem;
  padding-bottom: 3rem;
}
.data-header {
font-weight: 400;
letter-spacing: .025em;
text-transform: none;
-webkit-user-select: none;
-moz-user-select: none;
-ms-user-select: none;
user-select: none;
background-color: #226aa1;
z-index: 10;
color: #fff;
font-family: "Roboto", "Helvetica", "Arial", sans-serif;
}
/* .rounded-tl-lg {
border-top-left-radius: 0.5rem;
}
.rounded-tr-lg {
border-top-right-radius: 0.5rem;
} */
.tcollapse{
/* //border-collapse: collapse; */
font-family: "Roboto", "Helvetica", "Arial", sans-serif;
}
.kpi-table tr{
height: 40px;
}
.number-box{
background: #ee7600;
color: #fff;
border: 2px solid #1a5583;
padding: 5px;
margin-right: 10px;
border-radius: 3px;
}
.top-th{
border-bottom: 1px solid #003d6b;

}
.kpi-table tbody>tr{
border: 3px solid #f3f3f3;
border-top: 0;
}
.border-column{
  border-right: 2px solid #003d6b;
}
.left-align {
 text-align: left;
}
.td-header{
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #cccccc73;
}

#report2-grid .MuiSelect-select:hover, .MuiSelect-select:focus {
  background-color: transparent !important;
}
.top-th th{
  background: #ee7600;
}
.bottom-th{
  background: #293241 !important;
}
/* .bottom-th{
  background: none !important;
} */