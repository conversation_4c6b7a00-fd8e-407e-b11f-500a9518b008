{"name": "fixed-ops-command-centre", "author": "netspective", "email": "<EMAIL>", "licence": "UNLICENSED", "version": "1.0.0", "private": true, "scripts": {"start": "HOST=0.0.0.0 PORT=3000 NODE_OPTIONS=--openssl-legacy-provider react-scripts start", "build": "NODE_OPTIONS=--openssl-legacy-provider react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "ie 11", "not dead", "not op_mini all"], "development": ["ie 11", "last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@ag-grid-community/react": "^23.2.0", "@ag-grid-enterprise/all-modules": "23.2.0", "@apollo/client": "^3.3.3", "@apollo/react-hooks": "^3.1.3", "@babel/preset-env": "^7.12.9", "@babel/preset-react": "^7.12.5", "@cubejs-client/core": "^0.19.19", "@cubejs-client/react": "^0.19.19", "@cubejs-client/ws-transport": "^0.19.19", "@date-io/core": "^1.3.11", "@date-io/date-fns": "^1.3.11", "@date-io/moment": "^1.3.11", "@devexpress/dx-react-chart": "^2.7.6", "@devexpress/dx-react-chart-material-ui": "^2.7.6", "@fortawesome/fontawesome-svg-core": "^1.2.28", "@fortawesome/free-solid-svg-icons": "^5.13.0", "@fortawesome/react-fontawesome": "^0.1.9", "@fullcalendar/core": "^6.1.14", "@fullcalendar/daygrid": "^6.1.14", "@fullcalendar/react": "^6.1.14", "@material-ui/core": "^4.6.0", "@material-ui/icons": "^4.5.1", "@material-ui/lab": "^4.0.0-alpha.31", "@material-ui/pickers": "^3.3.10", "@material-ui/styles": "^4.6.0", "@opentelemetry/api": "^1.1.0", "@opentelemetry/exporter-collector": "^0.25.0", "@opentelemetry/instrumentation": "^0.27.0", "@opentelemetry/sdk-trace-web": "^1.1.1", "@progress/kendo-licensing": "^1.3.0", "@progress/kendo-react-dateinputs": "^5.11.0", "@progress/kendo-react-intl": "^5.11.0", "@react-keycloak/web": "^2.1.1", "@react-spectrum/datepicker": "^3.4.0", "@simonwep/pickr": "^1.8.2", "@tanstack/react-table": "^8.8.5", "@testing-library/react": "^11.2.7", "@wojtekmaj/react-daterange-picker": "^3.3.2", "ag-charts-community": "~1.0.0", "ag-charts-react": "~1.0.0", "ag-grid-community": "23.2.0", "ag-grid-enterprise": "^25.1.0", "ag-grid-react": "23.2.0", "apexcharts": "^3.17.1", "apollo-boost": "^0.4.7", "apollo-cache-inmemory": "^1.6.5", "apollo-client": "^2.6.8", "apollo-link-context": "^1.0.20", "apollo-link-schema": "^1.2.4", "axios": "^0.19.0", "axios-mock-adapter": "^1.17.0", "babel-jest": "^24.9.0", "babel-plugin-relay": "^10.0.1", "bootstrap": "^5.2.3", "bootstrap-daterangepicker": "^3.1.0", "chart.js": "^2.9.3", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.7.0", "chartjs-plugin-doughnutlabel": "^2.0.3", "chartjs-plugin-zoom": "^0.7.5", "clsx": "^1.0.4", "count-tabs": "^0.2.5", "date-fns": "^2.11.0", "dom-to-image": "^2.6.0", "draft-js": "^0.11.2", "enzyme-to-json": "^3.6.1", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "formik": "^2.4.6", "graphql": "^14.6.0", "graphql-tag": "^2.10.3", "graphql-tools": "^4.0.6", "hammerjs": "^2.0.8", "highcharts": "^8.0.4", "highcharts-grouped-categories": "^1.1.5", "highcharts-react-official": "^3.0.0", "history": "^4.10.1", "html2canvas": "^1.0.0-rc.5", "i": "^0.3.6", "immutable": "^4.0.0-rc.12", "jquery-sparkline": "^2.4.0", "js-base64": "^2.5.2", "js-cookie": "^2.2.1", "jsonwebtoken": "^8.5.1", "jspdf": "^2.5.1", "jspdf-autotable": "^5.0.2", "jss": "^10.0.0", "jss-rtl": "^0.3.0", "keycloak-js": "^10.0.0", "lodash.groupby": "^4.6.0", "lodash.times": "^4.3.2", "markdown-to-html-converter": "^1.0.4", "markdown-to-jsx": "^7.1.7", "material-ui-dropzone": "^3.5.0", "mocha": "^8.2.1", "moment": "^2.29.4", "pdfmake": "^0.1.72", "prismjs": "^1.30.0", "prop-types": "^15.8.1", "react": "^17.0.2", "react-apexcharts": "^1.3.6", "react-app-polyfill": "^1.0.4", "react-beautiful-dnd": "^11.0.5", "react-beforeunload": "^2.5.3", "react-bootstrap-daterangepicker": "^8.0.0", "react-bottom-scroll-listener": "^5.0.0", "react-chartjs-2": "^2.9.0", "react-client-session": "^0.0.8", "react-cookie": "^4.0.3", "react-d3-speedometer": "^1.0.2", "react-datepicker": "^4.10.0", "react-dom": "^17.0.1", "react-dom-factories": "^1.0.2", "react-dropzone": "^10.1.10", "react-error-boundary": "^3.1.4", "react-fast-marquee": "^1.1.1", "react-ga": "^2.7.0", "react-gauge-chart": "^0.5.1", "react-grid-layout": "^0.17.1", "react-gtm-module": "^2.0.11", "react-helmet": "^5.2.1", "react-html-parser": "^2.0.2", "react-idle-timer": "^5.6.0", "react-json-view": "^1.21.3", "react-markdown": "^4.2.2", "react-material-ui-carousel": "^2.2.2", "react-perfect-scrollbar": "^1.5.3", "react-phone-input-2": "^2.15.0", "react-phone-number-input": "^3.2.3", "react-redux": "^7.1.3", "react-relay": "^10.1.0", "react-render-html": "^0.6.0", "react-resizable": "^1.11.1", "react-router": "^5.1.2", "react-router-config": "^5.1.1", "react-router-dom": "^5.1.2", "react-screen-capture": "^1.0.1", "react-scripts": "^3.2.0", "react-scroll": "^1.8.5", "react-svg-gauge": "^1.0.10", "react-table": "^7.8.0", "react-timezone-select": "^1.3.2", "react-xml-viewer": "^1.3.0", "real-style-xlsx": "^1.0.4", "recharts": "1.8.5", "redux": "^4.0.4", "redux-devtools-extension": "^2.13.8", "redux-mock-store": "^1.5.4", "redux-thunk": "^2.3.0", "relay-test-utils": "^10.1.0", "sass": "^1.53.0", "select-timezone-material-ui": "^2.8.0", "sinon": "^9.2.1", "styled-components": "^5.3.3", "use-react-screenshot": "^3.0.0", "use-screenshot-hook": "^1.0.2", "uuid": "^3.3.3", "validate.js": "^0.12.0", "waait": "^1.0.5", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13", "yarn": "^1.22.10", "yup": "^1.4.0"}, "devDependencies": {"@fortawesome/fontawesome-free": "^5.13.0", "@testing-library/jest-dom": "^6.1.5", "dotenv": "^8.2.0", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.5", "eslint": "^6.6.0", "eslint-config-airbnb": "^18.0.1", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-react": "^7.16.0", "eslint-plugin-react-hooks": "^2.2.0", "jest": "^24.9.0", "jquery": "^3.6.3", "jsdom": "^16.4.0", "prettier": "^1.19.1", "react-test-renderer": "^17.0.1", "typescript": "^3.7.2"}}